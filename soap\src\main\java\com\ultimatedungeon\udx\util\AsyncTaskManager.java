package com.ultimatedungeon.udx.util;

import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;
import java.util.logging.Logger;

/**
 * Manages async operations and provides utilities for non-blocking task execution.
 * 
 * <p>This class handles file I/O, world generation, database operations, and other
 * potentially blocking operations to ensure they don't impact server performance.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class AsyncTaskManager {
    
    private final Plugin plugin;
    private final Logger logger;
    private final ConcurrentMap<String, BukkitTask> activeTasks;
    private final AtomicLong taskIdCounter;
    
    // Performance monitoring
    private final ConcurrentMap<String, Long> taskExecutionTimes;
    private final ConcurrentMap<String, Integer> taskExecutionCounts;
    
    public AsyncTaskManager(@NotNull Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.activeTasks = new ConcurrentHashMap<>();
        this.taskIdCounter = new AtomicLong(0);
        this.taskExecutionTimes = new ConcurrentHashMap<>();
        this.taskExecutionCounts = new ConcurrentHashMap<>();
    }
    
    /**
     * Executes a task asynchronously and returns a CompletableFuture.
     * 
     * @param taskName Name for tracking and debugging
     * @param task The task to execute
     * @param <T> Return type
     * @return CompletableFuture with the result
     */
    @NotNull
    public <T> CompletableFuture<T> executeAsync(@NotNull String taskName, @NotNull Supplier<T> task) {
        CompletableFuture<T> future = new CompletableFuture<>();
        String taskId = taskName + "_" + taskIdCounter.incrementAndGet();
        
        BukkitTask bukkitTask = new BukkitRunnable() {
            @Override
            public void run() {
                long startTime = System.nanoTime();
                
                try {
                    T result = task.get();
                    
                    // Schedule result delivery on main thread
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            future.complete(result);
                        }
                    }.runTask(plugin);
                    
                } catch (Exception e) {
                    logger.warning("Async task '" + taskName + "' failed: " + e.getMessage());
                    
                    // Schedule exception delivery on main thread
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            future.completeExceptionally(e);
                        }
                    }.runTask(plugin);
                    
                } finally {
                    // Record performance metrics
                    long executionTime = System.nanoTime() - startTime;
                    recordTaskExecution(taskName, executionTime);
                    
                    // Remove from active tasks
                    activeTasks.remove(taskId);
                }
            }
        }.runTaskAsynchronously(plugin);
        
        activeTasks.put(taskId, bukkitTask);
        return future;
    }
    
    /**
     * Executes a void task asynchronously.
     * 
     * @param taskName Name for tracking and debugging
     * @param task The task to execute
     * @return CompletableFuture<Void> for completion tracking
     */
    @NotNull
    public CompletableFuture<Void> executeAsyncVoid(@NotNull String taskName, @NotNull Runnable task) {
        return executeAsync(taskName, () -> {
            task.run();
            return null;
        });
    }
    
    /**
     * Executes a task with a delay asynchronously.
     * 
     * @param taskName Name for tracking and debugging
     * @param task The task to execute
     * @param delayTicks Delay in ticks before execution
     * @param <T> Return type
     * @return CompletableFuture with the result
     */
    @NotNull
    public <T> CompletableFuture<T> executeAsyncDelayed(@NotNull String taskName, @NotNull Supplier<T> task, long delayTicks) {
        CompletableFuture<T> future = new CompletableFuture<>();
        String taskId = taskName + "_delayed_" + taskIdCounter.incrementAndGet();
        
        BukkitTask bukkitTask = new BukkitRunnable() {
            @Override
            public void run() {
                // Execute the actual task async
                executeAsync(taskName, task).whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        future.completeExceptionally(throwable);
                    } else {
                        future.complete(result);
                    }
                });
                
                activeTasks.remove(taskId);
            }
        }.runTaskLaterAsynchronously(plugin, delayTicks);
        
        activeTasks.put(taskId, bukkitTask);
        return future;
    }
    
    /**
     * Executes a task on the main thread (sync).
     * 
     * @param taskName Name for tracking and debugging
     * @param task The task to execute
     * @param <T> Return type
     * @return CompletableFuture with the result
     */
    @NotNull
    public <T> CompletableFuture<T> executeSync(@NotNull String taskName, @NotNull Supplier<T> task) {
        CompletableFuture<T> future = new CompletableFuture<>();
        String taskId = taskName + "_sync_" + taskIdCounter.incrementAndGet();
        
        BukkitTask bukkitTask = new BukkitRunnable() {
            @Override
            public void run() {
                long startTime = System.nanoTime();
                
                try {
                    T result = task.get();
                    future.complete(result);
                } catch (Exception e) {
                    logger.warning("Sync task '" + taskName + "' failed: " + e.getMessage());
                    future.completeExceptionally(e);
                } finally {
                    // Record performance metrics
                    long executionTime = System.nanoTime() - startTime;
                    recordTaskExecution(taskName, executionTime);
                    
                    // Remove from active tasks
                    activeTasks.remove(taskId);
                }
            }
        }.runTask(plugin);
        
        activeTasks.put(taskId, bukkitTask);
        return future;
    }
    
    /**
     * Executes a void task on the main thread (sync).
     * 
     * @param taskName Name for tracking and debugging
     * @param task The task to execute
     * @return CompletableFuture<Void> for completion tracking
     */
    @NotNull
    public CompletableFuture<Void> executeSyncVoid(@NotNull String taskName, @NotNull Runnable task) {
        return executeSync(taskName, () -> {
            task.run();
            return null;
        });
    }
    
    /**
     * Records task execution metrics for performance monitoring.
     */
    private void recordTaskExecution(@NotNull String taskName, long executionTimeNanos) {
        taskExecutionTimes.merge(taskName, executionTimeNanos, Long::sum);
        taskExecutionCounts.merge(taskName, 1, Integer::sum);
    }
    
    /**
     * Gets the average execution time for a task type in milliseconds.
     */
    public double getAverageExecutionTime(@NotNull String taskName) {
        Long totalTime = taskExecutionTimes.get(taskName);
        Integer count = taskExecutionCounts.get(taskName);
        
        if (totalTime == null || count == null || count == 0) {
            return 0.0;
        }
        
        return (totalTime / 1_000_000.0) / count; // Convert to milliseconds
    }
    
    /**
     * Gets the total number of executions for a task type.
     */
    public int getExecutionCount(@NotNull String taskName) {
        return taskExecutionCounts.getOrDefault(taskName, 0);
    }
    
    /**
     * Gets the total execution time for a task type in milliseconds.
     */
    public double getTotalExecutionTime(@NotNull String taskName) {
        Long totalTime = taskExecutionTimes.get(taskName);
        return totalTime != null ? totalTime / 1_000_000.0 : 0.0;
    }
    
    /**
     * Gets all tracked task names.
     */
    @NotNull
    public java.util.Set<String> getTrackedTaskNames() {
        return taskExecutionCounts.keySet();
    }
    
    /**
     * Gets the number of currently active tasks.
     */
    public int getActiveTaskCount() {
        return activeTasks.size();
    }
    
    /**
     * Cancels a specific task by ID.
     */
    public boolean cancelTask(@NotNull String taskId) {
        BukkitTask task = activeTasks.remove(taskId);
        if (task != null) {
            task.cancel();
            return true;
        }
        return false;
    }
    
    /**
     * Cancels all active tasks.
     */
    public void cancelAllTasks() {
        for (BukkitTask task : activeTasks.values()) {
            try {
                task.cancel();
            } catch (Exception e) {
                logger.warning("Error cancelling task: " + e.getMessage());
            }
        }
        activeTasks.clear();
    }
    
    /**
     * Clears all performance metrics.
     */
    public void clearMetrics() {
        taskExecutionTimes.clear();
        taskExecutionCounts.clear();
    }
    
    /**
     * Shuts down the task manager and cancels all active tasks.
     */
    public void shutdown() {
        logger.info("Shutting down AsyncTaskManager...");
        cancelAllTasks();
        
        // Log final statistics
        logger.info("Task execution statistics:");
        for (String taskName : getTrackedTaskNames()) {
            logger.info(String.format("  %s: %d executions, %.2fms average, %.2fms total",
                taskName, getExecutionCount(taskName), 
                getAverageExecutionTime(taskName), getTotalExecutionTime(taskName)));
        }
        
        clearMetrics();
        logger.info("AsyncTaskManager shut down complete");
    }
}
