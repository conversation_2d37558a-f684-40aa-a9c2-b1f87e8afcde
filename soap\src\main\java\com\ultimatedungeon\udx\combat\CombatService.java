package com.ultimatedungeon.udx.combat;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for handling combat mechanics, damage calculation, and status effects.
 * 
 * <p>This service manages custom damage types, resistances, status effects,
 * and provides an API for other systems to interact with combat mechanics.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class CombatService implements Listener {
    
    private final UltimateDungeonX plugin;
    private final SchedulerUtil schedulerUtil;
    
    // Entity data storage
    private final Map<UUID, Map<String, Double>> entityResistances;
    private final Map<UUID, List<StatusEffect>> entityStatusEffects;
    private final Map<UUID, Map<String, Long>> entityCooldowns;
    
    // Status effect processing
    private BukkitTask statusEffectTask;
    
    public CombatService(@NotNull UltimateDungeonX plugin, @NotNull SchedulerUtil schedulerUtil) {
        this.plugin = plugin;
        this.schedulerUtil = schedulerUtil;
        
        this.entityResistances = new ConcurrentHashMap<>();
        this.entityStatusEffects = new ConcurrentHashMap<>();
        this.entityCooldowns = new ConcurrentHashMap<>();
        
        startStatusEffectProcessor();
    }
    
    /**
     * Starts the status effect processor task.
     */
    private void startStatusEffectProcessor() {
        statusEffectTask = schedulerUtil.runTaskTimer(() -> {
            processStatusEffects();
        }, 20L, 20L); // Process every second
    }
    
    /**
     * Processes all active status effects.
     */
    private void processStatusEffects() {
        for (Map.Entry<UUID, List<StatusEffect>> entry : entityStatusEffects.entrySet()) {
            UUID entityId = entry.getKey();
            List<StatusEffect> effects = entry.getValue();
            
            LivingEntity entity = getEntityById(entityId);
            if (entity == null || entity.isDead()) {
                effects.clear();
                continue;
            }
            
            // Process each effect
            Iterator<StatusEffect> iterator = effects.iterator();
            while (iterator.hasNext()) {
                StatusEffect effect = iterator.next();
                
                // Tick the effect
                if (!tickStatusEffect(entity, effect)) {
                    iterator.remove();
                    removeStatusEffectFromEntity(entity, effect);
                } else {
                    // Update the effect with reduced duration
                    StatusEffect updatedEffect = effect.withReducedDuration(20); // 1 second = 20 ticks
                    if (updatedEffect.getRemainingDuration() <= 0) {
                        iterator.remove();
                        removeStatusEffectFromEntity(entity, effect);
                    } else {
                        // Replace with updated effect
                        int index = effects.indexOf(effect);
                        if (index >= 0) {
                            effects.set(index, updatedEffect);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Ticks a status effect on an entity.
     * 
     * @param entity the entity with the effect
     * @param effect the status effect to tick
     * @return true if the effect should continue, false if it should be removed
     */
    private boolean tickStatusEffect(@NotNull LivingEntity entity, @NotNull StatusEffect effect) {
        switch (effect.id()) {
            case "poison" -> {
                double damage = effect.getParameter("damage", 2.0);
                dealDamage(entity, null, damage, DamageType.POISON, "Poison");
                return true;
            }
            case "regeneration" -> {
                double healing = effect.getParameter("healing", 2.0);
                healEntity(entity, healing);
                return true;
            }
            case "burn" -> {
                double damage = effect.getParameter("damage", 3.0);
                dealDamage(entity, null, damage, DamageType.FIRE, "Burn");
                entity.setFireTicks(40); // Keep entity on fire
                return true;
            }
            case "freeze" -> {
                // Slow movement and prevent actions
                applyMovementReduction(entity, 0.5);
                return true;
            }
            case "stun" -> {
                // Prevent all actions
                preventActions(entity);
                return true;
            }
            case "strength" -> {
                // Increase damage (handled in damage calculation)
                return true;
            }
            case "weakness" -> {
                // Decrease damage (handled in damage calculation)
                return true;
            }
            case "resistance" -> {
                // Increase damage resistance (handled in damage calculation)
                return true;
            }
            case "vulnerability" -> {
                // Decrease damage resistance (handled in damage calculation)
                return true;
            }
        }
        
        return effect.getRemainingDuration() > 0;
    }
    
    /**
     * Deals custom damage to an entity.
     * 
     * @param target the target entity
     * @param source the source entity (can be null)
     * @param baseDamage the base damage amount
     * @param damageType the type of damage
     * @param source the damage source description
     */
    public void dealDamage(@NotNull LivingEntity target, @Nullable LivingEntity source, 
                          double baseDamage, @NotNull DamageType damageType, @NotNull String sourceName) {
        
        // Calculate final damage with resistances and status effects
        double finalDamage = calculateDamage(target, source, baseDamage, damageType);
        
        // Apply damage
        if (finalDamage > 0) {
            if (source != null) {
                target.damage(finalDamage, source);
            } else {
                target.damage(finalDamage);
            }
            
            // Show damage indicator
            showDamageIndicator(target, finalDamage, damageType);
        }
    }
    
    /**
     * Calculates final damage after resistances and modifiers.
     * 
     * @param target the target entity
     * @param source the source entity (can be null)
     * @param baseDamage the base damage amount
     * @param damageType the type of damage
     * @return the final damage amount
     */
    public double calculateDamage(@NotNull LivingEntity target, @Nullable LivingEntity source, 
                                 double baseDamage, @NotNull DamageType damageType) {
        
        double damage = baseDamage;
        
        // Apply source modifiers (strength, weakness, etc.)
        if (source != null) {
            damage = applySourceModifiers(source, damage, damageType);
        }
        
        // Apply target resistances
        damage = applyResistances(target, damage, damageType);
        
        // Apply target status effect modifiers
        damage = applyStatusEffectModifiers(target, damage, damageType);
        
        // True damage ignores all resistances
        if (damageType == DamageType.TRUE) {
            return baseDamage;
        }
        
        return Math.max(0, damage);
    }
    
    /**
     * Applies source entity modifiers to damage.
     */
    private double applySourceModifiers(@NotNull LivingEntity source, double damage, @NotNull DamageType damageType) {
        List<StatusEffect> effects = getStatusEffects(source);
        
        for (StatusEffect effect : effects) {
            switch (effect.id()) {
                case "strength" -> damage *= (1.0 + (effect.amplifier() * 0.2));
                case "weakness" -> damage *= (1.0 - (effect.amplifier() * 0.2));
            }
        }
        
        return damage;
    }
    
    /**
     * Applies target resistances to damage.
     */
    private double applyResistances(@NotNull LivingEntity target, double damage, @NotNull DamageType damageType) {
        Map<String, Double> resistances = entityResistances.get(target.getUniqueId());
        if (resistances == null) {
            return damage;
        }
        
        Double resistance = resistances.get(damageType.name());
        if (resistance != null) {
            damage *= (1.0 - resistance);
        }
        
        return damage;
    }
    
    /**
     * Applies status effect modifiers to damage.
     */
    private double applyStatusEffectModifiers(@NotNull LivingEntity target, double damage, @NotNull DamageType damageType) {
        List<StatusEffect> effects = getStatusEffects(target);
        
        for (StatusEffect effect : effects) {
            switch (effect.id()) {
                case "resistance" -> damage *= (1.0 - (effect.amplifier() * 0.1));
                case "vulnerability" -> damage *= (1.0 + (effect.amplifier() * 0.1));
            }
        }
        
        return damage;
    }
    
    /**
     * Heals an entity.
     * 
     * @param entity the entity to heal
     * @param amount the amount to heal
     */
    public void healEntity(@NotNull LivingEntity entity, double amount) {
        double currentHealth = entity.getHealth();
        double maxHealth = entity.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
        double newHealth = Math.min(currentHealth + amount, maxHealth);
        
        entity.setHealth(newHealth);
        
        // Show healing indicator
        showHealingIndicator(entity, amount);
    }
    
    /**
     * Applies a status effect to an entity.
     * 
     * @param entity the target entity
     * @param effect the status effect to apply
     */
    public void applyStatusEffect(@NotNull LivingEntity entity, @NotNull StatusEffect effect) {
        UUID entityId = entity.getUniqueId();
        List<StatusEffect> effects = entityStatusEffects.computeIfAbsent(entityId, k -> new ArrayList<>());
        
        // Check for existing effects of the same type
        StatusEffect existing = findStatusEffect(entity, effect.id());
        if (existing != null) {
            // Handle stacking or replacement
            if (effect.canStackWith(existing)) {
                effects.add(effect);
            } else {
                // Replace with stronger effect or refresh duration
                if (effect.amplifier() > existing.amplifier() || effect.duration() > existing.getRemainingDuration()) {
                    effects.remove(existing);
                    effects.add(effect);
                }
            }
        } else {
            effects.add(effect);
        }
        
        // Apply immediate effect
        applyStatusEffectToEntity(entity, effect);
    }
    
    /**
     * Removes a status effect from an entity.
     * 
     * @param entity the target entity
     * @param effectId the ID of the effect to remove
     */
    public void removeStatusEffect(@NotNull LivingEntity entity, @NotNull String effectId) {
        List<StatusEffect> effects = entityStatusEffects.get(entity.getUniqueId());
        if (effects == null) {
            return;
        }
        
        effects.removeIf(effect -> {
            if (effect.id().equals(effectId)) {
                removeStatusEffectFromEntity(entity, effect);
                return true;
            }
            return false;
        });
    }
    
    /**
     * Gets all status effects on an entity.
     * 
     * @param entity the entity
     * @return list of status effects
     */
    @NotNull
    public List<StatusEffect> getStatusEffects(@NotNull LivingEntity entity) {
        return entityStatusEffects.getOrDefault(entity.getUniqueId(), new ArrayList<>());
    }
    
    /**
     * Finds a specific status effect on an entity.
     * 
     * @param entity the entity
     * @param effectId the effect ID to find
     * @return the status effect, or null if not found
     */
    @Nullable
    public StatusEffect findStatusEffect(@NotNull LivingEntity entity, @NotNull String effectId) {
        List<StatusEffect> effects = getStatusEffects(entity);
        return effects.stream()
            .filter(effect -> effect.id().equals(effectId))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Sets resistance for an entity to a specific damage type.
     * 
     * @param entity the entity
     * @param damageType the damage type
     * @param resistance the resistance value (0.0 = no resistance, 1.0 = immunity)
     */
    public void setResistance(@NotNull LivingEntity entity, @NotNull DamageType damageType, double resistance) {
        UUID entityId = entity.getUniqueId();
        Map<String, Double> resistances = entityResistances.computeIfAbsent(entityId, k -> new HashMap<>());
        resistances.put(damageType.name(), Math.max(0.0, Math.min(1.0, resistance)));
    }
    
    /**
     * Gets resistance for an entity to a specific damage type.
     * 
     * @param entity the entity
     * @param damageType the damage type
     * @return the resistance value
     */
    public double getResistance(@NotNull LivingEntity entity, @NotNull DamageType damageType) {
        Map<String, Double> resistances = entityResistances.get(entity.getUniqueId());
        if (resistances == null) {
            return 0.0;
        }
        return resistances.getOrDefault(damageType.name(), 0.0);
    }
    
    /**
     * Checks if an entity is on cooldown for a specific action.
     * 
     * @param entity the entity
     * @param action the action name
     * @return true if on cooldown, false otherwise
     */
    public boolean isOnCooldown(@NotNull LivingEntity entity, @NotNull String action) {
        Map<String, Long> cooldowns = entityCooldowns.get(entity.getUniqueId());
        if (cooldowns == null) {
            return false;
        }
        
        Long cooldownEnd = cooldowns.get(action);
        return cooldownEnd != null && System.currentTimeMillis() < cooldownEnd;
    }
    
    /**
     * Sets a cooldown for an entity action.
     * 
     * @param entity the entity
     * @param action the action name
     * @param durationMs the cooldown duration in milliseconds
     */
    public void setCooldown(@NotNull LivingEntity entity, @NotNull String action, long durationMs) {
        UUID entityId = entity.getUniqueId();
        Map<String, Long> cooldowns = entityCooldowns.computeIfAbsent(entityId, k -> new HashMap<>());
        cooldowns.put(action, System.currentTimeMillis() + durationMs);
    }
    
    /**
     * Clears all data for an entity (called when entity dies or leaves).
     * 
     * @param entity the entity
     */
    public void clearEntityData(@NotNull LivingEntity entity) {
        UUID entityId = entity.getUniqueId();
        entityResistances.remove(entityId);
        entityStatusEffects.remove(entityId);
        entityCooldowns.remove(entityId);
    }
    
    // Helper methods
    
    private void applyStatusEffectToEntity(@NotNull LivingEntity entity, @NotNull StatusEffect effect) {
        // Apply immediate effects based on effect type
        switch (effect.id()) {
            case "speed" -> applyMovementModifier(entity, 1.0 + (effect.amplifier() * 0.2));
            case "slowness" -> applyMovementModifier(entity, 1.0 - (effect.amplifier() * 0.2));
            case "jump_boost" -> applyJumpModifier(entity, 1.0 + (effect.amplifier() * 0.5));
        }
    }
    
    private void removeStatusEffectFromEntity(@NotNull LivingEntity entity, @NotNull StatusEffect effect) {
        // Remove effect-specific modifications
        switch (effect.id()) {
            case "speed", "slowness" -> resetMovementModifier(entity);
            case "jump_boost" -> resetJumpModifier(entity);
            case "freeze" -> resetMovementModifier(entity);
        }
    }
    
    private void applyMovementModifier(@NotNull LivingEntity entity, double multiplier) {
        // Implementation would modify entity movement speed
    }
    
    private void applyMovementReduction(@NotNull LivingEntity entity, double reduction) {
        // Implementation would reduce entity movement speed
    }
    
    private void resetMovementModifier(@NotNull LivingEntity entity) {
        // Implementation would reset entity movement speed to default
    }
    
    private void applyJumpModifier(@NotNull LivingEntity entity, double multiplier) {
        // Implementation would modify entity jump height
    }
    
    private void resetJumpModifier(@NotNull LivingEntity entity) {
        // Implementation would reset entity jump height to default
    }
    
    private void preventActions(@NotNull LivingEntity entity) {
        // Implementation would prevent entity from performing actions
    }
    
    private void showDamageIndicator(@NotNull LivingEntity entity, double damage, @NotNull DamageType damageType) {
        // Show floating damage text or particles
        if (entity instanceof Player player) {
            player.sendActionBar(damageType.getColorCode() + "-" + Math.round(damage));
        }
    }
    
    private void showHealingIndicator(@NotNull LivingEntity entity, double healing) {
        // Show floating healing text or particles
        if (entity instanceof Player player) {
            player.sendActionBar("§a+" + Math.round(healing));
        }
    }
    
    @Nullable
    private LivingEntity getEntityById(@NotNull UUID entityId) {
        // Try to find the entity in the world
        return Bukkit.getWorlds().stream()
            .flatMap(world -> world.getEntities().stream())
            .filter(entity -> entity instanceof LivingEntity)
            .map(entity -> (LivingEntity) entity)
            .filter(entity -> entity.getUniqueId().equals(entityId))
            .findFirst()
            .orElse(null);
    }
    
    // Event handlers
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamage(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof LivingEntity target)) {
            return;
        }
        
        // Handle custom damage types and resistances
        // This would integrate with the existing damage system
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (!(event.getEntity() instanceof LivingEntity target)) {
            return;
        }
        
        if (!(event.getDamager() instanceof LivingEntity source)) {
            return;
        }
        
        // Apply custom damage calculations
        // This would modify the damage based on our custom system
    }
    
    /**
     * Shuts down the combat service.
     */
    public void shutdown() {
        if (statusEffectTask != null) {
            statusEffectTask.cancel();
        }
        
        entityResistances.clear();
        entityStatusEffects.clear();
        entityCooldowns.clear();
    }
}
