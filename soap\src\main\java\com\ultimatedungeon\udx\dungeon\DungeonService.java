package com.ultimatedungeon.udx.dungeon;

import com.ultimatedungeon.udx.config.ConfigService;
import com.ultimatedungeon.udx.gen.GenerationService;
import com.ultimatedungeon.udx.instance.InstanceManager;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Service for managing dungeon definitions and configurations.
 * 
 * <p>This service handles dungeon metadata, difficulty scaling,
 * and integration with the generation and instance systems.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class DungeonService {
    
    private final Plugin plugin;
    private final ConfigService configService;
    private final InstanceManager instanceManager;
    private final GenerationService generationService;
    
    // Custom dungeons registry
    private final Map<String, String> customDungeons = new HashMap<>(); // name -> worldName
    
    public DungeonService(@NotNull Plugin plugin, @NotNull ConfigService configService, 
                         @NotNull InstanceManager instanceManager, @NotNull GenerationService generationService) {
        this.plugin = plugin;
        this.configService = configService;
        this.instanceManager = instanceManager;
        this.generationService = generationService;
        
        // Load existing custom dungeons from config
        loadCustomDungeons();
    }
    
    /**
     * Loads custom dungeons from configuration.
     */
    private void loadCustomDungeons() {
        File configFile = new File(plugin.getDataFolder(), "custom-dungeons.yml");
        if (!configFile.exists()) {
            customDungeons.clear();
            plugin.getLogger().info("No custom dungeons configuration found, starting fresh");
            return;
        }

        try {
            org.bukkit.configuration.file.YamlConfiguration config =
                org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(configFile);

            customDungeons.clear();

            if (config.contains("dungeons")) {
                org.bukkit.configuration.ConfigurationSection dungeonSection = config.getConfigurationSection("dungeons");
                if (dungeonSection != null) {
                    for (String dungeonName : dungeonSection.getKeys(false)) {
                        String worldName = dungeonSection.getString(dungeonName + ".world");
                        if (worldName != null) {
                            customDungeons.put(dungeonName, worldName);
                        }
                    }
                }
            }

            plugin.getLogger().info("Loaded " + customDungeons.size() + " custom dungeons");
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to load custom dungeons: " + e.getMessage());
            customDungeons.clear();
        }
    }
    
    /**
     * Registers a new custom dungeon.
     * 
     * @param dungeonName The display name of the dungeon
     * @param worldName The world name for the dungeon
     */
    public void registerCustomDungeon(@NotNull String dungeonName, @NotNull String worldName) {
        customDungeons.put(dungeonName, worldName);
        saveCustomDungeons();
        plugin.getLogger().info("Registered custom dungeon: " + dungeonName + " -> " + worldName);
    }
    
    /**
     * Gets the world name for a custom dungeon.
     * 
     * @param dungeonName The dungeon name
     * @return The world name, or null if not found
     */
    public String getCustomDungeonWorld(@NotNull String dungeonName) {
        return customDungeons.get(dungeonName);
    }
    
    /**
     * Gets all custom dungeon names.
     * 
     * @return Set of custom dungeon names
     */
    public Set<String> getCustomDungeonNames() {
        return customDungeons.keySet();
    }
    
    /**
     * Checks if a custom dungeon exists.
     * 
     * @param dungeonName The dungeon name
     * @return True if the dungeon exists
     */
    public boolean hasCustomDungeon(@NotNull String dungeonName) {
        return customDungeons.containsKey(dungeonName);
    }
    
    /**
     * Saves custom dungeons to configuration.
     */
    private void saveCustomDungeons() {
        try {
            File configFile = new File(plugin.getDataFolder(), "custom-dungeons.yml");

            // Ensure data folder exists
            if (!plugin.getDataFolder().exists()) {
                plugin.getDataFolder().mkdirs();
            }

            org.bukkit.configuration.file.YamlConfiguration config =
                new org.bukkit.configuration.file.YamlConfiguration();

            // Save dungeons
            for (Map.Entry<String, String> entry : customDungeons.entrySet()) {
                config.set("dungeons." + entry.getKey() + ".world", entry.getValue());
            }

            config.save(configFile);
            plugin.getLogger().fine("Saved custom dungeons configuration");
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save custom dungeons: " + e.getMessage());
        }
    }
    
    /**
     * Loads sample dungeons for first-time setup.
     */
    public void loadSampleDungeons() {
        // TODO: Implement sample dungeon loading
        // - Create sample dungeon configurations
        // - Load sample room templates
        // - Set up sample loot tables
        plugin.getLogger().info("Loading sample dungeons...");
        
        // For now, just log that we would load them
        plugin.getLogger().info("Sample dungeons loaded: Crypt of Echoes, Ember Foundry, Skyfane Spire");
    }
}
