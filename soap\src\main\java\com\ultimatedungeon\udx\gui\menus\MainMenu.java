package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.gui.MenuRegistry;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * Main menu for players - the primary entry point to UltimateDungeonX.
 * 
 * <p>This menu provides access to all player-facing features including
 * dungeon browsing, party management, achievements, and settings.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class MainMenu extends Menu {
    
    public MainMenu(@NotNull Player player, @NotNull MenuRegistry menuRegistry) {
        super(player, 
              Component.text("UltimateDungeonX").color(NamedTextColor.GOLD).decorate(TextDecoration.BOLD), 
              45, 
              menuRegistry);
    }
    
    @Override
    protected void setupMenu() {
        // Header decoration
        setupHeader();
        
        // Main action buttons
        setupMainButtons();
        
        // Secondary buttons
        setupSecondaryButtons();
        
        // Admin button (if player has permission)
        setupAdminButton();
        
        // Fill empty slots
        fillEmptySlots();
    }
    
    /**
     * Sets up the header decoration.
     */
    private void setupHeader() {
        // Title item
        ItemStack titleItem = createItem(
            Material.NETHER_STAR,
            Component.text("UltimateDungeonX").color(NamedTextColor.GOLD).decorate(TextDecoration.BOLD),
            createLore(
                "Welcome to UltimateDungeonX!",
                "",
                "The ultimate dungeon experience",
                "with no dependencies required."
            )
        );
        setItem(4, titleItem);
        
        // Decorative items
        ItemStack decoration = createItem(Material.PURPLE_STAINED_GLASS_PANE, Component.empty());
        for (int i = 0; i < 9; i++) {
            if (i != 4) {
                setItem(i, decoration);
            }
        }
    }
    
    /**
     * Sets up the main action buttons.
     */
    private void setupMainButtons() {
        // Play Dungeons button
        ItemStack playButton = createItem(
            Material.DIAMOND_SWORD,
            Component.text("⚔ Play Dungeons").color(NamedTextColor.GREEN).decorate(TextDecoration.BOLD),
            createLore(
                "Browse and join dungeon runs",
                "",
                "• Solo or party play",
                "• Multiple difficulties",
                "• Epic loot and rewards",
                "",
                "Click to browse dungeons!"
            )
        );
        setItem(20, playButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openDungeonBrowser(player);
            }
        });
        
        // Party Management button
        ItemStack partyButton = createItem(
            Material.PLAYER_HEAD,
            Component.text("👥 Party").color(NamedTextColor.BLUE).decorate(TextDecoration.BOLD),
            createLore(
                "Manage your party",
                "",
                "• Create or join parties",
                "• Invite friends",
                "• Ready up for dungeons",
                "",
                "Click to manage party!"
            )
        );
        setItem(22, partyButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openPartyMenu(player);
            }
        });
        
        // Achievements button
        ItemStack achievementsButton = createItem(
            Material.GOLDEN_APPLE,
            Component.text("🏆 Achievements").color(NamedTextColor.YELLOW).decorate(TextDecoration.BOLD),
            createLore(
                "View your achievements",
                "",
                "• Track your progress",
                "• Unlock rewards",
                "• Show off your skills",
                "",
                "Click to view achievements!"
            )
        );
        setItem(24, achievementsButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openAchievementsMenu(player);
            }
        });
    }
    
    /**
     * Sets up secondary action buttons.
     */
    private void setupSecondaryButtons() {
        // Leaderboards button
        ItemStack leaderboardsButton = createItem(
            Material.LECTERN,
            Component.text("📊 Leaderboards").color(NamedTextColor.AQUA),
            createLore(
                "View global rankings",
                "",
                "• Best completion times",
                "• Seasonal rankings",
                "• Compare with friends",
                "",
                "Click to view leaderboards!"
            )
        );
        setItem(30, leaderboardsButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openLeaderboardsMenu(player);
            }
        });
        
        // Settings button
        ItemStack settingsButton = createItem(
            Material.REDSTONE,
            Component.text("⚙ Settings").color(NamedTextColor.GRAY),
            createLore(
                "Customize your experience",
                "",
                "• Sound and visual settings",
                "• UI preferences",
                "• Notification options",
                "",
                "Click to open settings!"
            )
        );
        setItem(32, settingsButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openSettingsMenu(player);
            }
        });
    }
    
    /**
     * Sets up the admin button if player has permission.
     */
    private void setupAdminButton() {
        if (player.hasPermission("udx.admin")) {
            ItemStack adminButton = createItem(
                Material.COMMAND_BLOCK,
                Component.text("⚡ Admin Hub").color(NamedTextColor.RED).decorate(TextDecoration.BOLD),
                createLore(
                    "Administrative tools",
                    "",
                    "• Manage dungeons",
                    "• Monitor instances",
                    "• Configure settings",
                    "",
                    "Click to open admin hub!"
                )
            );
            setItem(40, adminButton, clickType -> {
                if (clickType == ClickType.LEFT) {
                    menuRegistry.openAdminHub(player);
                }
            });
        }
    }
    
    @Override
    protected void onOpen() {
        super.onOpen();
        
        // Send welcome message for new players
        // TODO: Check if player is new and send welcome message
        
        // Update last seen timestamp
        // TODO: Update player profile last seen
    }
}
