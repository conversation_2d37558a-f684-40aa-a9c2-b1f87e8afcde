package com.ultimatedungeon.udx.boss.ability;

import com.ultimatedungeon.udx.boss.BossInstance;
import com.ultimatedungeon.udx.boss.BossDef.BossAbility;
import com.ultimatedungeon.udx.boss.BossDef.Telegraph;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.title.Title;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Abstract base class for boss ability executors.
 * 
 * <p>Provides the framework for executing boss abilities with telegraph
 * and execution phases. Handles visual and audio feedback for players.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public abstract class BossAbilityExecutor {
    
    protected final BossInstance bossInstance;
    protected final BossAbility ability;
    
    public BossAbilityExecutor(@NotNull BossInstance bossInstance, @NotNull BossAbility ability) {
        this.bossInstance = bossInstance;
        this.ability = ability;
    }
    
    /**
     * Executes the ability with telegraph and execution phases.
     */
    @NotNull
    public CompletableFuture<Void> execute() {
        CompletableFuture<Void> future = new CompletableFuture<>();
        
        // Add to active abilities
        bossInstance.getActiveAbilities().add(ability.id());
        
        // Execute telegraph phase if present
        if (ability.telegraph() != null) {
            executeTelegraph().thenCompose(v -> executeAbility())
                .whenComplete((result, throwable) -> {
                    // Remove from active abilities
                    bossInstance.getActiveAbilities().remove(ability.id());
                    
                    if (throwable != null) {
                        future.completeExceptionally(throwable);
                    } else {
                        future.complete(null);
                    }
                });
        } else {
            // Execute ability directly
            executeAbility().whenComplete((result, throwable) -> {
                // Remove from active abilities
                bossInstance.getActiveAbilities().remove(ability.id());
                
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    future.complete(null);
                }
            });
        }
        
        return future;
    }
    
    /**
     * Executes the telegraph phase.
     */
    @NotNull
    protected CompletableFuture<Void> executeTelegraph() {
        CompletableFuture<Void> future = new CompletableFuture<>();
        Telegraph telegraph = ability.telegraph();
        
        if (telegraph == null) {
            future.complete(null);
            return future;
        }
        
        // Show telegraph visuals
        showTelegraphVisuals(telegraph);
        
        // Play telegraph sound
        if (telegraph.sound() != null && !telegraph.sound().isEmpty()) {
            playSound(telegraph.sound());
        }
        
        // Show telegraph message
        if (telegraph.message() != null && !telegraph.message().isEmpty()) {
            showTelegraphMessage(telegraph.message());
        }
        
        // Schedule completion after telegraph duration
        bossInstance.getPlugin().getServer().getScheduler().runTaskLater(
            bossInstance.getPlugin(),
            () -> future.complete(null),
            telegraph.duration() / 50L // Convert ms to ticks
        );
        
        return future;
    }
    
    /**
     * Shows telegraph visual effects.
     */
    protected void showTelegraphVisuals(@NotNull Telegraph telegraph) {
        Location bossLocation = bossInstance.getEntity().getLocation();
        Map<String, Object> params = telegraph.visual();
        
        switch (telegraph.type().toLowerCase()) {
            case "circle" -> showCircleTelegraph(bossLocation, params);
            case "cone" -> showConeTelegraph(bossLocation, params);
            case "line" -> showLineTelegraph(bossLocation, params);
            case "area" -> showAreaTelegraph(bossLocation, params);
        }
    }
    
    /**
     * Shows a circular telegraph.
     */
    protected void showCircleTelegraph(@NotNull Location center, @NotNull Map<String, Object> params) {
        double radius = ((Number) params.getOrDefault("radius", 5.0)).doubleValue();
        
        // Create circle of particles
        for (int i = 0; i < 360; i += 10) {
            double angle = Math.toRadians(i);
            double x = center.getX() + radius * Math.cos(angle);
            double z = center.getZ() + radius * Math.sin(angle);
            Location particleLocation = new Location(center.getWorld(), x, center.getY(), z);
            
            center.getWorld().spawnParticle(
                Particle.DUST,
                particleLocation,
                1,
                new Particle.DustOptions(org.bukkit.Color.RED, 1.0f)
            );
        }
    }
    
    /**
     * Shows a cone telegraph.
     */
    protected void showConeTelegraph(@NotNull Location center, @NotNull Map<String, Object> params) {
        double range = ((Number) params.getOrDefault("range", 10.0)).doubleValue();
        double angle = Math.toRadians(((Number) params.getOrDefault("angle", 45.0)).doubleValue());
        
        org.bukkit.util.Vector direction = bossInstance.getEntity().getLocation().getDirection();
        
        // Create cone of particles
        for (double r = 1; r <= range; r += 0.5) {
            for (double a = -angle / 2; a <= angle / 2; a += Math.toRadians(5)) {
                org.bukkit.util.Vector particleDir = direction.clone().rotateAroundY(a);
                Location particleLocation = center.clone().add(particleDir.multiply(r));
                
                center.getWorld().spawnParticle(
                    Particle.DUST,
                    particleLocation,
                    1,
                    new Particle.DustOptions(org.bukkit.Color.RED, 1.0f)
                );
            }
        }
    }
    
    /**
     * Shows a line telegraph.
     */
    protected void showLineTelegraph(@NotNull Location start, @NotNull Map<String, Object> params) {
        double length = ((Number) params.getOrDefault("length", 10.0)).doubleValue();
        
        org.bukkit.util.Vector direction = bossInstance.getEntity().getLocation().getDirection();
        
        // Create line of particles
        for (double d = 0; d <= length; d += 0.5) {
            Location particleLocation = start.clone().add(direction.clone().multiply(d));
            
            start.getWorld().spawnParticle(
                Particle.DUST,
                particleLocation,
                1,
                new Particle.DustOptions(org.bukkit.Color.RED, 1.0f)
            );
        }
    }
    
    /**
     * Shows an area telegraph.
     */
    protected void showAreaTelegraph(@NotNull Location center, @NotNull Map<String, Object> params) {
        double width = ((Number) params.getOrDefault("width", 10.0)).doubleValue();
        double height = ((Number) params.getOrDefault("height", 10.0)).doubleValue();
        
        // Create rectangular area of particles
        for (double x = -width / 2; x <= width / 2; x += 1.0) {
            for (double z = -height / 2; z <= height / 2; z += 1.0) {
                Location particleLocation = center.clone().add(x, 0, z);
                
                center.getWorld().spawnParticle(
                    Particle.EXPLOSION,
                    particleLocation,
                    1
                );
            }
        }
    }
    
    /**
     * Plays a sound effect.
     */
    protected void playSound(@NotNull String sound) {
        try {
            Sound bukkitSound = Sound.valueOf(sound.toUpperCase());
            Location bossLocation = bossInstance.getEntity().getLocation();
            
            bossLocation.getWorld().playSound(bossLocation, bukkitSound, 1.0f, 1.0f);
        } catch (IllegalArgumentException e) {
            // Invalid sound name, ignore
        }
    }
    
    /**
     * Shows a telegraph message to nearby players.
     */
    protected void showTelegraphMessage(@NotNull String message) {
        Collection<Player> nearbyPlayers = bossInstance.getParticipantPlayers();
        
        for (Player player : nearbyPlayers) {
            player.showTitle(Title.title(
                Component.text("§c§lWARNING"),
                Component.text(message),
                Title.Times.times(
                    Duration.ofMillis(500),
                    Duration.ofMillis(2000),
                    Duration.ofMillis(500)
                )
            ));
        }
    }
    
    /**
     * Executes the actual ability effect.
     * Must be implemented by subclasses.
     */
    @NotNull
    protected abstract CompletableFuture<Void> executeAbility();
    
    /**
     * Gets the boss instance.
     */
    @NotNull
    public BossInstance getBossInstance() {
        return bossInstance;
    }
    
    /**
     * Gets the ability definition.
     */
    @NotNull
    public BossAbility getAbility() {
        return ability;
    }
}
