package com.ultimatedungeon.udx.api.dungeon;

import org.bukkit.World;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Immutable representation of a dungeon instance for API consumers.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record DungeonInstance(
    @NotNull UUID instanceId,
    @NotNull String dungeonId,
    @NotNull String difficulty,
    @NotNull DungeonInstanceState state,
    @NotNull List<UUID> players,
    @NotNull UUID partyLeader,
    @NotNull Instant createdAt,
    @Nullable Instant startedAt,
    @Nullable Instant completedAt,
    @NotNull String worldName,
    int currentRoom,
    int totalRooms,
    long elapsedTimeMs
) {
    
    /**
     * Creates a new dungeon instance record.
     */
    public DungeonInstance {
        // Validation
        if (instanceId == null) {
            throw new IllegalArgumentException("Instance ID cannot be null");
        }
        if (dungeonId == null || dungeonId.isBlank()) {
            throw new IllegalArgumentException("Dungeon ID cannot be null or blank");
        }
        if (difficulty == null || difficulty.isBlank()) {
            throw new IllegalArgumentException("Difficulty cannot be null or blank");
        }
        if (state == null) {
            throw new IllegalArgumentException("State cannot be null");
        }
        if (players == null) {
            throw new IllegalArgumentException("Players list cannot be null");
        }
        if (partyLeader == null) {
            throw new IllegalArgumentException("Party leader cannot be null");
        }
        if (createdAt == null) {
            throw new IllegalArgumentException("Created at cannot be null");
        }
        if (worldName == null || worldName.isBlank()) {
            throw new IllegalArgumentException("World name cannot be null or blank");
        }
        if (currentRoom < 0) {
            throw new IllegalArgumentException("Current room cannot be negative");
        }
        if (totalRooms < 1) {
            throw new IllegalArgumentException("Total rooms must be at least 1");
        }
        if (elapsedTimeMs < 0) {
            throw new IllegalArgumentException("Elapsed time cannot be negative");
        }
    }
    
    /**
     * Gets the number of players in the instance.
     * 
     * @return The player count
     */
    public int getPlayerCount() {
        return players.size();
    }
    
    /**
     * Checks if a player is in this instance.
     * 
     * @param playerId The player UUID
     * @return True if the player is in the instance
     */
    public boolean hasPlayer(@NotNull UUID playerId) {
        return players.contains(playerId);
    }
    
    /**
     * Checks if a player is in this instance.
     * 
     * @param player The player
     * @return True if the player is in the instance
     */
    public boolean hasPlayer(@NotNull Player player) {
        return hasPlayer(player.getUniqueId());
    }
    
    /**
     * Checks if the instance is active (running or completing).
     * 
     * @return True if the instance is active
     */
    public boolean isActive() {
        return state == DungeonInstanceState.RUNNING || state == DungeonInstanceState.COMPLETING;
    }
    
    /**
     * Checks if the instance is completed.
     * 
     * @return True if the instance is completed
     */
    public boolean isCompleted() {
        return state == DungeonInstanceState.COMPLETED || state == DungeonInstanceState.FAILED;
    }
    
    /**
     * Gets the progress percentage (0-100).
     * 
     * @return The progress percentage
     */
    public double getProgressPercent() {
        if (totalRooms <= 0) return 0.0;
        return Math.min(100.0, (double) currentRoom / totalRooms * 100.0);
    }
    
    /**
     * Gets the duration of the instance in milliseconds.
     * 
     * @return The duration in milliseconds
     */
    public long getDurationMs() {
        if (startedAt == null) return 0L;
        
        Instant endTime = completedAt != null ? completedAt : Instant.now();
        return endTime.toEpochMilli() - startedAt.toEpochMilli();
    }
    
    /**
     * Checks if the player is the party leader.
     * 
     * @param playerId The player UUID
     * @return True if the player is the party leader
     */
    public boolean isPartyLeader(@NotNull UUID playerId) {
        return partyLeader.equals(playerId);
    }
    
    /**
     * Checks if the player is the party leader.
     * 
     * @param player The player
     * @return True if the player is the party leader
     */
    public boolean isPartyLeader(@NotNull Player player) {
        return isPartyLeader(player.getUniqueId());
    }
}
