name: ApexDungeons
main: com.apexdungeons.ApexDungeons
version: 0.1.0
api-version: "1.21"
softdepend:
  - MythicMobs
  - ModelEngine
authors: ["ApexDungeons Team"]
description: |
  ApexDungeons procedurally generates complex dungeon structures on demand. It comes with a polished GUI,
  modular room system and support for third‑party mob libraries like MythicMobs and ModelEngine.
commands:
  dgn:
    description: Main command for Soaps Dungeons.
    usage: /dgn [admin|givewand|create|remove|tp|start|leave|tools|reloadschematics|testworld|help]
    aliases: [dungeon, dgns, soapsdungeons]
permissions:
  apexdungeons.use:
    description: Allows access to the main dungeon GUI and interaction with dungeons.
    default: true
  apexdungeons.admin:
    description: Allows administrative control over dungeon generation and management.
    default: op
  apexdungeons.bypass:
    description: Bypass world or region restrictions.
    default: op