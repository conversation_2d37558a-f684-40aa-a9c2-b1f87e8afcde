package com.ultimatedungeon.udx.api.progression;

import org.jetbrains.annotations.NotNull;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Immutable representation of player profile data for API consumers.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record PlayerProfileData(
    @NotNull UUID playerId,
    @NotNull String playerName,
    @NotNull Instant firstJoined,
    @NotNull Instant lastSeen,
    int totalDungeonsCompleted,
    int totalDeaths,
    long totalPlayTimeMs,
    double totalDamageDealt,
    double totalHealingDone,
    int currentSeasonScore,
    @NotNull List<String> unlockedDungeons,
    @NotNull List<String> completedAchievements,
    @NotNull String preferredDifficulty
) {
    
    /**
     * Creates new player profile data.
     */
    public PlayerProfileData {
        if (playerId == null) {
            throw new IllegalArgumentException("Player ID cannot be null");
        }
        if (playerName == null || playerName.isBlank()) {
            throw new IllegalArgumentException("Player name cannot be null or blank");
        }
        if (firstJoined == null) {
            throw new IllegalArgumentException("First joined cannot be null");
        }
        if (lastSeen == null) {
            throw new IllegalArgumentException("Last seen cannot be null");
        }
        if (totalDungeonsCompleted < 0) {
            throw new IllegalArgumentException("Total dungeons completed cannot be negative");
        }
        if (totalDeaths < 0) {
            throw new IllegalArgumentException("Total deaths cannot be negative");
        }
        if (totalPlayTimeMs < 0) {
            throw new IllegalArgumentException("Total play time cannot be negative");
        }
        if (totalDamageDealt < 0) {
            throw new IllegalArgumentException("Total damage dealt cannot be negative");
        }
        if (totalHealingDone < 0) {
            throw new IllegalArgumentException("Total healing done cannot be negative");
        }
        if (currentSeasonScore < 0) {
            throw new IllegalArgumentException("Current season score cannot be negative");
        }
        if (unlockedDungeons == null) {
            throw new IllegalArgumentException("Unlocked dungeons cannot be null");
        }
        if (completedAchievements == null) {
            throw new IllegalArgumentException("Completed achievements cannot be null");
        }
        if (preferredDifficulty == null || preferredDifficulty.isBlank()) {
            throw new IllegalArgumentException("Preferred difficulty cannot be null or blank");
        }
    }
    
    /**
     * Gets the player's average completion time per dungeon.
     * 
     * @return Average completion time in milliseconds
     */
    public long getAverageCompletionTime() {
        if (totalDungeonsCompleted == 0) return 0L;
        return totalPlayTimeMs / totalDungeonsCompleted;
    }
    
    /**
     * Gets the player's death rate per dungeon.
     * 
     * @return Deaths per dungeon
     */
    public double getDeathRate() {
        if (totalDungeonsCompleted == 0) return 0.0;
        return (double) totalDeaths / totalDungeonsCompleted;
    }
    
    /**
     * Gets the number of achievements completed.
     * 
     * @return Number of completed achievements
     */
    public int getAchievementCount() {
        return completedAchievements.size();
    }
    
    /**
     * Gets the number of dungeons unlocked.
     * 
     * @return Number of unlocked dungeons
     */
    public int getUnlockedDungeonCount() {
        return unlockedDungeons.size();
    }
    
    /**
     * Checks if a specific dungeon is unlocked.
     * 
     * @param dungeonId The dungeon ID
     * @return True if the dungeon is unlocked
     */
    public boolean isDungeonUnlocked(@NotNull String dungeonId) {
        return unlockedDungeons.contains(dungeonId);
    }
    
    /**
     * Checks if a specific achievement is completed.
     * 
     * @param achievementId The achievement ID
     * @return True if the achievement is completed
     */
    public boolean isAchievementCompleted(@NotNull String achievementId) {
        return completedAchievements.contains(achievementId);
    }
}
