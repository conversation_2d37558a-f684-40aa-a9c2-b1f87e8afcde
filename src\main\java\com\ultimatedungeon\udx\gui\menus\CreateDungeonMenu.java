package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gen.ApexStyleGenerator;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.instance.VoidWorldGenerator;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.WorldType;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.io.File;

/**
 * Menu for creating new custom dungeons.
 * 
 * <p>This menu allows admins to create new dungeon worlds with predefined names
 * and automatically generates worlds for building.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class CreateDungeonMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public CreateDungeonMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Create Dungeon").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        // Clear inventory
        inventory.clear();
        clickHandlers.clear();
        
        // Title item
        ItemStack titleItem = new ItemBuilder(Material.DIAMOND_SWORD)
            .name(Component.text("Create New Dungeon").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Choose a dungeon template below").color(NamedTextColor.GRAY),
                Component.text("Each template creates a unique dungeon").color(NamedTextColor.GRAY)
            )
            .glow()
            .build();
        setItem(4, titleItem);
        
        // Premade Dungeon Templates
        createDungeonTemplate(19, "CryptOfEchoes", Material.SKELETON_SKULL, 
            "Crypt of Echoes", "A haunted crypt filled with undead", NamedTextColor.DARK_PURPLE);
        
        createDungeonTemplate(21, "EmberFoundry", Material.MAGMA_BLOCK,
            "Ember Foundry", "A fiery forge deep underground", NamedTextColor.RED);
        
        createDungeonTemplate(23, "SkyfaneSpire", Material.END_CRYSTAL,
            "Skyfane Spire", "A mystical tower reaching the clouds", NamedTextColor.LIGHT_PURPLE);
        
        createDungeonTemplate(25, "FrozenCaverns", Material.ICE,
            "Frozen Caverns", "Icy caves with frozen treasures", NamedTextColor.AQUA);
        
        // Custom Empty World Options
        createEmptyWorldOption(37, "CustomDungeon1", "Custom Dungeon 1");
        createEmptyWorldOption(39, "CustomDungeon2", "Custom Dungeon 2");
        createEmptyWorldOption(41, "CustomDungeon3", "Custom Dungeon 3");
        createEmptyWorldOption(43, "TestDungeon", "Test Dungeon");
        
        // Back Button
        ItemStack backButton = new ItemBuilder(Material.BARRIER)
            .name(Component.text("← Back").color(NamedTextColor.RED))
            .lore(Component.text("Return to admin hub").color(NamedTextColor.GRAY))
            .build();
        
        setItem(45, backButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                new AdminHubMenu(plugin, player).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
            }
        });
        
        // Fill empty slots
        fillEmptySlots();
    }
    
    private void createDungeonTemplate(int slot, String dungeonId, Material material, String displayName, String description, NamedTextColor color) {
        ItemStack templateItem = new ItemBuilder(material)
            .name(Component.text(displayName).color(color).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text(description).color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Features:").color(NamedTextColor.YELLOW),
                Component.text("• Pre-built rooms and corridors").color(NamedTextColor.GRAY),
                Component.text("• Boss arena included").color(NamedTextColor.GRAY),
                Component.text("• Loot chests placed").color(NamedTextColor.GRAY),
                Component.text("• Mob spawners configured").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to create this dungeon").color(NamedTextColor.GREEN)
            )
            .glow()
            .build();
        
        setItem(slot, templateItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                createPremadeDungeon(dungeonId, displayName);
            }
        });
    }
    
    private void createEmptyWorldOption(int slot, String dungeonId, String displayName) {
        ItemStack emptyItem = new ItemBuilder(Material.GRASS_BLOCK)
            .name(Component.text(displayName).color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Empty superflat world for building").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Features:").color(NamedTextColor.YELLOW),
                Component.text("• Flat grass terrain").color(NamedTextColor.GRAY),
                Component.text("• No mobs or weather").color(NamedTextColor.GRAY),
                Component.text("• Perfect for custom building").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to create empty world").color(NamedTextColor.GREEN)
            )
            .build();
        
        setItem(slot, emptyItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                createEmptyDungeon(dungeonId, displayName);
            }
        });
    }
    
    private void createPremadeDungeon(String dungeonId, String displayName) {
        close();
        
        player.sendMessage(Component.text("Creating " + displayName + "...").color(NamedTextColor.YELLOW));
        player.playSound(player.getLocation(), Sound.BLOCK_PORTAL_AMBIENT, 1.0f, 1.0f);
        
        String worldName = "udx_dungeon_" + dungeonId.toLowerCase();
        
        // Check if already exists
        if (Bukkit.getWorld(worldName) != null || plugin.getDungeonService().hasCustomDungeon(dungeonId)) {
            player.sendMessage(Component.text("A dungeon with that name already exists!").color(NamedTextColor.RED));
            player.sendMessage(Component.text("Use '/udx tp " + dungeonId + "' to teleport there").color(NamedTextColor.YELLOW));
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            
            plugin.getSchedulerUtil().runTaskLater(() -> {
                new AdminHubMenu(plugin, player).open();
            }, 40L);
            return;
        }
        
        try {
            // Create void world
            WorldCreator creator = new WorldCreator(worldName);
            creator.generator(new VoidWorldGenerator());
            creator.generateStructures(false);
            
            World world = creator.createWorld();
            
            if (world != null) {
                // Set world properties
                world.setDifficulty(org.bukkit.Difficulty.NORMAL);
                world.setSpawnFlags(true, true);
                world.setPVP(false);
                world.setGameRule(org.bukkit.GameRule.DO_DAYLIGHT_CYCLE, false);
                world.setGameRule(org.bukkit.GameRule.DO_WEATHER_CYCLE, false);
                world.setGameRule(org.bukkit.GameRule.KEEP_INVENTORY, true);
                world.setTime(6000);
                world.setStorm(false);
                
                player.sendMessage(Component.text("World created! Generating dungeon structure...").color(NamedTextColor.AQUA));
                
                // Generate dungeon asynchronously
                ApexStyleGenerator generator = new ApexStyleGenerator(plugin);
                long seed = dungeonId.hashCode(); // Consistent seed for same dungeon type
                
                generator.generateDungeon(world, 0, 64, 0, seed).thenAccept(result -> {
                    plugin.getSchedulerUtil().runTask(() -> {
                        if (result.isSuccess()) {
                            // Register dungeon
                            plugin.getDungeonService().registerCustomDungeon(dungeonId, worldName);
                            
                            // Add theme-specific decorations
                            addDungeonTheme(world, dungeonId);
                            
                            player.sendMessage(Component.text(displayName + " created successfully!").color(NamedTextColor.GREEN));
                            player.sendMessage(Component.text("Generated " + result.getLayout().getRooms().size() + " rooms").color(NamedTextColor.AQUA));
                            player.sendMessage(Component.text("Use '/udx tp " + dungeonId + "' to teleport there").color(NamedTextColor.YELLOW));
                            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
                            
                            plugin.getSchedulerUtil().runTaskLater(() -> {
                                new AdminHubMenu(plugin, player).open();
                            }, 40L);
                        } else {
                            player.sendMessage(Component.text("Failed to generate dungeon: " + result.getMessage()).color(NamedTextColor.RED));
                            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                        }
                    });
                }).exceptionally(throwable -> {
                    plugin.getSchedulerUtil().runTask(() -> {
                        plugin.getLogger().severe("Error generating dungeon: " + throwable.getMessage());
                        throwable.printStackTrace();
                        player.sendMessage(Component.text("Error during generation: " + throwable.getMessage()).color(NamedTextColor.RED));
                        player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                    });
                    return null;
                });
                
            } else {
                player.sendMessage(Component.text("Failed to create world! Check server logs.").color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("Error creating dungeon: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(Component.text("Error creating dungeon: " + e.getMessage()).color(NamedTextColor.RED));
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
        }
    }
    
    private void createEmptyDungeon(String dungeonId, String displayName) {
        close();
        
        player.sendMessage(Component.text("Creating " + displayName + "...").color(NamedTextColor.YELLOW));
        player.playSound(player.getLocation(), Sound.BLOCK_PORTAL_AMBIENT, 1.0f, 1.0f);
        
        String worldName = "udx_dungeon_" + dungeonId.toLowerCase();
        
        // Check if already exists
        if (Bukkit.getWorld(worldName) != null || plugin.getDungeonService().hasCustomDungeon(dungeonId)) {
            player.sendMessage(Component.text("A dungeon with that name already exists!").color(NamedTextColor.RED));
            player.sendMessage(Component.text("Use '/udx tp " + dungeonId + "' to teleport there").color(NamedTextColor.YELLOW));
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            
            plugin.getSchedulerUtil().runTaskLater(() -> {
                new AdminHubMenu(plugin, player).open();
            }, 40L);
            return;
        }
        
        try {
            // Create superflat world
            WorldCreator creator = new WorldCreator(worldName);
            creator.type(WorldType.FLAT);
            creator.generatorSettings("minecraft:bedrock,2*minecraft:dirt,minecraft:grass_block;minecraft:plains;village");
            creator.generateStructures(false);
            
            World world = creator.createWorld();
            
            if (world != null) {
                // Set world properties
                world.setDifficulty(org.bukkit.Difficulty.PEACEFUL);
                world.setSpawnFlags(false, false);
                world.setPVP(false);
                world.setGameRule(org.bukkit.GameRule.DO_MOB_SPAWNING, false);
                world.setGameRule(org.bukkit.GameRule.DO_DAYLIGHT_CYCLE, false);
                world.setGameRule(org.bukkit.GameRule.DO_WEATHER_CYCLE, false);
                world.setGameRule(org.bukkit.GameRule.KEEP_INVENTORY, true);
                world.setTime(6000);
                world.setStorm(false);
                
                // Register dungeon
                plugin.getDungeonService().registerCustomDungeon(dungeonId, worldName);
                
                player.sendMessage(Component.text(displayName + " created successfully!").color(NamedTextColor.GREEN));
                player.sendMessage(Component.text("Use '/udx tp " + dungeonId + "' to teleport there").color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("Use '/udx tools' in the world to access builder tools").color(NamedTextColor.AQUA));
                player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
                
                plugin.getSchedulerUtil().runTaskLater(() -> {
                    new AdminHubMenu(plugin, player).open();
                }, 40L);
                
            } else {
                player.sendMessage(Component.text("Failed to create world! Check server logs.").color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("Error creating empty dungeon: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(Component.text("Error creating dungeon: " + e.getMessage()).color(NamedTextColor.RED));
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
        }
    }
    
    private void addDungeonTheme(World world, String dungeonId) {
        // Add theme-specific decorations based on dungeon type
        // This is a placeholder for future theme implementation
        switch (dungeonId.toLowerCase()) {
            case "cryptofechoes":
                // Could add cobwebs, skulls, etc.
                break;
            case "emberfoundry":
                // Could add lava, magma blocks, etc.
                break;
            case "skyfanespire":
                // Could add end stone, crystals, etc.
                break;
            case "frozencaverns":
                // Could add ice, snow, etc.
                break;
            default:
                // Default theme
                break;
        }
    }
}
