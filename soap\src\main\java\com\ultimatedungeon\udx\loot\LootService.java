package com.ultimatedungeon.udx.loot;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.Chest;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.logging.Logger;

/**
 * Service for managing loot generation, chest filling, and reward distribution.
 * 
 * <p>Handles weighted random generation, rarity processing, chest regeneration,
 * and integration with economy systems.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class LootService {
    
    private final UltimateDungeonX plugin;
    private final SchedulerUtil schedulerUtil;
    private final Logger logger;
    
    private final Map<String, LootTable> lootTables = new ConcurrentHashMap<>();
    private final Map<Location, ChestRegenInfo> chestRegenData = new ConcurrentHashMap<>();
    private final Random random = new Random();
    
    // Vault integration (soft dependency)
    private Object economy = null;
    private boolean vaultEnabled = false;
    
    public LootService(@NotNull UltimateDungeonX plugin, @NotNull SchedulerUtil schedulerUtil) {
        this.plugin = plugin;
        this.schedulerUtil = schedulerUtil;
        this.logger = plugin.getLogger();
        
        setupVaultIntegration();
        startChestRegenTask();
    }
    
    /**
     * Registers a loot table.
     * 
     * @param table the loot table to register
     */
    public void registerLootTable(@NotNull LootTable table) {
        lootTables.put(table.getId(), table);
        logger.info("Registered loot table: " + table.getId());
    }
    
    /**
     * Gets a loot table by ID.
     * 
     * @param tableId the table ID
     * @return the loot table, or null if not found
     */
    @Nullable
    public LootTable getLootTable(@NotNull String tableId) {
        return lootTables.get(tableId);
    }
    
    /**
     * Gets all registered loot tables.
     * 
     * @return immutable collection of loot tables
     */
    @NotNull
    public Collection<LootTable> getAllLootTables() {
        return Collections.unmodifiableCollection(lootTables.values());
    }
    
    /**
     * Generates loot from a table with context.
     * 
     * @param tableId the table ID
     * @param context the generation context
     * @return list of generated items
     */
    @NotNull
    public List<ItemStack> generateLoot(@NotNull String tableId, @NotNull Map<String, Object> context) {
        LootTable table = getLootTable(tableId);
        if (table == null) {
            logger.warning("Loot table not found: " + tableId);
            return Collections.emptyList();
        }
        
        return generateLoot(table, context);
    }
    
    /**
     * Generates loot from a table with context.
     * 
     * @param table the loot table
     * @param context the generation context
     * @return list of generated items
     */
    @NotNull
    public List<ItemStack> generateLoot(@NotNull LootTable table, @NotNull Map<String, Object> context) {
        List<ItemStack> results = new ArrayList<>();
        
        // Determine number of rolls
        int rolls = table.getMinRolls();
        if (table.getMaxRolls() > table.getMinRolls()) {
            rolls = random.nextInt(table.getMaxRolls() - table.getMinRolls() + 1) + table.getMinRolls();
        }
        
        // Apply difficulty multiplier if present
        double difficultyMultiplier = (Double) context.getOrDefault("difficultyMultiplier", 1.0);
        if (difficultyMultiplier != 1.0) {
            rolls = (int) Math.ceil(rolls * difficultyMultiplier);
        }
        
        // Perform rolls
        for (int i = 0; i < rolls; i++) {
            ItemStack item = rollLoot(table, context);
            if (item != null) {
                results.add(item);
            }
        }
        
        // Check for jackpot
        if (table.hasJackpot() && random.nextDouble() < table.getJackpotChance()) {
            LootTable jackpotTable = getLootTable(table.getJackpotTableId());
            if (jackpotTable != null) {
                results.addAll(generateLoot(jackpotTable, context));
                
                // Notify players of jackpot
                if (context.containsKey("players")) {
                    @SuppressWarnings("unchecked")
                    List<Player> players = (List<Player>) context.get("players");
                    for (Player player : players) {
                        player.sendMessage(Component.text("§6§l✦ JACKPOT! ✦ §eYou hit the jackpot!"));
                    }
                }
            }
        }
        
        return results;
    }
    
    /**
     * Fills a chest with loot from a table.
     * 
     * @param chest the chest to fill
     * @param tableId the loot table ID
     * @param context the generation context
     */
    public void fillChest(@NotNull Chest chest, @NotNull String tableId, @NotNull Map<String, Object> context) {
        List<ItemStack> loot = generateLoot(tableId, context);
        fillChestWithItems(chest, loot);
    }
    
    /**
     * Fills a chest with specific items.
     * 
     * @param chest the chest to fill
     * @param items the items to place
     */
    public void fillChestWithItems(@NotNull Chest chest, @NotNull List<ItemStack> items) {
        Inventory inventory = chest.getInventory();
        inventory.clear();
        
        // Randomly distribute items in chest slots
        List<Integer> availableSlots = new ArrayList<>();
        for (int i = 0; i < inventory.getSize(); i++) {
            availableSlots.add(i);
        }
        Collections.shuffle(availableSlots, random);
        
        int slotIndex = 0;
        for (ItemStack item : items) {
            if (slotIndex >= availableSlots.size()) {
                break; // Chest is full
            }
            
            int slot = availableSlots.get(slotIndex++);
            inventory.setItem(slot, item);
        }
    }
    
    /**
     * Sets up chest regeneration for a location.
     * 
     * @param location the chest location
     * @param tableId the loot table ID
     * @param regenType the regeneration type
     * @param regenInterval the regeneration interval in seconds (for timed regen)
     */
    public void setupChestRegen(@NotNull Location location, @NotNull String tableId, 
                               @NotNull ChestRegenType regenType, long regenInterval) {
        ChestRegenInfo info = new ChestRegenInfo(tableId, regenType, regenInterval, System.currentTimeMillis());
        chestRegenData.put(location, info);
    }
    
    /**
     * Removes chest regeneration for a location.
     * 
     * @param location the chest location
     */
    public void removeChestRegen(@NotNull Location location) {
        chestRegenData.remove(location);
    }
    
    /**
     * Manually triggers chest regeneration.
     * 
     * @param location the chest location
     * @param context the generation context
     */
    public void regenerateChest(@NotNull Location location, @NotNull Map<String, Object> context) {
        ChestRegenInfo info = chestRegenData.get(location);
        if (info == null) {
            return;
        }
        
        Block block = location.getBlock();
        if (!(block.getState() instanceof Chest chest)) {
            return;
        }
        
        fillChest(chest, info.tableId(), context);
        info.updateLastRegen();
    }
    
    /**
     * Gives loot directly to players.
     * 
     * @param players the players to give loot to
     * @param tableId the loot table ID
     * @param context the generation context
     */
    public void giveLootToPlayers(@NotNull List<Player> players, @NotNull String tableId, 
                                 @NotNull Map<String, Object> context) {
        LootTable table = getLootTable(tableId);
        if (table == null) {
            return;
        }
        
        if (table.isPerPlayer()) {
            // Generate separate loot for each player
            for (Player player : players) {
                Map<String, Object> playerContext = new HashMap<>(context);
                playerContext.put("player", player);
                
                List<ItemStack> loot = generateLoot(table, playerContext);
                giveLootToPlayer(player, loot);
            }
        } else {
            // Generate shared loot
            List<ItemStack> loot = generateLoot(table, context);
            
            // Distribute evenly among players
            for (int i = 0; i < loot.size(); i++) {
                Player player = players.get(i % players.size());
                giveLootToPlayer(player, Collections.singletonList(loot.get(i)));
            }
        }
    }
    
    /**
     * Gives items to a specific player.
     * 
     * @param player the player
     * @param items the items to give
     */
    public void giveLootToPlayer(@NotNull Player player, @NotNull List<ItemStack> items) {
        for (ItemStack item : items) {
            // Try to add to inventory
            HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(item);
            
            // Drop leftover items
            for (ItemStack drop : leftover.values()) {
                player.getWorld().dropItemNaturally(player.getLocation(), drop);
            }
        }
    }
    
    /**
     * Applies rarity effects to an item.
     * 
     * @param item the item to modify
     * @param rarity the rarity to apply
     * @return the modified item
     */
    @NotNull
    public ItemStack applyRarityEffects(@NotNull ItemStack item, @NotNull Rarity rarity) {
        ItemStack result = item.clone();
        ItemMeta meta = result.getItemMeta();
        
        if (meta != null) {
            // Apply rarity to display name
            Component displayName = meta.displayName();
            if (displayName == null) {
                displayName = Component.translatable(result.getType().name().toLowerCase());
            }
            
            Component rarityName = rarity.formatText(displayName.toString());
            meta.displayName(rarity.getPrefix().append(rarityName));
            
            // Add glow effect for higher rarities
            if (rarity.hasGlow()) {
                meta.addEnchant(Enchantment.UNBREAKING, 1, true);
                meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
            }
            
            // Add rarity to lore
            List<Component> lore = meta.lore();
            if (lore == null) {
                lore = new ArrayList<>();
            }
            
            lore.add(Component.empty());
            lore.add(rarity.formatText("Rarity: " + rarity.getDisplayName()));
            meta.lore(lore);
            
            result.setItemMeta(meta);
        }
        
        return result;
    }
    
    /**
     * Shuts down the loot service.
     */
    public void shutdown() {
        lootTables.clear();
        chestRegenData.clear();
    }
    
    // Private methods
    
    private void setupVaultIntegration() {
        if (Bukkit.getPluginManager().getPlugin("Vault") != null) {
            try {
                Class<?> economyClass = Class.forName("net.milkbowl.vault.economy.Economy");
                RegisteredServiceProvider<?> rsp = Bukkit.getServicesManager().getRegistration(economyClass);
                if (rsp != null) {
                    economy = rsp.getProvider();
                    vaultEnabled = true;
                    logger.info("Vault economy integration enabled");
                }
            } catch (ClassNotFoundException e) {
                logger.info("Vault not found, economy features disabled");
            }
        }
    }
    
    private void startChestRegenTask() {
        schedulerUtil.runTaskTimer(() -> {
            long currentTime = System.currentTimeMillis();
            
            chestRegenData.entrySet().removeIf(entry -> {
                Location location = entry.getKey();
                ChestRegenInfo info = entry.getValue();
                
                if (info.regenType() == ChestRegenType.TIMED) {
                    long timeSinceRegen = currentTime - info.lastRegenTime();
                    if (timeSinceRegen >= info.regenInterval() * 1000) {
                        // Regenerate chest
                        Map<String, Object> context = Map.of("automated", true);
                        regenerateChest(location, context);
                        return false; // Keep the entry
                    }
                }
                
                // Remove entries for non-existent chests
                Block block = location.getBlock();
                return !(block.getState() instanceof Chest);
            });
        }, 20L, 20L); // Run every second
    }
    
    @Nullable
    private ItemStack rollLoot(@NotNull LootTable table, @NotNull Map<String, Object> context) {
        List<LootEntry> validEntries = table.getValidEntries(context);
        if (validEntries.isEmpty()) {
            return null;
        }
        
        // Calculate total weight
        double totalWeight = validEntries.stream()
                .mapToDouble(LootEntry::weight)
                .sum();
        
        if (totalWeight <= 0) {
            return null;
        }
        
        // Roll for entry
        double roll = random.nextDouble() * totalWeight;
        double currentWeight = 0;
        
        for (LootEntry entry : validEntries) {
            currentWeight += entry.weight();
            if (roll <= currentWeight) {
                return generateItemFromEntry(entry, context);
            }
        }
        
        return null;
    }
    
    @Nullable
    private ItemStack generateItemFromEntry(@NotNull LootEntry entry, @NotNull Map<String, Object> context) {
        switch (entry.type()) {
            case ITEM -> {
                return generateItem(entry, context);
            }
            case COMMAND -> {
                executeCommand(entry, context);
                return null;
            }
            case CURRENCY -> {
                giveCurrency(entry, context);
                return null;
            }
            case TABLE_REFERENCE -> {
                LootTable referencedTable = getLootTable(entry.value());
                if (referencedTable != null) {
                    List<ItemStack> items = generateLoot(referencedTable, context);
                    return items.isEmpty() ? null : items.get(0);
                }
                return null;
            }
            case COSMETIC_TOKEN -> {
                return generateCosmeticToken(entry, context);
            }
        }
        
        return null;
    }
    
    @NotNull
    private ItemStack generateItem(@NotNull LootEntry entry, @NotNull Map<String, Object> context) {
        Material material;
        try {
            material = Material.valueOf(entry.value().toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warning("Invalid material in loot entry: " + entry.value());
            material = Material.DIRT;
        }
        
        // Determine quantity
        int quantity = entry.minQuantity();
        if (entry.maxQuantity() > entry.minQuantity()) {
            quantity = random.nextInt(entry.maxQuantity() - entry.minQuantity() + 1) + entry.minQuantity();
        }
        
        ItemStack item = new ItemStack(material, quantity);
        return applyRarityEffects(item, entry.rarity());
    }
    
    private void executeCommand(@NotNull LootEntry entry, @NotNull Map<String, Object> context) {
        String command = entry.value();
        
        // Replace placeholders
        if (context.containsKey("player")) {
            Player player = (Player) context.get("player");
            command = command.replace("{player}", player.getName());
        }
        
        // Execute command
        final String finalCommand = command;
        schedulerUtil.runTask(() -> {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), finalCommand);
        });
    }
    
    private void giveCurrency(@NotNull LootEntry entry, @NotNull Map<String, Object> context) {
        if (!vaultEnabled || !context.containsKey("player")) {
            return;
        }
        
        Player player = (Player) context.get("player");
        
        // Determine amount
        int amount = entry.minQuantity();
        if (entry.maxQuantity() > entry.minQuantity()) {
            amount = random.nextInt(entry.maxQuantity() - entry.minQuantity() + 1) + entry.minQuantity();
        }
        
        // Use reflection to call economy methods (soft dependency)
        try {
            Class<?> economyClass = economy.getClass();
            economyClass.getMethod("depositPlayer", Player.class, double.class).invoke(economy, player, (double) amount);
            player.sendMessage(Component.text("§a+$" + amount));
        } catch (Exception e) {
            logger.warning("Failed to give currency to player: " + e.getMessage());
        }
    }
    
    @NotNull
    private ItemStack generateCosmeticToken(@NotNull LootEntry entry, @NotNull Map<String, Object> context) {
        ItemStack token = new ItemStack(Material.SUNFLOWER);
        ItemMeta meta = token.getItemMeta();
        
        if (meta != null) {
            meta.displayName(Component.text("§6Cosmetic Token"));
            meta.lore(List.of(
                Component.text("§7A special token that can be"),
                Component.text("§7exchanged for cosmetic rewards"),
                Component.empty(),
                Component.text("§eType: §f" + entry.value())
            ));
            token.setItemMeta(meta);
        }
        
        return applyRarityEffects(token, entry.rarity());
    }
    
    /**
     * Information about chest regeneration.
     */
    private static class ChestRegenInfo {
        private final String tableId;
        private final ChestRegenType regenType;
        private final long regenInterval;
        private long lastRegenTime;
        
        public ChestRegenInfo(@NotNull String tableId, @NotNull ChestRegenType regenType, 
                             long regenInterval, long lastRegenTime) {
            this.tableId = tableId;
            this.regenType = regenType;
            this.regenInterval = regenInterval;
            this.lastRegenTime = lastRegenTime;
        }
        
        public String tableId() { return tableId; }
        public ChestRegenType regenType() { return regenType; }
        public long regenInterval() { return regenInterval; }
        public long lastRegenTime() { return lastRegenTime; }
        
        public void updateLastRegen() {
            this.lastRegenTime = System.currentTimeMillis();
        }
    }
    
    /**
     * Types of chest regeneration.
     */
    public enum ChestRegenType {
        /** No regeneration */
        NONE,
        /** Regenerate once per instance */
        ONCE,
        /** Regenerate on a timer */
        TIMED,
        /** Regenerate per player */
        PER_PLAYER
    }
}
