package com.ultimatedungeon.udx.combat.mechanics;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.combat.CombatService;
import com.ultimatedungeon.udx.combat.StatusEffect;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.title.Title;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.NotNull;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Utility class for applying various combat mechanics.
 * 
 * <p>Provides methods for common combat effects like knockback, roots,
 * fears, pulls, pushes, and other crowd control mechanics.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class CombatMechanics {
    
    private final UltimateDungeonX plugin;
    private final CombatService combatService;
    
    public CombatMechanics(@NotNull UltimateDungeonX plugin, @NotNull CombatService combatService) {
        this.plugin = plugin;
        this.combatService = combatService;
    }
    
    /**
     * Applies knockback to an entity.
     * 
     * @param source the source of the knockback
     * @param target the target entity
     * @param strength the knockback strength
     * @param upward whether to include upward momentum
     */
    public void applyKnockback(@NotNull LivingEntity source, @NotNull LivingEntity target, 
                              double strength, boolean upward) {
        
        Vector direction = target.getLocation().toVector().subtract(source.getLocation().toVector());
        direction.setY(0); // Remove Y component for horizontal knockback
        direction.normalize();
        
        if (upward) {
            direction.setY(0.3); // Add upward momentum
        }
        
        direction.multiply(strength);
        target.setVelocity(direction);
        
        // Visual effects
        target.getWorld().spawnParticle(Particle.EXPLOSION, target.getLocation(), 5);
        target.getWorld().playSound(target.getLocation(), Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.5f);
    }
    
    /**
     * Applies a root effect that prevents movement.
     * 
     * @param target the target entity
     * @param durationMs the duration in milliseconds
     * @param source the source description
     */
    public void applyRoot(@NotNull LivingEntity target, long durationMs, @NotNull String source) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("preventMovement", true);
        
        StatusEffect rootEffect = new StatusEffect(
            "root",
            "Rooted",
            StatusEffect.StatusEffectType.CC,
            (int) (durationMs / 50), // Convert to ticks
            1,
            parameters,
            source
        );
        
        combatService.applyStatusEffect(target, rootEffect);
        
        // Apply slowness potion effect as backup
        target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, (int) (durationMs / 50), 255, false, false));
        
        // Visual effects
        showRootVisuals(target);
        
        // Notify player
        if (target instanceof Player player) {
            player.showTitle(Title.title(
                Component.empty(),
                Component.text("§6You are rooted!"),
                Title.Times.times(Duration.ofMillis(500), Duration.ofMillis(1000), Duration.ofMillis(500))
            ));
        }
    }
    
    /**
     * Applies a fear effect that causes the target to run away.
     * 
     * @param source the source of the fear
     * @param target the target entity
     * @param durationMs the duration in milliseconds
     * @param sourceDescription the source description
     */
    public void applyFear(@NotNull LivingEntity source, @NotNull LivingEntity target, 
                         long durationMs, @NotNull String sourceDescription) {
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("fearSource", source.getUniqueId().toString());
        parameters.put("runSpeed", 1.5);
        
        StatusEffect fearEffect = new StatusEffect(
            "fear",
            "Feared",
            StatusEffect.StatusEffectType.CC,
            (int) (durationMs / 50), // Convert to ticks
            1,
            parameters,
            sourceDescription
        );
        
        combatService.applyStatusEffect(target, fearEffect);
        
        // Apply speed boost to make them run
        target.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, (int) (durationMs / 50), 2, false, false));
        
        // Make them run away
        Vector runDirection = target.getLocation().toVector().subtract(source.getLocation().toVector());
        runDirection.setY(0);
        runDirection.normalize().multiply(2.0);
        target.setVelocity(runDirection);
        
        // Visual effects
        showFearVisuals(target);
        
        // Notify player
        if (target instanceof Player player) {
            player.showTitle(Title.title(
                Component.text("§4FEAR"),
                Component.text("§cYou must flee!"),
                Title.Times.times(Duration.ofMillis(500), Duration.ofMillis(1500), Duration.ofMillis(500))
            ));
        }
    }
    
    /**
     * Pulls an entity towards a location.
     * 
     * @param target the target entity
     * @param destination the destination location
     * @param strength the pull strength
     */
    public void applyPull(@NotNull LivingEntity target, @NotNull Location destination, double strength) {
        Vector pullDirection = destination.toVector().subtract(target.getLocation().toVector());
        pullDirection.normalize().multiply(strength);
        
        target.setVelocity(pullDirection);
        
        // Visual effects
        showPullVisuals(target, destination);
    }
    
    /**
     * Pushes an entity away from a location.
     * 
     * @param target the target entity
     * @param source the source location
     * @param strength the push strength
     */
    public void applyPush(@NotNull LivingEntity target, @NotNull Location source, double strength) {
        Vector pushDirection = target.getLocation().toVector().subtract(source.toVector());
        pushDirection.setY(0); // Keep horizontal
        pushDirection.normalize().multiply(strength);
        pushDirection.setY(0.2); // Add slight upward momentum
        
        target.setVelocity(pushDirection);
        
        // Visual effects
        showPushVisuals(target, source);
    }
    
    /**
     * Applies a stun effect that prevents all actions.
     * 
     * @param target the target entity
     * @param durationMs the duration in milliseconds
     * @param source the source description
     */
    public void applyStun(@NotNull LivingEntity target, long durationMs, @NotNull String source) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("preventActions", true);
        parameters.put("preventMovement", true);
        
        StatusEffect stunEffect = new StatusEffect(
            "stun",
            "Stunned",
            StatusEffect.StatusEffectType.CC,
            (int) (durationMs / 50), // Convert to ticks
            1,
            parameters,
            source
        );
        
        combatService.applyStatusEffect(target, stunEffect);
        
        // Apply slowness and weakness
        target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, (int) (durationMs / 50), 255, false, false));
        target.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, (int) (durationMs / 50), 255, false, false));
        
        // Visual effects
        showStunVisuals(target);
        
        // Notify player
        if (target instanceof Player player) {
            player.showTitle(Title.title(
                Component.text("§e⚡ STUNNED ⚡"),
                Component.text("§7You cannot act!"),
                Title.Times.times(Duration.ofMillis(500), Duration.ofMillis(1500), Duration.ofMillis(500))
            ));
        }
    }
    
    /**
     * Applies a silence effect that prevents ability usage.
     * 
     * @param target the target entity
     * @param durationMs the duration in milliseconds
     * @param source the source description
     */
    public void applySilence(@NotNull LivingEntity target, long durationMs, @NotNull String source) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("preventAbilities", true);
        
        StatusEffect silenceEffect = new StatusEffect(
            "silence",
            "Silenced",
            StatusEffect.StatusEffectType.CC,
            (int) (durationMs / 50), // Convert to ticks
            1,
            parameters,
            source
        );
        
        combatService.applyStatusEffect(target, silenceEffect);
        
        // Visual effects
        showSilenceVisuals(target);
        
        // Notify player
        if (target instanceof Player player) {
            player.showTitle(Title.title(
                Component.empty(),
                Component.text("§5You are silenced!"),
                Title.Times.times(Duration.ofMillis(500), Duration.ofMillis(1000), Duration.ofMillis(500))
            ));
        }
    }
    
    /**
     * Applies a slow effect that reduces movement speed.
     * 
     * @param target the target entity
     * @param durationMs the duration in milliseconds
     * @param intensity the slow intensity (0.0 to 1.0)
     * @param source the source description
     */
    public void applySlow(@NotNull LivingEntity target, long durationMs, double intensity, @NotNull String source) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("speedReduction", intensity);
        
        StatusEffect slowEffect = new StatusEffect(
            "slowness",
            "Slowed",
            StatusEffect.StatusEffectType.DEBUFF,
            (int) (durationMs / 50), // Convert to ticks
            (int) (intensity * 10), // Convert to amplifier
            parameters,
            source
        );
        
        combatService.applyStatusEffect(target, slowEffect);
        
        // Apply slowness potion effect
        int amplifier = Math.max(0, Math.min(255, (int) (intensity * 5)));
        target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, (int) (durationMs / 50), amplifier, false, false));
        
        // Visual effects
        showSlowVisuals(target);
    }
    
    /**
     * Creates a ground slam effect with AoE damage and knockback.
     * 
     * @param center the center location
     * @param radius the effect radius
     * @param damage the damage amount
     * @param knockbackStrength the knockback strength
     */
    public void createGroundSlam(@NotNull Location center, double radius, double damage, double knockbackStrength) {
        // Visual effects
        center.getWorld().spawnParticle(Particle.EXPLOSION, center, 1);
        center.getWorld().spawnParticle(Particle.BLOCK, center, 50, radius, 0.1, radius, 
                                       center.getBlock().getBlockData());
        center.getWorld().playSound(center, Sound.ENTITY_GENERIC_EXPLODE, 2.0f, 0.5f);
        
        // Create shockwave particles
        for (int i = 0; i < 360; i += 10) {
            double angle = Math.toRadians(i);
            for (double r = 0; r <= radius; r += 0.5) {
                double x = center.getX() + r * Math.cos(angle);
                double z = center.getZ() + r * Math.sin(angle);
                Location particleLocation = new Location(center.getWorld(), x, center.getY(), z);
                
                center.getWorld().spawnParticle(Particle.DUST, particleLocation, 1,
                    new Particle.DustOptions(org.bukkit.Color.MAROON, 1.0f));
            }
        }
        
        // Apply effects to nearby entities
        center.getWorld().getNearbyLivingEntities(center, radius).forEach(entity -> {
            // Apply damage
            combatService.dealDamage(entity, null, damage, 
                com.ultimatedungeon.udx.combat.DamageType.PHYSICAL, "Ground Slam");
            
            // Apply knockback
            applyKnockback(center, entity, knockbackStrength, true);
        });
    }
    
    // Visual effect methods
    
    private void showRootVisuals(@NotNull LivingEntity target) {
        Location location = target.getLocation();
        
        // Spawn vine-like particles around the target
        for (int i = 0; i < 20; i++) {
            double angle = Math.random() * 2 * Math.PI;
            double radius = 1.0 + Math.random() * 0.5;
            double x = location.getX() + radius * Math.cos(angle);
            double z = location.getZ() + radius * Math.sin(angle);
            Location particleLocation = new Location(location.getWorld(), x, location.getY(), z);
            
            location.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, particleLocation, 1);
        }
        
        location.getWorld().playSound(location, Sound.BLOCK_GRASS_BREAK, 1.0f, 0.8f);
    }
    
    private void showFearVisuals(@NotNull LivingEntity target) {
        Location location = target.getEyeLocation();
        
        // Dark particles around the head
        location.getWorld().spawnParticle(Particle.SMOKE, location, 10, 0.5, 0.5, 0.5, 0.1);
        location.getWorld().playSound(location, Sound.ENTITY_GHAST_SCREAM, 0.5f, 1.5f);
    }
    
    private void showPullVisuals(@NotNull LivingEntity target, @NotNull Location destination) {
        // Create a line of particles from target to destination
        Vector direction = destination.toVector().subtract(target.getLocation().toVector());
        double distance = direction.length();
        direction.normalize();
        
        for (double d = 0; d <= distance; d += 0.5) {
            Location particleLocation = target.getLocation().clone().add(direction.clone().multiply(d));
            target.getWorld().spawnParticle(Particle.END_ROD, particleLocation, 1);
        }
        
        target.getWorld().playSound(target.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 0.5f, 1.2f);
    }
    
    private void showPushVisuals(@NotNull LivingEntity target, @NotNull Location source) {
        // Explosion-like particles at the source
        source.getWorld().spawnParticle(Particle.EXPLOSION, source, 5);
        target.getWorld().playSound(target.getLocation(), Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.5f);
    }
    
    private void showStunVisuals(@NotNull LivingEntity target) {
        Location location = target.getEyeLocation();
        
        // Lightning-like particles
        location.getWorld().spawnParticle(Particle.ELECTRIC_SPARK, location, 15, 0.3, 0.3, 0.3, 0.1);
        location.getWorld().playSound(location, Sound.ENTITY_LIGHTNING_BOLT_IMPACT, 0.3f, 2.0f);
    }
    
    private void showSilenceVisuals(@NotNull LivingEntity target) {
        Location location = target.getEyeLocation();
        
        // Purple particles around the mouth area
        location.getWorld().spawnParticle(Particle.WITCH, location, 10, 0.2, 0.2, 0.2, 0.1);
        location.getWorld().playSound(location, Sound.ENTITY_WITCH_CELEBRATE, 0.5f, 0.8f);
    }
    
    private void showSlowVisuals(@NotNull LivingEntity target) {
        Location location = target.getLocation();
        
        // Blue particles around the feet
        location.getWorld().spawnParticle(Particle.DRIPPING_WATER, location, 15, 0.5, 0.1, 0.5, 0.1);
        location.getWorld().playSound(location, Sound.BLOCK_WATER_AMBIENT, 0.5f, 0.8f);
    }
    
    // Helper method for knockback with location source
    private void applyKnockback(@NotNull Location source, @NotNull LivingEntity target, 
                               double strength, boolean upward) {
        Vector direction = target.getLocation().toVector().subtract(source.toVector());
        direction.setY(0); // Remove Y component for horizontal knockback
        direction.normalize();
        
        if (upward) {
            direction.setY(0.3); // Add upward momentum
        }
        
        direction.multiply(strength);
        target.setVelocity(direction);
    }
}
