package com.ultimatedungeon.udx.api.progression;

import org.jetbrains.annotations.NotNull;
import java.time.Instant;

public record SeasonData(
    int seasonNumber,
    @NotNull String name,
    @NotNull String theme,
    @NotNull Instant startTime,
    @NotNull Instant endTime,
    boolean active
) {
    public SeasonData {
        if (seasonNumber < 1) throw new IllegalArgumentException("Season number must be positive");
        if (name == null || name.isBlank()) throw new IllegalArgumentException("Name cannot be null or blank");
        if (theme == null || theme.isBlank()) throw new IllegalArgumentException("Theme cannot be null or blank");
        if (startTime == null) throw new IllegalArgumentException("Start time cannot be null");
        if (endTime == null) throw new IllegalArgumentException("End time cannot be null");
    }
}
