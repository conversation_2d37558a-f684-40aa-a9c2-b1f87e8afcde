package com.ultimatedungeon.udx.spawner;

import org.bukkit.Material;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.EntityType;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * Defines a custom mob with attributes, equipment, and behaviors.
 * 
 * <p>This class represents a complete mob definition that can be spawned
 * in dungeons with custom properties, equipment, and AI behaviors.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class MobDef {
    
    private final String id;
    private final String displayName;
    private final EntityType entityType;
    private final Map<String, Object> attributes;
    private final Equipment equipment;
    private final List<PotionEffect> potionEffects;
    private final List<String> behaviors;
    private final List<String> abilities;
    private final String lootTableId;
    private final double leashRadius;
    private final TargetingType targeting;
    private final Set<MobFlag> flags;
    private final Map<String, Object> metadata;
    
    private MobDef(@NotNull Builder builder) {
        this.id = builder.id;
        this.displayName = builder.displayName;
        this.entityType = builder.entityType;
        this.attributes = Map.copyOf(builder.attributes);
        this.equipment = builder.equipment;
        this.potionEffects = List.copyOf(builder.potionEffects);
        this.behaviors = List.copyOf(builder.behaviors);
        this.abilities = List.copyOf(builder.abilities);
        this.lootTableId = builder.lootTableId;
        this.leashRadius = builder.leashRadius;
        this.targeting = builder.targeting;
        this.flags = Set.copyOf(builder.flags);
        this.metadata = new HashMap<>(builder.metadata);
    }
    
    /**
     * Gets the mob ID.
     * 
     * @return the mob ID
     */
    @NotNull
    public String getId() {
        return id;
    }
    
    /**
     * Gets the display name.
     * 
     * @return the display name
     */
    @NotNull
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the entity type.
     * 
     * @return the entity type
     */
    @NotNull
    public EntityType getEntityType() {
        return entityType;
    }
    
    /**
     * Gets the attributes map.
     * 
     * @return map of attribute names to values
     */
    @NotNull
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    /**
     * Gets an attribute value.
     * 
     * @param attribute the attribute
     * @return the value, or null if not set
     */
    @Nullable
    public Double getAttribute(@NotNull Attribute attribute) {
        return getAttribute(attribute.toString());
    }
    
    /**
     * Gets an attribute value by name.
     * 
     * @param name the attribute name
     * @return the value, or null if not set
     */
    @Nullable
    public Double getAttribute(@NotNull String name) {
        Object value = attributes.get(name);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return null;
    }
    
    /**
     * Gets the equipment.
     * 
     * @return the equipment
     */
    @NotNull
    public Equipment getEquipment() {
        return equipment;
    }
    
    /**
     * Gets the potion effects.
     * 
     * @return list of potion effects
     */
    @NotNull
    public List<PotionEffect> getPotionEffects() {
        return potionEffects;
    }
    
    /**
     * Gets the behavior IDs.
     * 
     * @return list of behavior IDs
     */
    @NotNull
    public List<String> getBehaviors() {
        return behaviors;
    }
    
    /**
     * Gets the ability IDs.
     * 
     * @return list of ability IDs
     */
    @NotNull
    public List<String> getAbilities() {
        return abilities;
    }
    
    /**
     * Gets the loot table ID.
     * 
     * @return the loot table ID, or null if none
     */
    @Nullable
    public String getLootTableId() {
        return lootTableId;
    }
    
    /**
     * Gets the leash radius.
     * 
     * @return the leash radius in blocks
     */
    public double getLeashRadius() {
        return leashRadius;
    }
    
    /**
     * Gets the targeting type.
     * 
     * @return the targeting type
     */
    @NotNull
    public TargetingType getTargeting() {
        return targeting;
    }
    
    /**
     * Gets the mob flags.
     * 
     * @return set of flags
     */
    @NotNull
    public Set<MobFlag> getFlags() {
        return flags;
    }
    
    /**
     * Checks if a flag is set.
     * 
     * @param flag the flag to check
     * @return true if the flag is set
     */
    public boolean hasFlag(@NotNull MobFlag flag) {
        return flags.contains(flag);
    }
    
    /**
     * Gets metadata value.
     * 
     * @param key the metadata key
     * @return the value, or null if not found
     */
    @Nullable
    public Object getMetadata(@NotNull String key) {
        return metadata.get(key);
    }
    
    /**
     * Gets all metadata.
     * 
     * @return copy of metadata map
     */
    @NotNull
    public Map<String, Object> getAllMetadata() {
        return new HashMap<>(metadata);
    }
    
    /**
     * Creates a new builder.
     * 
     * @param id the mob ID
     * @param entityType the entity type
     * @return new builder instance
     */
    @NotNull
    public static Builder builder(@NotNull String id, @NotNull EntityType entityType) {
        return new Builder(id, entityType);
    }
    
    /**
     * Creates a builder from existing mob def.
     * 
     * @param mobDef the mob def to copy
     * @return new builder with copied values
     */
    @NotNull
    public static Builder builder(@NotNull MobDef mobDef) {
        return new Builder(mobDef.id, mobDef.entityType)
            .displayName(mobDef.displayName)
            .equipment(mobDef.equipment)
            .potionEffects(mobDef.potionEffects)
            .behaviors(mobDef.behaviors)
            .abilities(mobDef.abilities)
            .lootTable(mobDef.lootTableId)
            .leashRadius(mobDef.leashRadius)
            .targeting(mobDef.targeting)
            .flags(mobDef.flags);
    }
    
    /**
     * Mob equipment definition.
     */
    public static final class Equipment {
        private final ItemStack helmet;
        private final ItemStack chestplate;
        private final ItemStack leggings;
        private final ItemStack boots;
        private final ItemStack mainHand;
        private final ItemStack offHand;
        private final Map<String, Float> dropChances;
        
        public Equipment(@Nullable ItemStack helmet, @Nullable ItemStack chestplate, 
                        @Nullable ItemStack leggings, @Nullable ItemStack boots,
                        @Nullable ItemStack mainHand, @Nullable ItemStack offHand,
                        @NotNull Map<String, Float> dropChances) {
            this.helmet = helmet;
            this.chestplate = chestplate;
            this.leggings = leggings;
            this.boots = boots;
            this.mainHand = mainHand;
            this.offHand = offHand;
            this.dropChances = Map.copyOf(dropChances);
        }
        
        @Nullable public ItemStack getHelmet() { return helmet; }
        @Nullable public ItemStack getChestplate() { return chestplate; }
        @Nullable public ItemStack getLeggings() { return leggings; }
        @Nullable public ItemStack getBoots() { return boots; }
        @Nullable public ItemStack getMainHand() { return mainHand; }
        @Nullable public ItemStack getOffHand() { return offHand; }
        @NotNull public Map<String, Float> getDropChances() { return dropChances; }
        
        public float getDropChance(@NotNull String slot) {
            return dropChances.getOrDefault(slot, 0.0f);
        }
        
        public static Equipment empty() {
            return new Equipment(null, null, null, null, null, null, Map.of());
        }
    }
    
    /**
     * Targeting behavior types.
     */
    public enum TargetingType {
        NEAREST,
        RANDOM,
        LOWEST_HEALTH,
        HIGHEST_HEALTH,
        TANK,
        HEALER,
        DAMAGE_DEALER,
        LAST_ATTACKER
    }
    
    /**
     * Mob behavior flags.
     */
    public enum MobFlag {
        FIRE_IMMUNE,
        KNOCKBACK_RESISTANT,
        SILENT,
        PERSISTENT,
        NO_AI,
        INVULNERABLE,
        GLOWING,
        INVISIBLE,
        NO_GRAVITY,
        CUSTOM_NAME_VISIBLE
    }
    
    /**
     * Builder for mob definitions.
     */
    public static final class Builder {
        private final String id;
        private final EntityType entityType;
        private String displayName;
        private final Map<String, Object> attributes = new HashMap<>();
        private Equipment equipment = Equipment.empty();
        private final List<PotionEffect> potionEffects = new ArrayList<>();
        private final List<String> behaviors = new ArrayList<>();
        private final List<String> abilities = new ArrayList<>();
        private String lootTableId;
        private double leashRadius = 16.0;
        private TargetingType targeting = TargetingType.NEAREST;
        private final Set<MobFlag> flags = EnumSet.noneOf(MobFlag.class);
        private final Map<String, Object> metadata = new HashMap<>();
        
        private Builder(@NotNull String id, @NotNull EntityType entityType) {
            this.id = id;
            this.entityType = entityType;
            this.displayName = id;
        }
        
        @NotNull
        public Builder displayName(@NotNull String displayName) {
            this.displayName = displayName;
            return this;
        }
        
        @NotNull
        public Builder attribute(@NotNull Attribute attribute, double value) {
            return attribute(attribute.toString(), value);
        }
        
        @NotNull
        public Builder attribute(@NotNull String name, double value) {
            this.attributes.put(name, value);
            return this;
        }
        
        @NotNull
        public Builder equipment(@NotNull Equipment equipment) {
            this.equipment = equipment;
            return this;
        }
        
        @NotNull
        public Builder potionEffect(@NotNull PotionEffect effect) {
            this.potionEffects.add(effect);
            return this;
        }
        
        @NotNull
        public Builder potionEffects(@NotNull Collection<PotionEffect> effects) {
            this.potionEffects.addAll(effects);
            return this;
        }
        
        @NotNull
        public Builder behavior(@NotNull String behaviorId) {
            this.behaviors.add(behaviorId);
            return this;
        }
        
        @NotNull
        public Builder behaviors(@NotNull Collection<String> behaviorIds) {
            this.behaviors.addAll(behaviorIds);
            return this;
        }
        
        @NotNull
        public Builder ability(@NotNull String abilityId) {
            this.abilities.add(abilityId);
            return this;
        }
        
        @NotNull
        public Builder abilities(@NotNull Collection<String> abilityIds) {
            this.abilities.addAll(abilityIds);
            return this;
        }
        
        @NotNull
        public Builder lootTable(@Nullable String lootTableId) {
            this.lootTableId = lootTableId;
            return this;
        }
        
        @NotNull
        public Builder leashRadius(double radius) {
            this.leashRadius = radius;
            return this;
        }
        
        @NotNull
        public Builder targeting(@NotNull TargetingType targeting) {
            this.targeting = targeting;
            return this;
        }
        
        @NotNull
        public Builder flag(@NotNull MobFlag flag) {
            this.flags.add(flag);
            return this;
        }
        
        @NotNull
        public Builder flags(@NotNull Collection<MobFlag> flags) {
            this.flags.addAll(flags);
            return this;
        }
        
        @NotNull
        public Builder metadata(@NotNull String key, @Nullable Object value) {
            if (value == null) {
                this.metadata.remove(key);
            } else {
                this.metadata.put(key, value);
            }
            return this;
        }
        
        @NotNull
        public MobDef build() {
            return new MobDef(this);
        }
    }
    
    @Override
    public String toString() {
        return "MobDef{" +
               "id='" + id + '\'' +
               ", displayName='" + displayName + '\'' +
               ", entityType=" + entityType +
               ", behaviors=" + behaviors.size() +
               ", abilities=" + abilities.size() +
               '}';
    }
}
