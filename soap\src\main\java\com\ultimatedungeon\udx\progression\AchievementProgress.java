package com.ultimatedungeon.udx.progression;

import org.jetbrains.annotations.NotNull;

import java.time.Instant;
import java.util.Objects;

/**
 * Tracks progress towards completing an achievement.
 * 
 * <p>This class maintains the current progress value, target value,
 * and completion status for a specific achievement.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class AchievementProgress {
    
    private final String achievementId;
    private int progress;
    private final int target;
    private boolean completed;
    private Instant startedAt;
    private Instant completedAt;
    
    public AchievementProgress(@NotNull String achievementId, int target) {
        this.achievementId = achievementId;
        this.progress = 0;
        this.target = target;
        this.completed = false;
        this.startedAt = Instant.now();
    }
    
    /**
     * Sets the current progress value.
     * 
     * @param progress the progress value
     */
    public void setProgress(int progress) {
        this.progress = Math.max(0, Math.min(progress, target));
        
        // Check for completion
        if (this.progress >= target && !completed) {
            completed = true;
            completedAt = Instant.now();
        }
    }
    
    /**
     * Increments the progress by the specified amount.
     * 
     * @param amount the amount to increment
     */
    public void incrementProgress(int amount) {
        setProgress(progress + amount);
    }
    
    /**
     * Gets the completion percentage (0.0 to 1.0).
     * 
     * @return the completion percentage
     */
    public double getCompletionPercentage() {
        if (target <= 0) return 1.0;
        return Math.min(1.0, (double) progress / target);
    }
    
    /**
     * Checks if the achievement is completed.
     * 
     * @return true if completed, false otherwise
     */
    public boolean isCompleted() {
        return completed || progress >= target;
    }
    
    // Getters
    
    @NotNull
    public String getAchievementId() {
        return achievementId;
    }
    
    public int getProgress() {
        return progress;
    }
    
    public int getTarget() {
        return target;
    }
    
    @NotNull
    public Instant getStartedAt() {
        return startedAt;
    }
    
    public Instant getCompletedAt() {
        return completedAt;
    }
    
    // Setters for data loading
    
    public void setCompleted(boolean completed) {
        this.completed = completed;
    }
    
    public void setStartedAt(@NotNull Instant startedAt) {
        this.startedAt = startedAt;
    }
    
    public void setCompletedAt(Instant completedAt) {
        this.completedAt = completedAt;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        AchievementProgress that = (AchievementProgress) obj;
        return Objects.equals(achievementId, that.achievementId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(achievementId);
    }
    
    @Override
    public String toString() {
        return "AchievementProgress{" +
            "achievementId='" + achievementId + '\'' +
            ", progress=" + progress +
            ", target=" + target +
            ", completed=" + completed +
            '}';
    }
}
