# UltimateDungeonX API Documentation

This document provides comprehensive information for developers who want to create addons or integrate with UltimateDungeonX.

## Overview

The UltimateDungeonX API provides a stable, well-documented interface for:

- **Event Handling**: Listen to dungeon lifecycle events
- **Service Access**: Interact with core plugin services
- **Data Models**: Access immutable data structures
- **Custom Content**: Register custom dungeons, mobs, and mechanics
- **Integration**: Hook into existing systems safely

## Getting Started

### Maven Dependency

```xml
<dependency>
    <groupId>com.ultimatedungeon</groupId>
    <artifactId>UltimateDungeonX</artifactId>
    <version>1.0.0</version>
    <scope>provided</scope>
</dependency>
```

### Gradle Dependency

```kotlin
dependencies {
    compileOnly("com.ultimatedungeon:UltimateDungeonX:1.0.0")
}
```

### Basic Setup

```java
public class MyAddon extends JavaPlugin {
    
    private UltimateDungeonXAPI udxAPI;
    
    @Override
    public void onEnable() {
        // Get API instance
        udxAPI = UltimateDungeonXAPI.getInstance();
        
        if (udxAPI == null) {
            getLogger().severe("UltimateDungeonX not found! Disabling addon.");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        
        // Register event listeners
        getServer().getPluginManager().registerEvents(new DungeonListener(), this);
        
        getLogger().info("MyAddon enabled with UDX API v" + udxAPI.getVersion());
    }
}
```

## Core API Classes

### UltimateDungeonXAPI

The main entry point for all API interactions.

```java
public class UltimateDungeonXAPI {
    
    /**
     * Get the API instance
     * @return API instance or null if UDX is not loaded
     */
    public static UltimateDungeonXAPI getInstance();
    
    /**
     * Get API version for compatibility checking
     * @return API version string
     */
    public String getVersion();
    
    /**
     * Access core services
     */
    public DungeonService getDungeonService();
    public PartyService getPartyService();
    public ProgressionService getProgressionService();
    public LootService getLootService();
    
    /**
     * Check if a feature is available
     * @param feature Feature name
     * @return true if available
     */
    public boolean isFeatureAvailable(String feature);
}
```

## Service Interfaces

### DungeonService

Manage dungeons and instances.

```java
public interface DungeonService {
    
    /**
     * Get all available dungeons
     * @return Collection of dungeon definitions
     */
    Collection<DungeonDefinition> getAllDungeons();
    
    /**
     * Get dungeon by ID
     * @param dungeonId Dungeon identifier
     * @return Dungeon definition or null if not found
     */
    @Nullable
    DungeonDefinition getDungeon(String dungeonId);
    
    /**
     * Get active instances
     * @return Collection of active dungeon instances
     */
    Collection<DungeonInstance> getActiveInstances();
    
    /**
     * Get instance by ID
     * @param instanceId Instance identifier
     * @return Instance or null if not found
     */
    @Nullable
    DungeonInstance getInstance(UUID instanceId);
    
    /**
     * Get instance containing a player
     * @param player Player to check
     * @return Instance or null if player not in dungeon
     */
    @Nullable
    DungeonInstance getPlayerInstance(Player player);
    
    /**
     * Create a new dungeon instance
     * @param dungeonId Dungeon to instantiate
     * @param party Party to create instance for
     * @param difficulty Difficulty tier
     * @return CompletableFuture with instance or error
     */
    CompletableFuture<DungeonInstance> createInstance(
        String dungeonId, 
        PartyData party, 
        DifficultyTier difficulty
    );
    
    /**
     * Stop a dungeon instance
     * @param instanceId Instance to stop
     * @param reason Reason for stopping
     * @return CompletableFuture that completes when stopped
     */
    CompletableFuture<Void> stopInstance(UUID instanceId, String reason);
    
    /**
     * Register a custom dungeon
     * @param definition Dungeon definition
     * @throws IllegalArgumentException if invalid
     */
    void registerDungeon(DungeonDefinition definition);
    
    /**
     * Unregister a custom dungeon
     * @param dungeonId Dungeon ID to remove
     * @return true if removed, false if not found
     */
    boolean unregisterDungeon(String dungeonId);
}
```

### PartyService

Manage parties and matchmaking.

```java
public interface PartyService {
    
    /**
     * Get party containing a player
     * @param player Player to check
     * @return Party data or null if not in party
     */
    @Nullable
    PartyData getPlayerParty(Player player);
    
    /**
     * Create a new party
     * @param leader Party leader
     * @return Created party data
     */
    PartyData createParty(Player leader);
    
    /**
     * Invite player to party
     * @param party Target party
     * @param inviter Player sending invite
     * @param target Player being invited
     * @return CompletableFuture with result
     */
    CompletableFuture<Boolean> invitePlayer(PartyData party, Player inviter, Player target);
    
    /**
     * Remove player from party
     * @param party Target party
     * @param player Player to remove
     * @param reason Reason for removal
     * @return true if removed successfully
     */
    boolean removePlayer(PartyData party, Player player, String reason);
    
    /**
     * Disband a party
     * @param party Party to disband
     * @param reason Reason for disbanding
     */
    void disbandParty(PartyData party, String reason);
    
    /**
     * Get current queue status
     * @param party Party to check
     * @return Queue data or null if not queued
     */
    @Nullable
    QueueData getQueueStatus(PartyData party);
    
    /**
     * Add party to dungeon queue
     * @param party Party to queue
     * @param dungeonId Target dungeon
     * @param difficulty Desired difficulty
     * @return CompletableFuture with queue result
     */
    CompletableFuture<QueueData> queueParty(PartyData party, String dungeonId, DifficultyTier difficulty);
    
    /**
     * Remove party from queue
     * @param party Party to dequeue
     * @return true if removed from queue
     */
    boolean dequeueParty(PartyData party);
}
```

### ProgressionService

Handle player progression and achievements.

```java
public interface ProgressionService {
    
    /**
     * Get player profile
     * @param playerId Player UUID
     * @return Player profile data
     */
    CompletableFuture<PlayerProfileData> getPlayerProfile(UUID playerId);
    
    /**
     * Get dungeon progress for player
     * @param playerId Player UUID
     * @param dungeonId Dungeon ID
     * @return Progress data or null if never attempted
     */
    CompletableFuture<DungeonProgressData> getDungeonProgress(UUID playerId, String dungeonId);
    
    /**
     * Update dungeon progress
     * @param playerId Player UUID
     * @param dungeonId Dungeon ID
     * @param progress New progress data
     * @return CompletableFuture that completes when saved
     */
    CompletableFuture<Void> updateDungeonProgress(UUID playerId, String dungeonId, DungeonProgressData progress);
    
    /**
     * Get achievement progress
     * @param playerId Player UUID
     * @param achievementId Achievement ID
     * @return Achievement progress or null if not started
     */
    CompletableFuture<AchievementProgressData> getAchievementProgress(UUID playerId, String achievementId);
    
    /**
     * Award achievement to player
     * @param playerId Player UUID
     * @param achievementId Achievement ID
     * @return CompletableFuture with success status
     */
    CompletableFuture<Boolean> awardAchievement(UUID playerId, String achievementId);
    
    /**
     * Get leaderboard entries
     * @param dungeonId Dungeon ID (null for global)
     * @param difficulty Difficulty tier (null for all)
     * @param limit Maximum entries to return
     * @return List of leaderboard entries
     */
    CompletableFuture<List<LeaderboardEntry>> getLeaderboard(
        @Nullable String dungeonId, 
        @Nullable DifficultyTier difficulty, 
        int limit
    );
    
    /**
     * Get current season data
     * @return Current season information
     */
    SeasonData getCurrentSeason();
    
    /**
     * Get player's season stats
     * @param playerId Player UUID
     * @param seasonId Season ID (null for current)
     * @return Season statistics
     */
    CompletableFuture<SeasonStatsData> getSeasonStats(UUID playerId, @Nullable String seasonId);
}
```

### LootService

Handle loot generation and rewards.

```java
public interface LootService {
    
    /**
     * Get loot table by ID
     * @param tableId Table identifier
     * @return Loot table data or null if not found
     */
    @Nullable
    LootTableData getLootTable(String tableId);
    
    /**
     * Generate loot from table
     * @param tableId Table to roll
     * @param player Player receiving loot (for context)
     * @param modifiers Loot modifiers to apply
     * @return List of generated items
     */
    List<ItemStack> generateLoot(String tableId, Player player, LootModifiers modifiers);
    
    /**
     * Generate loot with custom parameters
     * @param table Loot table to use
     * @param rolls Number of rolls to make
     * @param player Player context
     * @param modifiers Loot modifiers
     * @return Generated loot items
     */
    List<ItemStack> generateLoot(LootTableData table, int rolls, Player player, LootModifiers modifiers);
    
    /**
     * Get drop rate information for a table
     * @param tableId Table to analyze
     * @return Drop rate information
     */
    List<DropRateInfo> getDropRates(String tableId);
    
    /**
     * Get player's loot history
     * @param playerId Player UUID
     * @param limit Maximum entries to return
     * @return List of loot history entries
     */
    CompletableFuture<List<LootHistoryEntry>> getLootHistory(UUID playerId, int limit);
    
    /**
     * Register a custom loot table
     * @param table Loot table to register
     * @throws IllegalArgumentException if invalid
     */
    void registerLootTable(LootTableData table);
    
    /**
     * Unregister a loot table
     * @param tableId Table ID to remove
     * @return true if removed, false if not found
     */
    boolean unregisterLootTable(String tableId);
}
```

## Event System

UltimateDungeonX fires comprehensive events for all major actions.

### Dungeon Events

```java
/**
 * Fired when a dungeon instance is created
 */
public class DungeonCreateEvent extends Event implements Cancellable {
    public DungeonDefinition getDungeon();
    public PartyData getParty();
    public DifficultyTier getDifficulty();
    public UUID getInstanceId();
    
    // Cancellable
    public boolean isCancelled();
    public void setCancelled(boolean cancelled);
    public void setCancelReason(String reason);
}

/**
 * Fired when a dungeon starts (players enter)
 */
public class DungeonStartEvent extends Event {
    public DungeonInstance getInstance();
    public List<Player> getPlayers();
    public long getStartTime();
}

/**
 * Fired when a dungeon is completed successfully
 */
public class DungeonCompleteEvent extends Event {
    public DungeonInstance getInstance();
    public List<Player> getPlayers();
    public long getDuration();
    public Map<String, Object> getStatistics();
    public List<ItemStack> getRewards();
}

/**
 * Fired when a dungeon fails or is abandoned
 */
public class DungeonFailEvent extends Event {
    public DungeonInstance getInstance();
    public List<Player> getPlayers();
    public String getFailureReason();
    public long getDuration();
}

/**
 * Fired when a dungeon instance is disposed
 */
public class DungeonDisposeEvent extends Event {
    public UUID getInstanceId();
    public String getDungeonId();
    public String getDisposeReason();
}
```

### Boss Events

```java
/**
 * Fired when a boss spawns
 */
public class BossSpawnEvent extends Event implements Cancellable {
    public DungeonInstance getInstance();
    public String getBossId();
    public Location getSpawnLocation();
    public LivingEntity getBossEntity();
}

/**
 * Fired when a boss changes phases
 */
public class BossPhaseChangeEvent extends Event {
    public DungeonInstance getInstance();
    public LivingEntity getBoss();
    public String getPreviousPhase();
    public String getNewPhase();
    public double getHealthPercentage();
}

/**
 * Fired when a boss uses an ability
 */
public class BossAbilityEvent extends Event implements Cancellable {
    public DungeonInstance getInstance();
    public LivingEntity getBoss();
    public String getAbilityId();
    public List<Player> getTargets();
    
    public void setTargets(List<Player> targets);
}

/**
 * Fired when a boss is defeated
 */
public class BossDefeatedEvent extends Event {
    public DungeonInstance getInstance();
    public LivingEntity getBoss();
    public List<Player> getParticipants();
    public long getFightDuration();
}
```

### Party Events

```java
/**
 * Fired when a party is created
 */
public class PartyCreateEvent extends Event {
    public PartyData getParty();
    public Player getLeader();
}

/**
 * Fired when a player joins a party
 */
public class PartyJoinEvent extends Event implements Cancellable {
    public PartyData getParty();
    public Player getPlayer();
    public Player getInviter();
}

/**
 * Fired when a player leaves a party
 */
public class PartyLeaveEvent extends Event {
    public PartyData getParty();
    public Player getPlayer();
    public String getLeaveReason();
}

/**
 * Fired when a party is disbanded
 */
public class PartyDisbandEvent extends Event {
    public PartyData getParty();
    public String getDisbandReason();
}
```

### Loot Events

```java
/**
 * Fired when loot is generated
 */
public class LootGenerateEvent extends Event implements Cancellable {
    public String getTableId();
    public Player getPlayer();
    public List<ItemStack> getLoot();
    public LootModifiers getModifiers();
    
    public void setLoot(List<ItemStack> loot);
    public void addLoot(ItemStack item);
    public void removeLoot(ItemStack item);
}

/**
 * Fired when a player receives loot
 */
public class LootReceiveEvent extends Event {
    public Player getPlayer();
    public List<ItemStack> getLoot();
    public String getSource(); // "chest", "boss", "completion", etc.
    public DungeonInstance getInstance();
}
```

### Achievement Events

```java
/**
 * Fired when a player earns an achievement
 */
public class AchievementEarnEvent extends Event implements Cancellable {
    public Player getPlayer();
    public String getAchievementId();
    public AchievementData getAchievement();
    public String getTriggerReason();
}

/**
 * Fired when achievement progress is updated
 */
public class AchievementProgressEvent extends Event {
    public Player getPlayer();
    public String getAchievementId();
    public int getPreviousProgress();
    public int getNewProgress();
    public int getRequiredProgress();
}
```

## Data Models

All data models are immutable records for thread safety and consistency.

### DungeonDefinition

```java
public record DungeonDefinition(
    String id,
    String name,
    String description,
    List<String> lore,
    String theme,
    String environment,
    int difficultyBase,
    int lengthMin,
    int lengthMax,
    int branchingFactor,
    LevelRequirement levelRequirement,
    PartyConfiguration partyConfig,
    Map<DifficultyTier, DifficultyScaling> difficultyTiers,
    List<String> allowedAffixes,
    Map<String, String> lootTables,
    List<Objective> objectives,
    RewardConfiguration rewards,
    EnvironmentConfiguration environment,
    Map<String, Object> metadata
) {
    // Validation methods
    public boolean isValidForPlayer(Player player);
    public boolean isValidForParty(PartyData party);
    public DifficultyScaling getScaling(DifficultyTier tier);
}
```

### DungeonInstance

```java
public record DungeonInstance(
    UUID instanceId,
    String dungeonId,
    DungeonInstanceState state,
    PartyData party,
    DifficultyTier difficulty,
    World world,
    Instant createdAt,
    Instant startedAt,
    Instant completedAt,
    Map<String, Object> statistics,
    List<String> activeAffixes,
    DungeonPerformanceStats performanceStats
) {
    // Utility methods
    public boolean isActive();
    public Duration getDuration();
    public List<Player> getPlayers();
    public boolean containsPlayer(Player player);
}
```

### PartyData

```java
public record PartyData(
    UUID partyId,
    UUID leaderId,
    List<UUID> memberIds,
    Instant createdAt,
    Map<String, Object> settings,
    PartyState state
) {
    // Utility methods
    public List<Player> getOnlineMembers();
    public boolean isLeader(Player player);
    public boolean isMember(Player player);
    public int getSize();
}
```

### PlayerProfileData

```java
public record PlayerProfileData(
    UUID playerId,
    String lastKnownName,
    Instant firstJoin,
    Instant lastSeen,
    int totalRuns,
    int completedRuns,
    Map<String, DungeonProgressData> dungeonProgress,
    Map<String, AchievementProgressData> achievementProgress,
    Map<String, SeasonStatsData> seasonStats,
    PlayerStatistics overallStats
) {
    // Utility methods
    public double getCompletionRate();
    public List<String> getUnlockedDungeons();
    public List<String> getEarnedAchievements();
}
```

## Custom Content Registration

### Registering Custom Dungeons

```java
public class CustomDungeonAddon extends JavaPlugin {
    
    @Override
    public void onEnable() {
        UltimateDungeonXAPI api = UltimateDungeonXAPI.getInstance();
        
        // Create custom dungeon definition
        DungeonDefinition customDungeon = DungeonDefinition.builder()
            .id("my_addon:custom_dungeon")
            .name("Custom Dungeon")
            .description("A dungeon created by my addon")
            .theme("custom")
            .difficultyBase(5)
            .lengthMin(5)
            .lengthMax(8)
            .levelRequirement(new LevelRequirement(30, 60, 45))
            .partyConfig(new PartyConfiguration(2, 4, 3, true))
            .build();
            
        // Register with UDX
        api.getDungeonService().registerDungeon(customDungeon);
        
        getLogger().info("Registered custom dungeon: " + customDungeon.id());
    }
    
    @Override
    public void onDisable() {
        UltimateDungeonXAPI api = UltimateDungeonXAPI.getInstance();
        if (api != null) {
            // Clean up registered content
            api.getDungeonService().unregisterDungeon("my_addon:custom_dungeon");
        }
    }
}
```

### Custom Loot Tables

```java
public void registerCustomLoot() {
    UltimateDungeonXAPI api = UltimateDungeonXAPI.getInstance();
    
    // Create loot entries
    List<LootEntryData> entries = List.of(
        LootEntryData.builder()
            .id("custom_sword")
            .type(LootEntryType.ITEM)
            .weight(10.0)
            .rarity(ItemRarity.RARE)
            .item(new ItemStack(Material.DIAMOND_SWORD))
            .build(),
        LootEntryData.builder()
            .id("bonus_currency")
            .type(LootEntryType.CURRENCY)
            .weight(20.0)
            .currencyAmount(100.0)
            .build()
    );
    
    // Create loot table
    LootTableData customTable = LootTableData.builder()
        .id("my_addon:custom_loot")
        .name("Custom Loot Table")
        .minRolls(1)
        .maxRolls(3)
        .entries(entries)
        .build();
        
    // Register with UDX
    api.getLootService().registerLootTable(customTable);
}
```

## Integration Examples

### Listening to Events

```java
public class DungeonListener implements Listener {
    
    @EventHandler
    public void onDungeonComplete(DungeonCompleteEvent event) {
        DungeonInstance instance = event.getInstance();
        List<Player> players = event.getPlayers();
        
        // Award custom rewards
        for (Player player : players) {
            // Give custom currency
            EconomyAPI.addMoney(player, 500);
            
            // Send custom message
            player.sendMessage("§aYou completed " + instance.dungeonId() + "!");
            
            // Trigger custom effects
            player.getWorld().spawnParticle(Particle.FIREWORKS_SPARK, 
                player.getLocation(), 50, 1, 1, 1, 0.1);
        }
    }
    
    @EventHandler
    public void onBossPhaseChange(BossPhaseChangeEvent event) {
        String newPhase = event.getNewPhase();
        DungeonInstance instance = event.getInstance();
        
        // Announce phase change to all players
        for (Player player : instance.getPlayers()) {
            player.sendTitle("§cPhase " + newPhase, 
                "§7The boss grows stronger!", 10, 40, 10);
        }
        
        // Log for analytics
        getLogger().info("Boss phase change in " + instance.instanceId() + 
            ": " + event.getPreviousPhase() + " -> " + newPhase);
    }
    
    @EventHandler
    public void onLootGenerate(LootGenerateEvent event) {
        Player player = event.getPlayer();
        
        // Add bonus loot for VIP players
        if (player.hasPermission("vip.bonus_loot")) {
            ItemStack bonusItem = new ItemStack(Material.DIAMOND, 2);
            event.addLoot(bonusItem);
        }
        
        // Modify existing loot based on player stats
        List<ItemStack> loot = event.getLoot();
        for (ItemStack item : loot) {
            if (item.getType() == Material.IRON_SWORD) {
                // Upgrade iron swords to diamond for high-level players
                if (player.getLevel() >= 50) {
                    item.setType(Material.DIAMOND_SWORD);
                }
            }
        }
    }
}
```

### Accessing Player Data

```java
public class PlayerStatsCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("§cThis command is for players only!");
            return true;
        }
        
        UltimateDungeonXAPI api = UltimateDungeonXAPI.getInstance();
        ProgressionService progressionService = api.getProgressionService();
        
        // Get player profile asynchronously
        progressionService.getPlayerProfile(player.getUniqueId())
            .thenAccept(profile -> {
                // Send stats to player
                player.sendMessage("§6=== Dungeon Statistics ===");
                player.sendMessage("§7Total Runs: §f" + profile.totalRuns());
                player.sendMessage("§7Completed: §f" + profile.completedRuns());
                player.sendMessage("§7Success Rate: §f" + 
                    String.format("%.1f%%", profile.getCompletionRate() * 100));
                
                // Show unlocked dungeons
                List<String> unlocked = profile.getUnlockedDungeons();
                player.sendMessage("§7Unlocked Dungeons: §f" + unlocked.size());
                
                // Show achievements
                List<String> achievements = profile.getEarnedAchievements();
                player.sendMessage("§7Achievements: §f" + achievements.size());
            })
            .exceptionally(throwable -> {
                player.sendMessage("§cError loading your statistics!");
                throwable.printStackTrace();
                return null;
            });
            
        return true;
    }
}
```

### Creating Custom Mechanics

```java
public class CustomMechanics implements Listener {
    
    private final Map<UUID, Long> lastTeleport = new HashMap<>();
    
    @EventHandler
    public void onDungeonStart(DungeonStartEvent event) {
        DungeonInstance instance = event.getInstance();
        
        // Add custom mechanic for specific dungeon
        if ("my_addon:teleport_dungeon".equals(instance.dungeonId())) {
            // Give all players teleport items
            ItemStack teleportItem = new ItemStack(Material.ENDER_PEARL);
            ItemMeta meta = teleportItem.getItemMeta();
            meta.setDisplayName("§bTeleport Crystal");
            meta.setLore(List.of("§7Right-click to teleport to spawn"));
            teleportItem.setItemMeta(meta);
            
            for (Player player : event.getPlayers()) {
                player.getInventory().addItem(teleportItem);
            }
        }
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null || !item.hasItemMeta()) return;
        
        // Check for custom teleport item
        if ("§bTeleport Crystal".equals(item.getItemMeta().getDisplayName())) {
            event.setCancelled(true);
            
            // Check cooldown
            UUID playerId = player.getUniqueId();
            long now = System.currentTimeMillis();
            long lastUse = lastTeleport.getOrDefault(playerId, 0L);
            
            if (now - lastUse < 10000) { // 10 second cooldown
                player.sendMessage("§cTeleport on cooldown!");
                return;
            }
            
            // Get dungeon instance
            UltimateDungeonXAPI api = UltimateDungeonXAPI.getInstance();
            DungeonInstance instance = api.getDungeonService().getPlayerInstance(player);
            
            if (instance != null) {
                // Teleport to spawn location
                Location spawn = instance.world().getSpawnLocation();
                player.teleport(spawn);
                player.sendMessage("§aTeleported to spawn!");
                
                // Update cooldown
                lastTeleport.put(playerId, now);
                
                // Consume item
                item.setAmount(item.getAmount() - 1);
            }
        }
    }
}
```

## Best Practices

### Thread Safety

- All API calls are thread-safe
- Use CompletableFuture for async operations
- Don't block the main thread with database operations

```java
// Good: Async operation
api.getProgressionService().getPlayerProfile(playerId)
    .thenAccept(profile -> {
        // Handle result on main thread
        Bukkit.getScheduler().runTask(plugin, () -> {
            // Update GUI or send messages
        });
    });

// Bad: Blocking main thread
PlayerProfileData profile = api.getProgressionService()
    .getPlayerProfile(playerId).join(); // DON'T DO THIS
```

### Error Handling

- Always handle CompletableFuture exceptions
- Check for null returns from optional methods
- Validate input parameters

```java
api.getDungeonService().createInstance(dungeonId, party, difficulty)
    .thenAccept(instance -> {
        // Success
        logger.info("Created instance: " + instance.instanceId());
    })
    .exceptionally(throwable -> {
        // Handle error
        logger.severe("Failed to create instance: " + throwable.getMessage());
        return null;
    });
```

### Resource Management

- Unregister custom content in onDisable()
- Don't hold references to UDX objects after disable
- Use weak references for caching if needed

```java
@Override
public void onDisable() {
    // Clean up registered content
    UltimateDungeonXAPI api = UltimateDungeonXAPI.getInstance();
    if (api != null) {
        api.getDungeonService().unregisterDungeon("my_addon:custom_dungeon");
        api.getLootService().unregisterLootTable("my_addon:custom_loot");
    }
    
    // Clear caches
    playerCache.clear();
}
```

### Version Compatibility

- Check API version on startup
- Handle missing features gracefully
- Use feature flags for optional functionality

```java
@Override
public void onEnable() {
    UltimateDungeonXAPI api = UltimateDungeonXAPI.getInstance();
    
    if (api == null) {
        getLogger().severe("UltimateDungeonX not found!");
        getServer().getPluginManager().disablePlugin(this);
        return;
    }
    
    // Check version compatibility
    String version = api.getVersion();
    if (!isCompatibleVersion(version)) {
        getLogger().warning("UDX version " + version + " may not be compatible!");
    }
    
    // Check for optional features
    if (api.isFeatureAvailable("custom_mechanics")) {
        enableCustomMechanics();
    }
}

private boolean isCompatibleVersion(String version) {
    // Parse version and check compatibility
    return version.startsWith("1.0");
}
```

## Migration Guide

### From Version 1.0 to 1.1

- `DungeonService.createInstance()` now returns `CompletableFuture<DungeonInstance>`
- `PartyData` record structure changed - use new builder pattern
- Event names standardized - update your listeners

### Deprecated Methods

- `DungeonService.createInstanceSync()` - Use `createInstance().join()`
- `LootService.generateLootSync()` - Use `generateL
