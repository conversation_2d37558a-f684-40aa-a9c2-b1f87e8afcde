package com.ultimatedungeon.udx.api;

import com.ultimatedungeon.udx.api.dungeon.DungeonService;
import com.ultimatedungeon.udx.api.party.PartyService;
import com.ultimatedungeon.udx.api.progression.ProgressionService;
import com.ultimatedungeon.udx.api.loot.LootService;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.ServicesManager;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * Main API entry point for UltimateDungeonX.
 * 
 * <p>This class provides access to all public services and APIs for addon developers.
 * Services are registered with Bukkit's ServicesManager for easy access.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class UltimateDungeonXAPI {
    
    private static UltimateDungeonXAPI instance;
    private final Plugin plugin;
    private final ServicesManager servicesManager;
    
    // Service instances
    private DungeonService dungeonService;
    private PartyService partyService;
    private ProgressionService progressionService;
    private LootService lootService;
    
    private UltimateDungeonXAPI(@NotNull Plugin plugin) {
        this.plugin = plugin;
        this.servicesManager = plugin.getServer().getServicesManager();
    }
    
    /**
     * Initializes the API with the plugin instance.
     * 
     * @param plugin The UltimateDungeonX plugin instance
     */
    public static void initialize(@NotNull Plugin plugin) {
        if (instance != null) {
            throw new IllegalStateException("UltimateDungeonXAPI is already initialized");
        }
        instance = new UltimateDungeonXAPI(plugin);
    }
    
    /**
     * Gets the API instance.
     * 
     * @return The API instance, or null if not initialized
     */
    @Nullable
    public static UltimateDungeonXAPI getInstance() {
        return instance;
    }
    
    /**
     * Gets the API instance, throwing an exception if not available.
     * 
     * @return The API instance
     * @throws IllegalStateException if the API is not initialized
     */
    @NotNull
    public static UltimateDungeonXAPI getInstanceOrThrow() {
        if (instance == null) {
            throw new IllegalStateException("UltimateDungeonXAPI is not initialized. Is UltimateDungeonX loaded?");
        }
        return instance;
    }
    
    /**
     * Checks if the API is available.
     * 
     * @return True if the API is initialized and available
     */
    public static boolean isAvailable() {
        return instance != null;
    }
    
    /**
     * Gets the plugin version.
     * 
     * @return The plugin version string
     */
    @NotNull
    public String getVersion() {
        return plugin.getDescription().getVersion();
    }
    
    /**
     * Gets the plugin instance.
     * 
     * @return The plugin instance
     */
    @NotNull
    public Plugin getPlugin() {
        return plugin;
    }
    
    /**
     * Registers a service with the Bukkit ServicesManager.
     * 
     * @param serviceClass The service interface class
     * @param implementation The service implementation
     * @param <T> The service type
     */
    public <T> void registerService(@NotNull Class<T> serviceClass, @NotNull T implementation) {
        servicesManager.register(serviceClass, implementation, plugin, org.bukkit.plugin.ServicePriority.Normal);
    }
    
    /**
     * Gets a service from the Bukkit ServicesManager.
     * 
     * @param serviceClass The service interface class
     * @param <T> The service type
     * @return The service implementation, or null if not found
     */
    @Nullable
    public <T> T getService(@NotNull Class<T> serviceClass) {
        return servicesManager.getRegistration(serviceClass) != null 
            ? servicesManager.getRegistration(serviceClass).getProvider() 
            : null;
    }
    
    /**
     * Gets the dungeon service for managing dungeons and instances.
     * 
     * @return The dungeon service
     */
    @NotNull
    public DungeonService getDungeonService() {
        if (dungeonService == null) {
            dungeonService = getService(DungeonService.class);
            if (dungeonService == null) {
                throw new IllegalStateException("DungeonService is not available");
            }
        }
        return dungeonService;
    }
    
    /**
     * Gets the party service for managing parties and matchmaking.
     * 
     * @return The party service
     */
    @NotNull
    public PartyService getPartyService() {
        if (partyService == null) {
            partyService = getService(PartyService.class);
            if (partyService == null) {
                throw new IllegalStateException("PartyService is not available");
            }
        }
        return partyService;
    }
    
    /**
     * Gets the progression service for managing player progress and achievements.
     * 
     * @return The progression service
     */
    @NotNull
    public ProgressionService getProgressionService() {
        if (progressionService == null) {
            progressionService = getService(ProgressionService.class);
            if (progressionService == null) {
                throw new IllegalStateException("ProgressionService is not available");
            }
        }
        return progressionService;
    }
    
    /**
     * Gets the loot service for managing loot tables and rewards.
     * 
     * @return The loot service
     */
    @NotNull
    public LootService getLootService() {
        if (lootService == null) {
            lootService = getService(LootService.class);
            if (lootService == null) {
                throw new IllegalStateException("LootService is not available");
            }
        }
        return lootService;
    }
    
    /**
     * Shuts down the API and unregisters all services.
     */
    public static void shutdown() {
        if (instance != null) {
            // Unregister services
            instance.servicesManager.unregisterAll(instance.plugin);
            
            // Clear references
            instance.dungeonService = null;
            instance.partyService = null;
            instance.progressionService = null;
            instance.lootService = null;
            
            instance = null;
        }
    }
}
