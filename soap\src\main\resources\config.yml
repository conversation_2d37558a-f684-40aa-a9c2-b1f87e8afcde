# UltimateDungeonX Configuration File
# Version: 1.0.0

config-version: 1

# General Settings
general:
  # Name of the build world where rooms are created
  build-world: "udx_build"
  
  # Maximum number of concurrent dungeon instances
  max-instances: 50
  
  # Timeout for inactive instances (in minutes)
  instance-timeout-minutes: 60
  
  # Enable debug mode for additional logging
  debug-mode: false

# Performance Settings
performance:
  # Maximum blocks to place per tick during pasting
  blocks-per-tick: 4096
  
  # Maximum entities allowed per instance
  max-entities-per-instance: 200
  
  # Enable asynchronous dungeon generation
  async-generation: true
  
  # Radius of chunks to preload around instances
  chunk-preload-radius: 2

# Database Settings
database:
  # Database type: "sqlite" or "mysql"
  type: "sqlite"
  
  # SQLite settings (used when type is "sqlite")
  sqlite:
    file: "players.db"
  
  # MySQL settings (used when type is "mysql")
  mysql:
    host: "localhost"
    port: 3306
    database: "udx"
    username: "udx"
    password: "password"
    # Connection pool settings
    max-connections: 10
    connection-timeout: 30000

# GUI Settings
gui:
  # Enable sound effects in menus
  sounds-enabled: true
  
  # Enable particle effects in menus
  particles-enabled: true
  
  # Enable menu animations
  animations-enabled: true
  
  # Default menu size (must be multiple of 9)
  menu-size: 54

# Party Settings
party:
  # Maximum party size
  max-size: 5
  
  # Timeout for party invitations (in seconds)
  invite-timeout-seconds: 30
  
  # Timeout for ready checks (in seconds)
  ready-check-timeout-seconds: 15
  
  # Allow cross-world party invites
  cross-world-invites: true

# Instance Protection Settings
instances:
  protection:
    # Prevent block breaking in instances
    block-break: false
    
    # Prevent block placing in instances
    block-place: false
    
    # Enable PvP in instances
    pvp: false
    
    # Allow mob griefing (creeper explosions, etc.)
    mob-griefing: false
    
    # Allow fire spread
    fire-spread: false
    
    # Allow liquid flow
    liquid-flow: false
    
    # Prevent item dropping on death
    keep-inventory: true
    
    # Prevent experience loss on death
    keep-experience: true

# Dungeon Settings
dungeons:
  # Default difficulty for new dungeons
  default-difficulty: "NORMAL"
  
  # Maximum dungeon length (number of rooms)
  max-length: 15
  
  # Maximum branching factor
  max-branching: 2
  
  # Timeout for dungeon generation (in seconds)
  generation-timeout-seconds: 30
  
  # Minimum rooms required for a valid dungeon
  min-rooms: 3
  
  # Enable dungeon previews in browser
  enable-previews: true

# Mob Settings
mobs:
  # Maximum mobs per spawner
  max-per-spawner: 10
  
  # Despawn mobs when chunk unloads
  despawn-on-chunk-unload: true
  
  # Leash radius for mobs (blocks from spawn point)
  leash-radius: 32
  
  # Cooldown between mob abilities (in seconds)
  ability-cooldown-seconds: 5
  
  # Enable custom AI behaviors
  custom-ai: true
  
  # Mob health scaling per difficulty
  health-scaling:
    EASY: 0.75
    NORMAL: 1.0
    HARD: 1.5
    MYTHIC: 2.0
    NIGHTMARE: 3.0

# Boss Settings
bosses:
  # Enable boss health bars
  bossbar-enabled: true
  
  # Show telegraph particles for boss abilities
  telegraph-particles: true
  
  # Announce boss phase changes
  phase-announcements: true
  
  # Enrage timer for bosses (in minutes)
  enrage-timer-minutes: 10
  
  # Boss damage scaling per difficulty
  damage-scaling:
    EASY: 0.8
    NORMAL: 1.0
    HARD: 1.3
    MYTHIC: 1.7
    NIGHTMARE: 2.5

# Loot Settings
loot:
  # Enable chest regeneration
  chest-regeneration: true
  
  # Give each player individual loot
  per-player-loot: true
  
  # Add glow effect to rare items
  rarity-glow: true
  
  # Chance for jackpot loot (0.0 to 1.0)
  jackpot-chance: 0.01
  
  # Loot multipliers per difficulty
  quantity-multipliers:
    EASY: 0.8
    NORMAL: 1.0
    HARD: 1.2
    MYTHIC: 1.5
    NIGHTMARE: 2.0
  
  # Rarity chance multipliers per difficulty
  rarity-multipliers:
    EASY: 0.7
    NORMAL: 1.0
    HARD: 1.3
    MYTHIC: 1.8
    NIGHTMARE: 2.5

# Affix Settings
affixes:
  # Hours between affix rotations
  rotation-hours: 24
  
  # Maximum affixes per dungeon
  max-per-dungeon: 3
  
  # Scale affix effects with difficulty
  difficulty-scaling: true
  
  # Enable weekly special affixes
  weekly-specials: true

# Progression Settings
progression:
  # Enable seasonal rankings
  seasonal-rankings: true
  
  # Season length in days
  season-length-days: 90
  
  # Enable achievements
  achievements-enabled: true
  
  # Enable leaderboards
  leaderboards-enabled: true
  
  # Points for dungeon completion
  completion-points:
    EASY: 10
    NORMAL: 25
    HARD: 50
    MYTHIC: 100
    NIGHTMARE: 200

# Integration Settings
integrations:
  # Vault economy integration
  vault:
    enabled: true
    # Currency rewards per difficulty
    currency-rewards:
      EASY: 50
      NORMAL: 100
      HARD: 200
      MYTHIC: 400
      NIGHTMARE: 800
  
  # PlaceholderAPI integration
  placeholderapi:
    enabled: true

# Spectator Settings
spectator:
  # Allow spectating active runs
  enabled: true
  
  # Spectators can see player inventories
  see-inventories: false
  
  # Spectators can teleport to players
  teleport-to-players: true
  
  # Maximum spectators per instance
  max-per-instance: 5

# Backup Settings
backup:
  # Enable automatic backups
  enabled: true
  
  # Backup interval in hours
  interval-hours: 6
  
  # Maximum backup files to keep
  max-backups: 10
  
  # Backup player data
  include-player-data: true
  
  # Backup dungeon configurations
  include-dungeons: true

# Logging Settings
logging:
  # Log dungeon completions
  log-completions: true
  
  # Log player deaths
  log-deaths: true
  
  # Log loot drops
  log-loot: false
  
  # Log performance metrics
  log-performance: true
  
  # Log level for debug messages
  debug-level: "INFO"

# Experimental Features
experimental:
  # Enable experimental features (may be unstable)
  enabled: false
  
  # Advanced pathfinding for mobs
  advanced-pathfinding: false
  
  # Dynamic difficulty adjustment
  dynamic-difficulty: false
  
  # Procedural boss generation
  procedural-bosses: false
