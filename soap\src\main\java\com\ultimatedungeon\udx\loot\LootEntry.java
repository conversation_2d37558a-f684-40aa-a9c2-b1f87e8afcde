package com.ultimatedungeon.udx.loot;

import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;

/**
 * Represents a single entry in a loot table.
 * 
 * <p>Each entry defines what can be dropped, the chance of dropping,
 * quantity ranges, and conditions for the drop.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record LootEntry(
    @NotNull String id,
    @NotNull LootEntryType type,
    @NotNull String value,
    double weight,
    int minQuantity,
    int maxQuantity,
    @NotNull Rarity rarity,
    @Nullable String condition,
    @NotNull Map<String, Object> parameters
) {
    
    /**
     * Creates a simple item loot entry.
     * 
     * @param id the entry ID
     * @param itemType the item type (e.g., "DIAMOND_SWORD")
     * @param weight the drop weight
     * @param minQuantity minimum quantity
     * @param maxQuantity maximum quantity
     * @param rarity the item rarity
     * @return the loot entry
     */
    @NotNull
    public static LootEntry item(@NotNull String id, @NotNull String itemType, 
                                double weight, int minQuantity, int maxQuantity, 
                                @NotNull Rarity rarity) {
        return new LootEntry(
            id,
            LootEntryType.ITEM,
            itemType,
            weight,
            minQuantity,
            maxQuantity,
            rarity,
            null,
            Map.of()
        );
    }
    
    /**
     * Creates a command loot entry.
     * 
     * @param id the entry ID
     * @param command the command to execute
     * @param weight the drop weight
     * @param rarity the entry rarity
     * @return the loot entry
     */
    @NotNull
    public static LootEntry command(@NotNull String id, @NotNull String command, 
                                   double weight, @NotNull Rarity rarity) {
        return new LootEntry(
            id,
            LootEntryType.COMMAND,
            command,
            weight,
            1,
            1,
            rarity,
            null,
            Map.of()
        );
    }
    
    /**
     * Creates a currency loot entry.
     * 
     * @param id the entry ID
     * @param currencyType the currency type
     * @param weight the drop weight
     * @param minAmount minimum amount
     * @param maxAmount maximum amount
     * @param rarity the entry rarity
     * @return the loot entry
     */
    @NotNull
    public static LootEntry currency(@NotNull String id, @NotNull String currencyType, 
                                    double weight, int minAmount, int maxAmount, 
                                    @NotNull Rarity rarity) {
        return new LootEntry(
            id,
            LootEntryType.CURRENCY,
            currencyType,
            weight,
            minAmount,
            maxAmount,
            rarity,
            null,
            Map.of("currencyType", currencyType)
        );
    }
    
    /**
     * Creates a table reference entry that points to another loot table.
     * 
     * @param id the entry ID
     * @param tableId the referenced table ID
     * @param weight the drop weight
     * @param rarity the entry rarity
     * @return the loot entry
     */
    @NotNull
    public static LootEntry tableReference(@NotNull String id, @NotNull String tableId, 
                                          double weight, @NotNull Rarity rarity) {
        return new LootEntry(
            id,
            LootEntryType.TABLE_REFERENCE,
            tableId,
            weight,
            1,
            1,
            rarity,
            null,
            Map.of()
        );
    }
    
    /**
     * Creates a copy of this entry with a condition.
     * 
     * @param condition the condition string
     * @return the new entry with condition
     */
    @NotNull
    public LootEntry withCondition(@NotNull String condition) {
        return new LootEntry(
            id,
            type,
            value,
            weight,
            minQuantity,
            maxQuantity,
            rarity,
            condition,
            parameters
        );
    }
    
    /**
     * Creates a copy of this entry with additional parameters.
     * 
     * @param additionalParams the additional parameters
     * @return the new entry with merged parameters
     */
    @NotNull
    public LootEntry withParameters(@NotNull Map<String, Object> additionalParams) {
        Map<String, Object> mergedParams = Map.copyOf(parameters);
        mergedParams.putAll(additionalParams);
        
        return new LootEntry(
            id,
            type,
            value,
            weight,
            minQuantity,
            maxQuantity,
            rarity,
            condition,
            mergedParams
        );
    }
    
    /**
     * Checks if this entry has a condition.
     * 
     * @return true if a condition is set
     */
    public boolean hasCondition() {
        return condition != null && !condition.trim().isEmpty();
    }
    
    /**
     * Gets a parameter value by key.
     * 
     * @param key the parameter key
     * @param defaultValue the default value if not found
     * @param <T> the parameter type
     * @return the parameter value or default
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public <T> T getParameter(@NotNull String key, @Nullable T defaultValue) {
        Object value = parameters.get(key);
        if (value != null) {
            try {
                return (T) value;
            } catch (ClassCastException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * The type of loot entry.
     */
    public enum LootEntryType {
        /** A Minecraft item */
        ITEM,
        /** A command to execute */
        COMMAND,
        /** Currency (requires Vault) */
        CURRENCY,
        /** Reference to another loot table */
        TABLE_REFERENCE,
        /** Custom cosmetic token */
        COSMETIC_TOKEN
    }
}
