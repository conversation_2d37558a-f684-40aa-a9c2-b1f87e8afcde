package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Achievements menu showing player progress and unlocked rewards.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class AchievementsMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public AchievementsMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Achievements").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Player stats overview
        setupStatsOverview();
        
        // Achievement categories
        setupAchievementCategories();
        
        // Sample achievements
        setupSampleAchievements();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Main Menu").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(49, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMainMenu(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupStatsOverview() {
        // Player level and progress
        ItemStack playerStatsItem = new ItemBuilder(Material.PLAYER_HEAD)
            .name(Component.text(player.getName()).color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Dungeon Level: 15").color(NamedTextColor.YELLOW),
                Component.text("Total Completions: 47").color(NamedTextColor.GRAY),
                Component.text("Best Time: 12:34").color(NamedTextColor.GRAY),
                Component.text("Deaths: 23").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Season Rank: #156").color(NamedTextColor.AQUA),
                Component.text("Season Score: 2,847").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(4, playerStatsItem, clickType -> {
            // Info only, no action
        });
        
        // Achievement progress
        ItemStack progressItem = new ItemBuilder(Material.EXPERIENCE_BOTTLE)
            .name(Component.text("Achievement Progress").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Completed: 12/45").color(NamedTextColor.GRAY),
                Component.text("Progress: 26.7%").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Recent Unlocks:").color(NamedTextColor.YELLOW),
                Component.text("• First Steps").color(NamedTextColor.GREEN),
                Component.text("• Dungeon Explorer").color(NamedTextColor.GREEN),
                Component.text("• Team Player").color(NamedTextColor.GREEN)
            )
            .build();
        
        setItem(22, progressItem, clickType -> {
            // Info only, no action
        });
    }
    
    private void setupAchievementCategories() {
        // Exploration achievements
        ItemStack explorationItem = new ItemBuilder(Material.COMPASS)
            .name(Component.text("Exploration").color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Dungeon discovery and exploration").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Progress: 4/12 completed").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to view achievements").color(NamedTextColor.GREEN)
            )
            .build();
        
        setItem(19, explorationItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Open exploration achievements submenu
                player.sendMessage(Component.text("Exploration achievements coming soon!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Combat achievements
        ItemStack combatItem = new ItemBuilder(Material.DIAMOND_SWORD)
            .name(Component.text("Combat").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Fighting and boss encounters").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Progress: 3/15 completed").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to view achievements").color(NamedTextColor.GREEN)
            )
            .build();
        
        setItem(21, combatItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Open combat achievements submenu
                player.sendMessage(Component.text("Combat achievements coming soon!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Social achievements
        ItemStack socialItem = new ItemBuilder(Material.PLAYER_HEAD)
            .name(Component.text("Social").color(NamedTextColor.LIGHT_PURPLE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Party play and cooperation").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Progress: 2/8 completed").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to view achievements").color(NamedTextColor.GREEN)
            )
            .build();
        
        setItem(23, socialItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Open social achievements submenu
                player.sendMessage(Component.text("Social achievements coming soon!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Collection achievements
        ItemStack collectionItem = new ItemBuilder(Material.CHEST)
            .name(Component.text("Collection").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Loot gathering and rare finds").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Progress: 3/10 completed").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to view achievements").color(NamedTextColor.GREEN)
            )
            .build();
        
        setItem(25, collectionItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Open collection achievements submenu
                player.sendMessage(Component.text("Collection achievements coming soon!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
    }
    
    private void setupSampleAchievements() {
        // First Steps (completed)
        ItemStack firstStepsItem = new ItemBuilder(Material.LEATHER_BOOTS)
            .name(Component.text("First Steps").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Complete your first dungeon").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("✓ COMPLETED").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true),
                Component.text("Unlocked: 2 days ago").color(NamedTextColor.DARK_GRAY),
                Component.empty(),
                Component.text("Rewards:").color(NamedTextColor.YELLOW),
                Component.text("• Title: 'Novice Explorer'").color(NamedTextColor.AQUA),
                Component.text("• 100 Dungeon Coins").color(NamedTextColor.GOLD)
            )
            .glow()
            .build();
        
        setItem(28, firstStepsItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Achievement already completed!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
            }
        });
        
        // Speed Runner (in progress)
        ItemStack speedRunnerItem = new ItemBuilder(Material.GOLDEN_BOOTS)
            .name(Component.text("Speed Runner").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Complete a dungeon in under 10 minutes").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("⧗ IN PROGRESS").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true),
                Component.text("Best Time: 12:34").color(NamedTextColor.GRAY),
                Component.text("Target: 10:00").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("Rewards:").color(NamedTextColor.YELLOW),
                Component.text("• Title: 'Swift Runner'").color(NamedTextColor.AQUA),
                Component.text("• 250 Dungeon Coins").color(NamedTextColor.GOLD)
            )
            .build();
        
        setItem(29, speedRunnerItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Keep practicing to unlock this achievement!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Boss Slayer (locked)
        ItemStack bossSlayerItem = new ItemBuilder(Material.NETHERITE_SWORD)
            .name(Component.text("Boss Slayer").color(NamedTextColor.DARK_GRAY).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Defeat 10 different dungeon bosses").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("✗ LOCKED").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true),
                Component.text("Progress: 3/10 bosses").color(NamedTextColor.GRAY),
                Component.text("Requirement: Complete 'First Steps'").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("Rewards:").color(NamedTextColor.DARK_GRAY),
                Component.text("• Title: 'Boss Hunter'").color(NamedTextColor.DARK_GRAY),
                Component.text("• 500 Dungeon Coins").color(NamedTextColor.DARK_GRAY),
                Component.text("• Rare Loot Box").color(NamedTextColor.DARK_GRAY)
            )
            .build();
        
        setItem(30, bossSlayerItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("This achievement is locked! Complete the requirements first.")
                    .color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.BLOCK_ANVIL_LAND, 1.0f, 0.8f);
            }
        });
        
        // Team Player (completed)
        ItemStack teamPlayerItem = new ItemBuilder(Material.TOTEM_OF_UNDYING)
            .name(Component.text("Team Player").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Revive 5 party members").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("✓ COMPLETED").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true),
                Component.text("Unlocked: 1 day ago").color(NamedTextColor.DARK_GRAY),
                Component.empty(),
                Component.text("Rewards:").color(NamedTextColor.YELLOW),
                Component.text("• Title: 'Lifesaver'").color(NamedTextColor.AQUA),
                Component.text("• 200 Dungeon Coins").color(NamedTextColor.GOLD)
            )
            .glow()
            .build();
        
        setItem(31, teamPlayerItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Achievement already completed!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
            }
        });
        
        // Treasure Hunter (in progress)
        ItemStack treasureHunterItem = new ItemBuilder(Material.DIAMOND)
            .name(Component.text("Treasure Hunter").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Find 50 rare items").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("⧗ IN PROGRESS").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true),
                Component.text("Progress: 23/50 items").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Rewards:").color(NamedTextColor.YELLOW),
                Component.text("• Title: 'Treasure Seeker'").color(NamedTextColor.AQUA),
                Component.text("• 300 Dungeon Coins").color(NamedTextColor.GOLD),
                Component.text("• Epic Loot Box").color(NamedTextColor.LIGHT_PURPLE)
            )
            .build();
        
        setItem(32, treasureHunterItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Keep exploring to find more rare items!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Secret achievement (hidden)
        ItemStack secretItem = new ItemBuilder(Material.BARRIER)
            .name(Component.text("???").color(NamedTextColor.DARK_PURPLE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Hidden achievement").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("? HIDDEN ?").color(NamedTextColor.DARK_PURPLE).decoration(TextDecoration.BOLD, true),
                Component.text("Complete more achievements to reveal").color(NamedTextColor.GRAY)
            )
            .build();
        
        setItem(33, secretItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("This achievement is still hidden...")
                    .color(NamedTextColor.DARK_PURPLE));
                player.playSound(player.getLocation(), Sound.AMBIENT_CAVE, 1.0f, 1.5f);
            }
        });
    }
}
