package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * Main menu for players to access all UltimateDungeonX features.
 * 
 * <p>This menu provides access to dungeon browsing, party management,
 * achievements, settings, and other player-facing features.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class PlayerMainMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public PlayerMainMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("UltimateDungeonX").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true), 
              27, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        // Clear inventory
        inventory.clear();
        clickHandlers.clear();
        
        // Play Dungeons
        ItemStack playItem = new ItemBuilder(Material.DIAMOND_SWORD)
            .name(Component.text("Play Dungeons").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Browse and queue for dungeons").color(NamedTextColor.GRAY),
                Component.text("Solo or with your party").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to open dungeon browser").color(NamedTextColor.YELLOW)
            )
            .glow()
            .build();
        
        setItem(11, playItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                new DungeonBrowserMenu(plugin, player).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Party Management
        ItemStack partyItem = new ItemBuilder(Material.PLAYER_HEAD)
            .name(Component.text("Party").color(NamedTextColor.BLUE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Create or join a party").color(NamedTextColor.GRAY),
                Component.text("Invite friends to play together").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to manage party").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(13, partyItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openPartyMenu(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Achievements & Progress
        ItemStack achievementsItem = new ItemBuilder(Material.BOOK)
            .name(Component.text("Achievements").color(NamedTextColor.LIGHT_PURPLE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("View your progress and achievements").color(NamedTextColor.GRAY),
                Component.text("Check leaderboards and statistics").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to view achievements").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(15, achievementsItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openAchievementsMenu(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Settings
        ItemStack settingsItem = new ItemBuilder(Material.REDSTONE)
            .name(Component.text("Settings").color(NamedTextColor.RED))
            .lore(
                Component.text("Configure your preferences").color(NamedTextColor.GRAY),
                Component.text("Toggle HUD elements and sounds").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to open settings").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(22, settingsItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openSettingsMenu(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Admin access (if player has permission)
        if (player.hasPermission("udx.admin")) {
            ItemStack adminItem = new ItemBuilder(Material.COMMAND_BLOCK)
                .name(Component.text("Admin Hub").color(NamedTextColor.DARK_RED).decoration(TextDecoration.BOLD, true))
                .lore(
                    Component.text("Access administrative features").color(NamedTextColor.GRAY),
                    Component.text("Manage dungeons and instances").color(NamedTextColor.GRAY),
                    Component.empty(),
                    Component.text("Click to open admin hub").color(NamedTextColor.YELLOW)
                )
                .glow()
                .build();
            
            setItem(4, adminItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    new AdminHubMenu(plugin, player).open();
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                }
            });
        }
        
        // Fill empty slots with glass panes
        fillEmptySlots();
    }
}
