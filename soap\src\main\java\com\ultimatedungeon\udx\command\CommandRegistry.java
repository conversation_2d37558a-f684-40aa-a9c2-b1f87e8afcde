package com.ultimatedungeon.udx.command;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.MenuRegistry;
import com.ultimatedungeon.udx.gui.menus.BuilderToolsMenu;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.command.PluginCommand;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Registry for all UltimateDungeonX commands.
 * 
 * <p>This class handles command registration and routing. All commands are designed
 * to be thin wrappers that open GUIs rather than performing complex operations directly.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class CommandRegistry implements CommandExecutor, TabCompleter {
    
    private final JavaPlugin plugin;
    private final MenuRegistry menuRegistry;
    
    public CommandRegistry(@NotNull JavaPlugin plugin, @NotNull MenuRegistry menuRegistry) {
        this.plugin = plugin;
        this.menuRegistry = menuRegistry;
    }
    
    /**
     * Registers all commands with the plugin.
     */
    public void registerCommands() {
        // Main command
        registerCommand("udx", this);
        
        // Admin commands
        registerCommand("udxadmin", this);
        
        // Editor commands
        registerCommand("udxeditor", this);
        
        // Portal command
        registerCommand("udxportal", this);
        
        plugin.getLogger().info("Commands registered successfully");
    }
    
    /**
     * Registers a single command with the plugin.
     */
    private void registerCommand(@NotNull String name, @NotNull CommandExecutor executor) {
        PluginCommand command = plugin.getCommand(name);
        if (command != null) {
            command.setExecutor(executor);
            command.setTabCompleter(this);
        } else {
            plugin.getLogger().warning("Command '" + name + "' not found in plugin.yml");
        }
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        // Only players can use these commands
        if (!(sender instanceof Player player)) {
            sender.sendMessage(Component.text("This command can only be used by players.")
                .color(NamedTextColor.RED));
            return true;
        }
        
        String commandName = command.getName().toLowerCase();
        
        try {
            switch (commandName) {
                case "udx" -> handleMainCommand(player, args);
                case "udxadmin" -> handleAdminCommand(player, args);
                case "udxeditor" -> handleEditorCommand(player, args);
                case "udxportal" -> handlePortalCommand(player, args);
                default -> {
                    player.sendMessage(Component.text("Unknown command: " + commandName)
                        .color(NamedTextColor.RED));
                    return false;
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Error executing command '" + commandName + "': " + e.getMessage());
            e.printStackTrace();
            
            player.sendMessage(Component.text("An error occurred while executing the command. Please try again.")
                .color(NamedTextColor.RED));
        }
        
        return true;
    }
    
    /**
     * Handles the main /udx command.
     */
    private void handleMainCommand(@NotNull Player player, @NotNull String[] args) {
        // Check basic permission
        if (!player.hasPermission("udx.use")) {
            player.sendMessage(Component.text("You don't have permission to use UltimateDungeonX.")
                .color(NamedTextColor.RED));
            return;
        }
        
        // Handle subcommands
        if (args.length > 0) {
            String subcommand = args[0].toLowerCase();
            switch (subcommand) {
                case "admin" -> {
                    if (player.hasPermission("udx.admin")) {
                        menuRegistry.openAdminHub(player);
                    } else {
                        player.sendMessage(Component.text("You don't have permission to access the admin hub.")
                            .color(NamedTextColor.RED));
                    }
                }
                case "editor" -> {
                    if (player.hasPermission("udx.editor")) {
                        menuRegistry.openEditorHub(player);
                    } else {
                        player.sendMessage(Component.text("You don't have permission to access the editor.")
                            .color(NamedTextColor.RED));
                    }
                }
                case "portal" -> {
                    if (player.hasPermission("udx.admin.portal")) {
                        menuRegistry.givePortalKeystone(player);
                    } else {
                        player.sendMessage(Component.text("You don't have permission to create portals.")
                            .color(NamedTextColor.RED));
                    }
                }
                case "help" -> sendHelpMessage(player);
                case "version" -> sendVersionMessage(player);
                case "tp" -> {
                    if (player.hasPermission("udx.admin.teleport")) {
                        handleTeleportCommand(player, Arrays.copyOfRange(args, 1, args.length));
                    } else {
                        player.sendMessage(Component.text("You don't have permission to teleport to dungeons.")
                            .color(NamedTextColor.RED));
                    }
                }
                case "tools" -> {
                    if (player.hasPermission("udx.editor")) {
                        handleToolsCommand(player);
                    } else {
                        player.sendMessage(Component.text("You don't have permission to use builder tools.")
                            .color(NamedTextColor.RED));
                    }
                }
                case "reload" -> {
                    if (player.hasPermission("udx.admin")) {
                        // TODO: Implement config reload
                        player.sendMessage(Component.text("Configuration reloaded.")
                            .color(NamedTextColor.GREEN));
                    } else {
                        player.sendMessage(Component.text("You don't have permission to reload the configuration.")
                            .color(NamedTextColor.RED));
                    }
                }
                default -> {
                    player.sendMessage(Component.text("Unknown subcommand: " + subcommand)
                        .color(NamedTextColor.RED));
                    sendHelpMessage(player);
                }
            }
        } else {
            // Open main menu
            menuRegistry.openMainMenu(player);
        }
    }
    
    /**
     * Handles the /udxadmin command.
     */
    private void handleAdminCommand(@NotNull Player player, @NotNull String[] args) {
        if (!player.hasPermission("udx.admin")) {
            player.sendMessage(Component.text("You don't have permission to access the admin hub.")
                .color(NamedTextColor.RED));
            return;
        }
        
        menuRegistry.openAdminHub(player);
    }
    
    /**
     * Handles the /udxeditor command.
     */
    private void handleEditorCommand(@NotNull Player player, @NotNull String[] args) {
        if (!player.hasPermission("udx.editor")) {
            player.sendMessage(Component.text("You don't have permission to access the editor.")
                .color(NamedTextColor.RED));
            return;
        }
        
        menuRegistry.openEditorHub(player);
    }
    
    /**
     * Handles the /udxportal command.
     */
    private void handlePortalCommand(@NotNull Player player, @NotNull String[] args) {
        if (!player.hasPermission("udx.admin.portal")) {
            player.sendMessage(Component.text("You don't have permission to create portals.")
                .color(NamedTextColor.RED));
            return;
        }
        
        menuRegistry.givePortalKeystone(player);
    }
    
    /**
     * Sends a help message to the player.
     */
    private void sendHelpMessage(@NotNull Player player) {
        player.sendMessage(Component.text("=== UltimateDungeonX Help ===").color(NamedTextColor.GOLD));
        player.sendMessage(Component.text("/udx - Open the main menu").color(NamedTextColor.YELLOW));
        
        if (player.hasPermission("udx.admin")) {
            player.sendMessage(Component.text("/udx admin - Open the admin hub").color(NamedTextColor.YELLOW));
        }
        
        if (player.hasPermission("udx.editor")) {
            player.sendMessage(Component.text("/udx editor - Open the editor hub").color(NamedTextColor.YELLOW));
        }
        
        if (player.hasPermission("udx.admin.portal")) {
            player.sendMessage(Component.text("/udx portal - Get a portal keystone").color(NamedTextColor.YELLOW));
        }
        
        player.sendMessage(Component.text("/udx help - Show this help message").color(NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/udx version - Show plugin version").color(NamedTextColor.YELLOW));
        
        if (player.hasPermission("udx.admin")) {
            player.sendMessage(Component.text("/udx reload - Reload configuration").color(NamedTextColor.YELLOW));
        }
    }
    
    /**
     * Sends version information to the player.
     */
    private void sendVersionMessage(@NotNull Player player) {
        String version = plugin.getPluginMeta().getVersion();
        player.sendMessage(Component.text("UltimateDungeonX v" + version).color(NamedTextColor.GREEN));
        player.sendMessage(Component.text("A no-dependency, GUI-first dungeon plugin").color(NamedTextColor.GRAY));
    }
    
    /**
     * Handles the /udx tp <dungeon> command.
     */
    private void handleTeleportCommand(@NotNull Player player, @NotNull String[] args) {
        if (args.length == 0) {
            player.sendMessage(Component.text("Usage: /udx tp <dungeon_name>").color(NamedTextColor.RED));
            return;
        }
        
        String dungeonName = args[0];
        
        // Get the plugin instance to access DungeonService
        if (plugin instanceof UltimateDungeonX udxPlugin) {
            String worldName = udxPlugin.getDungeonService().getCustomDungeonWorld(dungeonName);
            
            if (worldName == null) {
                player.sendMessage(Component.text("Dungeon '" + dungeonName + "' not found!").color(NamedTextColor.RED));
                player.sendMessage(Component.text("Use '/udx admin' to create a new dungeon.").color(NamedTextColor.YELLOW));
                return;
            }
            
            World world = Bukkit.getWorld(worldName);
            if (world == null) {
                player.sendMessage(Component.text("Dungeon world '" + worldName + "' is not loaded!").color(NamedTextColor.RED));
                return;
            }
            
            // Teleport to spawn location
            player.teleport(world.getSpawnLocation());
            player.sendMessage(Component.text("Teleported to dungeon: " + dungeonName).color(NamedTextColor.GREEN));
            player.sendMessage(Component.text("Use '/udx tools' to access builder tools").color(NamedTextColor.AQUA));
        } else {
            player.sendMessage(Component.text("Error: Plugin instance not available").color(NamedTextColor.RED));
        }
    }
    
    /**
     * Handles the /udx tools command.
     */
    private void handleToolsCommand(@NotNull Player player) {
        World world = player.getWorld();
        String worldName = world.getName();
        
        // Check if player is in a custom dungeon world
        if (!worldName.startsWith("udx_dungeon_")) {
            player.sendMessage(Component.text("Builder tools can only be used in custom dungeon worlds!").color(NamedTextColor.RED));
            player.sendMessage(Component.text("Use '/udx tp <dungeon>' to teleport to a dungeon first.").color(NamedTextColor.YELLOW));
            return;
        }
        
        // Open builder tools menu
        openBuilderToolsMenu(player);
    }
    
    /**
     * Opens the builder tools menu for the player.
     */
    private void openBuilderToolsMenu(@NotNull Player player) {
        menuRegistry.openMenu(player, new BuilderToolsMenu((UltimateDungeonX) plugin, player));
        player.sendMessage(Component.text("Builder tools opened! Use the wands to place and configure dungeon elements.").color(NamedTextColor.GREEN));
    }
    
    @Override
    @Nullable
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, @NotNull String alias, @NotNull String[] args) {
        if (!(sender instanceof Player player)) {
            return new ArrayList<>();
        }
        
        String commandName = command.getName().toLowerCase();
        
        if ("udx".equals(commandName)) {
            if (args.length == 1) {
                List<String> completions = new ArrayList<>();
                String partial = args[0].toLowerCase();
                
                // Basic subcommands
                if ("help".startsWith(partial)) completions.add("help");
                if ("version".startsWith(partial)) completions.add("version");
                
                // Permission-based subcommands
                if (player.hasPermission("udx.admin")) {
                    if ("admin".startsWith(partial)) completions.add("admin");
                    if ("reload".startsWith(partial)) completions.add("reload");
                }
                
                if (player.hasPermission("udx.editor")) {
                    if ("editor".startsWith(partial)) completions.add("editor");
                }
                
                if (player.hasPermission("udx.admin.portal")) {
                    if ("portal".startsWith(partial)) completions.add("portal");
                }
                
                return completions;
            }
        }
        
        return new ArrayList<>();
    }
}
