# UltimateDungeonX Baseline Affix Library
# A collection of 12+ affix definitions that modify dungeon runs

affixes:
  # ===== OFFENSIVE AFFIXES =====
  
  berserk_mobs:
    id: "berserk_mobs"
    name: "Berserk"
    description: "Mobs deal increased damage but have reduced health"
    rarity: "COMMON"
    type: "OFFENSIVE"
    category: "mob_modifier"
    effects:
      mob_damage_multiplier: 1.5
      mob_health_multiplier: 0.7
      mob_speed_multiplier: 1.2
    visual_effects:
      mob_particles: "ANGRY_VILLAGER"
      mob_glow_color: "RED"
    duration: "PERMANENT"
    
  explosive_death:
    id: "explosive_death"
    name: "Volatile"
    description: "Mobs explode when they die, dealing area damage"
    rarity: "UNCOMMON"
    type: "OFFENSIVE"
    category: "death_effect"
    effects:
      explosion_damage: 8.0
      explosion_radius: 3.0
      explosion_delay: 10
    visual_effects:
      death_particles: "EXPLOSION_LARGE"
      death_sound: "ENTITY_GENERIC_EXPLODE"
    telegraph:
      warning_time: 20
      warning_particle: "REDSTONE"
      
  enraged_elites:
    id: "enraged_elites"
    name: "Elite Fury"
    description: "Elite mobs spawn more frequently and are more dangerous"
    rarity: "RARE"
    type: "OFFENSIVE"
    category: "spawn_modifier"
    effects:
      elite_spawn_chance: 0.4
      elite_damage_multiplier: 1.8
      elite_health_multiplier: 2.0
      elite_ability_cooldown_reduction: 0.5
    visual_effects:
      elite_aura: "SOUL_FIRE_FLAME"
      elite_name_color: "GOLD"
      
  # ===== DEFENSIVE AFFIXES =====
  
  armored_foes:
    id: "armored_foes"
    name: "Fortified"
    description: "Mobs have increased armor and resistance to damage"
    rarity: "COMMON"
    type: "DEFENSIVE"
    category: "mob_modifier"
    effects:
      mob_armor_multiplier: 2.0
      mob_damage_reduction: 0.25
      mob_knockback_resistance: 0.5
    visual_effects:
      mob_particles: "IRON_CRACK"
      armor_glow: true
      
  regenerating_enemies:
    id: "regenerating_enemies"
    name: "Regenerative"
    description: "Mobs slowly regenerate health over time"
    rarity: "UNCOMMON"
    type: "DEFENSIVE"
    category: "healing"
    effects:
      regeneration_rate: 0.5
      regeneration_interval: 40
      max_regeneration_percent: 0.8
    visual_effects:
      heal_particles: "HEART"
      heal_sound: "ENTITY_PLAYER_LEVELUP"
      
  shielded_mobs:
    id: "shielded_mobs"
    name: "Shielded"
    description: "Mobs spawn with temporary damage shields"
    rarity: "RARE"
    type: "DEFENSIVE"
    category: "shield"
    effects:
      shield_health: 20.0
      shield_regeneration_delay: 100
      shield_damage_reduction: 0.5
    visual_effects:
      shield_particles: "ENCHANTMENT_TABLE"
      shield_color: "BLUE"
      
  # ===== ENVIRONMENTAL AFFIXES =====
  
  frosted_floors:
    id: "frosted_floors"
    name: "Frosted"
    description: "Floors are slippery and movement is unpredictable"
    rarity: "COMMON"
    type: "ENVIRONMENTAL"
    category: "terrain"
    effects:
      floor_slipperiness: 0.98
      movement_unpredictability: 0.3
      fall_damage_multiplier: 1.5
    visual_effects:
      floor_particles: "SNOWBALL"
      ambient_sound: "BLOCK_GLASS_BREAK"
    environmental_changes:
      replace_blocks:
        "STONE": "ICE"
        "COBBLESTONE": "PACKED_ICE"
        
  thick_air:
    id: "thick_air"
    name: "Dense Atmosphere"
    description: "Projectiles move slower and have reduced range"
    rarity: "UNCOMMON"
    type: "ENVIRONMENTAL"
    category: "projectile"
    effects:
      projectile_speed_multiplier: 0.6
      projectile_range_multiplier: 0.8
      bow_charge_time_multiplier: 1.4
    visual_effects:
      ambient_particles: "CLOUD"
      particle_density: "MEDIUM"
      
  cursed_ground:
    id: "cursed_ground"
    name: "Cursed Earth"
    description: "Standing still for too long causes damage over time"
    rarity: "RARE"
    type: "ENVIRONMENTAL"
    category: "movement"
    effects:
      stationary_time_threshold: 60
      curse_damage: 2.0
      curse_interval: 20
    visual_effects:
      curse_particles: "SPELL_WITCH"
      ground_color_tint: "DARK_PURPLE"
    telegraph:
      warning_time: 40
      warning_particle: "REDSTONE"
      
  # ===== MAGICAL AFFIXES =====
  
  mana_burn:
    id: "mana_burn"
    name: "Mana Burn"
    description: "Taking damage drains experience levels"
    rarity: "UNCOMMON"
    type: "MAGICAL"
    category: "resource_drain"
    effects:
      experience_drain_per_hit: 1
      max_experience_drain: 10
      drain_chance: 0.3
    visual_effects:
      drain_particles: "ENCHANTMENT_TABLE"
      drain_sound: "BLOCK_ENCHANTMENT_TABLE_USE"
      
  spell_reflect:
    id: "spell_reflect"
    name: "Spell Reflection"
    description: "Mobs have a chance to reflect projectiles back at players"
    rarity: "RARE"
    type: "MAGICAL"
    category: "reflection"
    effects:
      reflect_chance: 0.25
      reflect_damage_multiplier: 1.2
      reflect_speed_multiplier: 1.5
    visual_effects:
      reflect_particles: "ENCHANTMENT_TABLE"
      reflect_sound: "BLOCK_ANVIL_LAND"
      
  arcane_overload:
    id: "arcane_overload"
    name: "Arcane Overload"
    description: "Magic abilities have reduced cooldowns but increased mana costs"
    rarity: "EPIC"
    type: "MAGICAL"
    category: "ability_modifier"
    effects:
      ability_cooldown_multiplier: 0.6
      mana_cost_multiplier: 1.8
      spell_power_multiplier: 1.3
    visual_effects:
      overload_particles: "DRAGON_BREATH"
      ambient_glow: "PURPLE"

# Affix Categories for organization
categories:
  offensive: ["berserk_mobs", "explosive_death", "enraged_elites"]
  defensive: ["armored_foes", "regenerating_enemies", "shielded_mobs"]
  environmental: ["frosted_floors", "thick_air", "cursed_ground"]
  magical: ["mana_burn", "spell_reflect", "arcane_overload"]

# Rarity Distribution
rarity_weights:
  COMMON: 50.0      # 50% chance
  UNCOMMON: 30.0    # 30% chance
  RARE: 15.0        # 15% chance
  EPIC: 4.0         # 4% chance
  LEGENDARY: 1.0    # 1% chance

# Rotation Schedule
rotation_schedule:
  daily:
    count: 2
    rarity_bias: "COMMON"
  weekly:
    count: 4
    rarity_bias: "UNCOMMON"
  monthly:
    count: 6
    rarity_bias: "RARE"
  seasonal:
    count: 8
    rarity_bias: "EPIC"

# Affix Combinations (some affixes work well together)
synergies:
  - affixes: ["berserk_mobs", "explosive_death"]
    name: "Berserker Bombs"
    bonus_effect: "explosion_damage_multiplier: 1.5"
  - affixes: ["armored_foes", "regenerating_enemies"]
    name: "Fortress Defense"
    bonus_effect: "regeneration_rate_multiplier: 1.3"
  - affixes: ["frosted_floors", "thick_air"]
    name: "Arctic Conditions"
    bonus_effect: "movement_speed_reduction: 0.2"

# Incompatible Affixes (cannot appear together)
conflicts:
  - ["berserk_mobs", "armored_foes"]  # Contradictory effects
  - ["regenerating_enemies", "explosive_death"]  # Thematic conflict
  - ["mana_burn", "arcane_overload"]  # Both affect mana systems

# Seasonal Themes
seasonal_themes:
  spring:
    preferred_affixes: ["regenerating_enemies", "cursed_ground"]
    theme_color: "GREEN"
  summer:
    preferred_affixes: ["explosive_death", "thick_air"]
    theme_color: "YELLOW"
  autumn:
    preferred_affixes: ["mana_burn", "spell_reflect"]
    theme_color: "ORANGE"
  winter:
    preferred_affixes: ["frosted_floors", "shielded_mobs"]
    theme_color: "LIGHT_BLUE"
