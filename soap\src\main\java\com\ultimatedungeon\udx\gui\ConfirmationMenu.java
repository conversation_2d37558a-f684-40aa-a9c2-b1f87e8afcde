package com.ultimatedungeon.udx.gui;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * A confirmation dialog menu for dangerous or important actions.
 * 
 * <p>This menu presents a simple yes/no choice with clear visual indicators
 * and customizable confirmation messages.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class ConfirmationMenu extends Menu {
    
    private final Component message;
    private final Runnable onConfirm;
    private final Runnable onCancel;
    
    /**
     * Creates a new confirmation menu.
     * 
     * @param player the player viewing the menu
     * @param message the confirmation message
     * @param onConfirm action to run when confirmed
     * @param parent the parent menu to return to
     * @param menuRegistry the menu registry
     */
    public ConfirmationMenu(@NotNull Player player, @NotNull Component message, 
                           @NotNull Runnable onConfirm, @Nullable Menu parent, 
                           @NotNull MenuRegistry menuRegistry) {
        this(player, message, onConfirm, parent != null ? parent::open : null, parent, menuRegistry);
    }
    
    /**
     * Creates a new confirmation menu with custom cancel action.
     * 
     * @param player the player viewing the menu
     * @param message the confirmation message
     * @param onConfirm action to run when confirmed
     * @param onCancel action to run when cancelled
     * @param parent the parent menu
     * @param menuRegistry the menu registry
     */
    public ConfirmationMenu(@NotNull Player player, @NotNull Component message, 
                           @NotNull Runnable onConfirm, @Nullable Runnable onCancel, 
                           @Nullable Menu parent, @NotNull MenuRegistry menuRegistry) {
        super(player, Component.text("Confirm Action").color(NamedTextColor.YELLOW), 27, menuRegistry);
        this.message = message;
        this.onConfirm = onConfirm;
        this.onCancel = onCancel != null ? onCancel : () -> {};
        this.setParent(parent);
    }
    
    @Override
    protected void setupMenu() {
        // Message display (top row)
        ItemStack messageItem = createItem(
            Material.PAPER,
            Component.text("Confirmation Required").color(NamedTextColor.YELLOW),
            java.util.List.of(
                Component.empty(),
                message,
                Component.empty(),
                Component.text("Are you sure you want to continue?").color(NamedTextColor.GRAY)
            )
        );
        setItem(13, messageItem);
        
        // Confirm button (green - left side)
        ItemStack confirmButton = createItem(
            Material.LIME_CONCRETE,
            Component.text("✓ CONFIRM").color(NamedTextColor.GREEN),
            createLore(
                "Click to confirm this action",
                "",
                "This action cannot be undone!"
            )
        );
        
        // Place confirm buttons in a pattern
        for (int slot : new int[]{10, 11, 12, 19, 20, 21}) {
            setItem(slot, confirmButton, clickType -> {
                if (clickType == ClickType.LEFT) {
                    handleConfirm();
                }
            });
        }
        
        // Cancel button (red - right side)
        ItemStack cancelButton = createItem(
            Material.RED_CONCRETE,
            Component.text("✕ CANCEL").color(NamedTextColor.RED),
            createLore(
                "Click to cancel this action",
                "",
                "Return to the previous menu"
            )
        );
        
        // Place cancel buttons in a pattern
        for (int slot : new int[]{14, 15, 16, 23, 24, 25}) {
            setItem(slot, cancelButton, clickType -> {
                if (clickType == ClickType.LEFT) {
                    handleCancel();
                }
            });
        }
        
        // Fill remaining slots with barrier blocks
        ItemStack barrier = createItem(Material.BARRIER, Component.empty());
        for (int i = 0; i < 27; i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, barrier);
            }
        }
    }
    
    /**
     * Handles confirmation action.
     */
    private void handleConfirm() {
        try {
            close();
            onConfirm.run();
        } catch (Exception e) {
            menuRegistry.getPlugin().getLogger().severe("Error in confirmation action: " + e.getMessage());
            e.printStackTrace();
            
            player.sendMessage(Component.text("An error occurred while processing your confirmation.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Handles cancel action.
     */
    private void handleCancel() {
        try {
            close();
            onCancel.run();
        } catch (Exception e) {
            menuRegistry.getPlugin().getLogger().severe("Error in cancel action: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Override
    protected void onClose() {
        // If menu is closed without explicit choice, treat as cancel
        if (player.getOpenInventory().getTopInventory() != inventory) {
            handleCancel();
        }
    }
}
