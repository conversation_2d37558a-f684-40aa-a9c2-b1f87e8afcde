# UltimateDungeonX LuckPerms Setup Guide

This guide provides the exact LuckPer<PERSON> commands to set up permissions for UltimateDungeonX.

## 🔧 Admin Setup (Full Access)

### **Option 1: Direct User Permissions (Quick Setup)**

Replace `YourUsername` with your actual Minecraft username:

```bash
# Give yourself ALL UltimateDungeonX permissions
/lp user YourUsername permission set udx.* true
```

**Example**:
```bash
/lp user Notch permission set udx.* true
/lp user Steve permission set udx.* true
```

### **Option 2: Admin Group Setup (Recommended)**

Create an admin group for multiple administrators:

```bash
# Create admin group
/lp creategroup udx_admin

# Give the group all permissions
/lp group udx_admin permission set udx.* true

# Add yourself to the admin group
/lp user YourUsername parent add udx_admin
```

**Example**:
```bash
/lp creategroup udx_admin
/lp group udx_admin permission set udx.* true
/lp user Notch parent add udx_admin
/lp user Steve parent add udx_admin
```

## 👥 Player Setup (Regular Access)

### **Basic Player Permissions**

Give all players access to use dungeons:

```bash
# Basic dungeon access for all players
/lp group default permission set udx.use true
/lp group default permission set udx.join true
/lp group default permission set udx.party true
/lp group default permission set udx.spectate true
```

### **Alternative: Player Group Setup**

Create a dedicated player group:

```bash
# Create player group
/lp creategroup udx_player

# Give basic permissions
/lp group udx_player permission set udx.use true
/lp group udx_player permission set udx.join true
/lp group udx_player permission set udx.party true
/lp group udx_player permission set udx.spectate true

# Make default group inherit from player group
/lp group default parent add udx_player
```

## 🛠️ Staff/Moderator Setup (Editor Access)

### **Moderator Permissions**

Give staff members content editing access without full admin:

```bash
# Create moderator group
/lp creategroup udx_moderator

# Give basic permissions
/lp group udx_moderator permission set udx.use true
/lp group udx_moderator permission set udx.join true
/lp group udx_moderator permission set udx.party true
/lp group udx_moderator permission set udx.spectate true

# Give editor permissions
/lp group udx_moderator permission set udx.editor.* true

# Add staff members
/lp user StaffMember parent add udx_moderator
```

## 📋 Complete Setup Script

### **Copy-Paste Setup (All-in-One)**

```bash
# === ADMIN SETUP ===
# Create admin group and give full access
/lp creategroup udx_admin
/lp group udx_admin permission set udx.* true

# Add yourself as admin (REPLACE WITH YOUR USERNAME)
/lp user YourUsername parent add udx_admin

# === PLAYER SETUP ===
# Give all players basic dungeon access
/lp group default permission set udx.use true
/lp group default permission set udx.join true
/lp group default permission set udx.party true
/lp group default permission set udx.spectate true

# === MODERATOR SETUP (OPTIONAL) ===
# Create moderator group with editor access
/lp creategroup udx_moderator
/lp group udx_moderator permission set udx.use true
/lp group udx_moderator permission set udx.join true
/lp group udx_moderator permission set udx.party true
/lp group udx_moderator permission set udx.spectate true
/lp group udx_moderator permission set udx.editor.* true

# Add moderators (REPLACE WITH ACTUAL USERNAMES)
# /lp user ModeratorName parent add udx_moderator
```

## 🎯 Permission Breakdown

### **Admin Permissions (udx.*)**
- `udx.use` - Basic plugin access
- `udx.join` - Join dungeon runs
- `udx.party` - Party management
- `udx.spectate` - Spectate runs
- `udx.admin.*` - All admin functions
  - `udx.admin` - Access admin hub
  - `udx.admin.create` - Create dungeons
  - `udx.admin.delete` - Delete dungeons
  - `udx.admin.manage` - Manage content
  - `udx.admin.teleport` - Teleport to instances
  - `udx.admin.force` - Force start/stop
  - `udx.admin.portal` - Create portals
  - `udx.admin.debug` - Debug tools
- `udx.editor.*` - All editor functions
  - `udx.editor.room` - Room editor
  - `udx.editor.spawner` - Spawner editor
  - `udx.editor.boss` - Boss editor
  - `udx.editor.loot` - Loot editor

### **Player Permissions**
- `udx.use` - Open main menu with `/udx`
- `udx.join` - Join dungeon runs and queues
- `udx.party` - Create/manage parties, send invites
- `udx.spectate` - Watch active dungeon runs

### **Moderator Permissions**
- All player permissions +
- `udx.editor.*` - Access to content creation tools

## ✅ Testing Your Setup

### **Test Admin Access**
```bash
# Test basic access
/udx
# Should open main menu

# Test admin access
/udx admin
# Should open admin hub

# Test editor access
/udx editor
# Should open editor tools
```

### **Check Your Permissions**
```bash
# View your permissions
/lp user YourUsername permission info

# Check specific permission
/lp user YourUsername permission check udx.admin
```

### **Test Player Access**
```bash
# Switch to a test player account and try:
/udx
# Should open main menu for players
```

## 🔧 Troubleshooting

### **"You don't have permission" Error**

1. **Check your username spelling**:
   ```bash
   /lp user YourExactMinecraftName permission set udx.* true
   ```

2. **Verify LuckPerms is working**:
   ```bash
   /lp info
   ```

3. **Check if permission was applied**:
   ```bash
   /lp user YourUsername permission info
   ```

### **Commands Not Working**

1. **Verify plugin is loaded**:
   ```bash
   /plugins
   # Look for "UltimateDungeonX" in green
   ```

2. **Check server console for errors**

3. **Try relogging** to refresh permissions

### **Players Can't Access**

1. **Check default group permissions**:
   ```bash
   /lp group default permission info
   ```

2. **Verify players are in default group**:
   ```bash
   /lp user PlayerName info
   ```

## 📝 Quick Reference

### **Essential Commands**
```bash
# Give yourself full access
/lp user YourUsername permission set udx.* true

# Give players basic access
/lp group default permission set udx.use true
/lp group default permission set udx.join true
/lp group default permission set udx.party true

# Test access
/udx
/udx admin
```

### **Permission Hierarchy**
- **udx.*** = Everything (admins)
- **udx.use, udx.join, udx.party** = Basic player access
- **udx.editor.*** = Content creation (moderators)
- **udx.admin.*** = Server management (admins only)

---

**After running these commands, you'll have full admin access and your players will be able to use all dungeon features!**
