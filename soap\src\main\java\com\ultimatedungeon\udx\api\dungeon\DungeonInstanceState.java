package com.ultimatedungeon.udx.api.dungeon;

/**
 * Enumeration of possible dungeon instance states.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public enum DungeonInstanceState {
    
    /**
     * Instance is being created (world generation, room placement).
     */
    CREATING,
    
    /**
     * Instance is ready for players to join.
     */
    READY,
    
    /**
     * Instance is actively running with players inside.
     */
    RUNNING,
    
    /**
     * Instance is in the completion phase (rewards, cleanup).
     */
    COMPLETING,
    
    /**
     * Instance completed successfully.
     */
    COMPLETED,
    
    /**
     * Instance failed (party wiped, timeout, error).
     */
    FAILED,
    
    /**
     * Instance is being disposed (world cleanup, data saving).
     */
    DISPOSING,
    
    /**
     * Instance has been disposed and is no longer accessible.
     */
    DISPOSED
}
