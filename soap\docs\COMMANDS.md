# UltimateDungeonX Commands Reference

This document provides a complete reference for all commands available in UltimateDungeonX.

## Overview

UltimateDungeonX follows a **GUI-first design philosophy**. Commands serve primarily as entry points to open menus rather than performing direct actions. This ensures a consistent, user-friendly experience across all features.

## Player Commands

### `/udx`
**Description**: Opens the main player menu  
**Permission**: `udx.use` (default: true)  
**Aliases**: `/ultimatedungeon`, `/dungeonx`  
**Usage**: `/udx`

Opens the primary interface where players can:
- Browse and join dungeons
- Manage parties and invitations
- View achievements and progression
- Access settings and preferences

**Example**:
```
/udx
```

### `/udx spectate`
**Description**: Opens the spectate menu to watch active dungeon runs  
**Permission**: `udx.spectate` (default: true)  
**Aliases**: `/udxspectate`  
**Usage**: `/udx spectate`

Allows players to spectate ongoing dungeon runs (if enabled by server configuration).

**Example**:
```
/udx spectate
```

## Administrative Commands

### `/udx admin`
**Description**: Opens the administrative hub  
**Permission**: `udx.admin` (default: op)  
**Aliases**: `/udxadmin`  
**Usage**: `/udx admin`

Provides access to:
- Dungeon management (create, edit, delete)
- Active instance monitoring
- Content editors (rooms, mobs, bosses, loot)
- System settings and configuration
- Performance monitoring and debug tools

**Example**:
```
/udx admin
```

### `/udx editor`
**Description**: Opens the content editor hub  
**Permission**: `udx.editor.*` (default: op)  
**Aliases**: `/udxeditor`  
**Usage**: `/udx editor`

Quick access to content creation tools:
- Room editor for building dungeon layouts
- Mob editor for custom creature definitions
- Boss editor for multi-phase encounters
- Loot editor for reward tables

**Example**:
```
/udx editor
```

### `/udx portal`
**Description**: Gives a Portal Keystone item for creating dungeon portals  
**Permission**: `udx.admin.portal` (default: op)  
**Aliases**: `/udxportal`  
**Usage**: `/udx portal [dungeon_id]`

**Parameters**:
- `dungeon_id` (optional): Bind the portal to a specific dungeon

Creates a Portal Keystone item that can be placed in the world to create permanent dungeon entry points.

**Examples**:
```
/udx portal                    # Generic portal (opens dungeon browser)
/udx portal crypt_of_echoes    # Direct portal to specific dungeon
```

## Command Details

### Permission System

UltimateDungeonX uses a hierarchical permission system:

```
udx.*                          # All permissions
├── udx.use                    # Basic plugin access
├── udx.join                   # Join dungeon runs
├── udx.party                  # Create and manage parties
├── udx.spectate               # Spectate active runs
├── udx.admin.*                # All admin permissions
│   ├── udx.admin              # Access admin hub
│   ├── udx.admin.create       # Create dungeons and instances
│   ├── udx.admin.delete       # Delete dungeons and stop instances
│   ├── udx.admin.manage       # Manage existing content
│   ├── udx.admin.teleport     # Teleport to instances
│   ├── udx.admin.force        # Force start/stop operations
│   ├── udx.admin.portal       # Create portal keystones
│   └── udx.admin.debug        # Access debug tools
└── udx.editor.*               # All editor permissions
    ├── udx.editor.room        # Room editor access
    ├── udx.editor.spawner     # Spawner editor access
    ├── udx.editor.boss        # Boss editor access
    └── udx.editor.loot        # Loot editor access
```

### Default Permissions

**All Players** (default: true):
- `udx.use` - Basic plugin access
- `udx.join` - Join dungeon runs
- `udx.party` - Party management
- `udx.spectate` - Spectate runs

**Operators** (default: op):
- `udx.admin.*` - All administrative functions
- `udx.editor.*` - All content editing tools

### Command Aliases

For convenience, several command aliases are available:

| Primary Command | Aliases |
|----------------|---------|
| `/udx` | `/ultimatedungeon`, `/dungeonx` |
| `/udx admin` | `/udxadmin` |
| `/udx editor` | `/udxeditor` |
| `/udx portal` | `/udxportal` |
| `/udx spectate` | `/udxspectate` |

## GUI Navigation

### Main Menu (`/udx`)

```
┌─────────────────────────────────────────────────────┐
│                 UltimateDungeonX                    │
├─────────────────────────────────────────────────────┤
│  [🧭 Play]     [👥 Parties]   [⭐ Achievements]    │
│                                                     │
│              [⚙️ Settings]                          │
└─────────────────────────────────────────────────────┘
```

**Navigation Options**:
- **Play**: Browse and join dungeons
- **Parties**: Create/manage parties, view invitations
- **Achievements**: View progress, leaderboards, statistics
- **Settings**: Configure preferences, GUI options

### Admin Hub (`/udx admin`)

```
┌─────────────────────────────────────────────────────┐
│                  Admin Hub                          │
├─────────────────────────────────────────────────────┤
│  [🏰 Dungeons]  [🌍 Instances]  [🛠️ Editors]      │
│                                                     │
│  [⚙️ System]    [📊 Analytics]   [🔧 Debug]        │
└─────────────────────────────────────────────────────┘
```

**Admin Functions**:
- **Dungeons**: Create, edit, manage dungeon definitions
- **Instances**: Monitor active runs, performance metrics
- **Editors**: Access content creation tools
- **System**: Global settings, affix rotation, maintenance
- **Analytics**: Usage statistics, performance reports
- **Debug**: Performance monitoring, troubleshooting tools

## Quick Reference

### Essential Commands for New Users

1. **Start Playing**: `/udx` → Play → Browse Dungeons
2. **Create Party**: `/udx` → Parties → Create Party
3. **View Progress**: `/udx` → Achievements

### Essential Commands for Admins

1. **Admin Panel**: `/udx admin`
2. **Create Dungeon**: `/udx admin` → Dungeons → New Dungeon
3. **Edit Content**: `/udx editor`
4. **Monitor Server**: `/udx admin` → Analytics → Performance
5. **Create Portal**: `/udx portal`

### Troubleshooting Commands

1. **Check Permissions**: Use your permission plugin to verify `udx.*` permissions
2. **Debug Mode**: `/udx admin` → Debug → Performance Monitor
3. **View Logs**: Check server console for `[UltimateDungeonX]` messages

## Command Examples

### Player Workflow
```bash
# Join the server and start playing
/udx                           # Open main menu
# Click "Play" → Browse dungeons → Select dungeon → Queue

# Create a party with friends
/udx                           # Open main menu  
# Click "Parties" → Create Party → Invite players

# Check your progress
/udx                           # Open main menu
# Click "Achievements" → View progress and leaderboards
```

### Admin Workflow
```bash
# Set up a new dungeon
/udx admin                     # Open admin hub
# Click "Dungeons" → New Dungeon → Configure settings

# Create rooms for the dungeon
/udx editor                    # Open editor hub
# Click "Room Editor" → Select area → Add connectors → Save

# Add custom mobs
/udx editor                    # Open editor hub  
# Click "Mob Editor" → New Mob → Configure attributes → Save

# Create a lobby portal
/udx portal crypt_of_echoes    # Get portal keystone
# Place keystone in lobby area
```

## Integration with Other Plugins

### Permission Plugins (LuckPerms, PermissionsEx, etc.)

**Grant basic access to all players**:
```bash
# LuckPerms example
/lp group default permission set udx.use true
/lp group default permission set udx.join true
/lp group default permission set udx.party true
```

**Grant admin access to staff**:
```bash
# LuckPerms example
/lp group admin permission set udx.admin.* true
/lp group moderator permission set udx.editor.* true
```

### Economy Plugins (Vault)

UltimateDungeonX automatically integrates with Vault-compatible economy plugins. No additional commands needed - rewards are automatically deposited to player accounts.

### PlaceholderAPI

Available placeholders for use in other plugins:
- `%udx_player_level%` - Player's dungeon level
- `%udx_completed_runs%` - Total completed runs
- `%udx_current_dungeon%` - Currently active dungeon (if any)
- `%udx_party_size%` - Current party size
- `%udx_achievements%` - Number of achievements unlocked

## Configuration Commands

While UltimateDungeonX is GUI-first, some configuration can be done via files:

### Config File Locations
- Main config: `plugins/UltimateDungeonX/config.yml`
- Player data: `plugins/UltimateDungeonX/players.db` (SQLite)
- Dungeons: `plugins/UltimateDungeonX/dungeons/*/`

### Reload Configuration
Configuration changes require a server restart or plugin reload. Use your server's reload command:
```bash
/reload confirm                # Full server reload (not recommended)
/plugman reload UltimateDungeonX  # Plugin-specific reload (if PlugMan installed)
```

## Support and Troubleshooting

### Common Issues

**"Unknown command" error**:
- Verify the plugin is installed and enabled
- Check server console for loading errors
- Ensure you have the required permissions

**GUI not opening**:
- Check permissions (`udx.use` for players, `udx.admin` for admin features)
- Verify no inventory is currently open
- Check for plugin conflicts

**Performance issues**:
- Use `/udx admin` → Debug → Performance Monitor
- Check server TPS and memory usage
- Review configuration settings in `config.yml`

### Getting Help

1. **In-Game**: `/udx admin` → Debug → Copy Debug Info
2. **Documentation**: Check the `docs/` folder for detailed guides
3. **Logs**: Server console shows detailed error messages
4. **Configuration**: Review `docs/CONFIG.md` for all settings

---

*This command reference provides quick access to all UltimateDungeonX functionality. For detailed feature explanations, see the other documentation files in the `docs/` folder.*
