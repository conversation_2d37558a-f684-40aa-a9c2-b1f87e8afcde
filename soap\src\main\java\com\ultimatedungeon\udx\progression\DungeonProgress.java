package com.ultimatedungeon.udx.progression;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.*;

/**
 * Tracks a player's progress for a specific dungeon.
 * 
 * <p>This class maintains completion records across different difficulties,
 * personal best times, death counts, and other dungeon-specific statistics.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class DungeonProgress {
    
    private final String dungeonId;
    private final Map<String, DifficultyProgress> difficultyProgress;
    private Instant firstCompletion;
    private Instant lastCompletion;
    private int totalCompletions;
    private int totalDeaths;
    private long totalPlayTime;
    private int bestScore;
    
    public DungeonProgress(@NotNull String dungeonId) {
        this.dungeonId = dungeonId;
        this.difficultyProgress = new HashMap<>();
        this.totalCompletions = 0;
        this.totalDeaths = 0;
        this.totalPlayTime = 0;
        this.bestScore = 0;
    }
    
    /**
     * Records a completion for this dungeon.
     * 
     * @param difficulty the difficulty tier
     * @param completionTime the completion time in milliseconds
     * @param deaths the number of deaths
     * @param score the score achieved
     */
    public void recordCompletion(@NotNull String difficulty, long completionTime, int deaths, int score) {
        DifficultyProgress progress = difficultyProgress.computeIfAbsent(difficulty,
            k -> new DifficultyProgress(difficulty));
        
        progress.recordCompletion(completionTime, deaths, score);
        
        // Update overall progress
        totalCompletions++;
        totalDeaths += deaths;
        totalPlayTime += completionTime;
        
        if (score > bestScore) {
            bestScore = score;
        }
        
        Instant now = Instant.now();
        if (firstCompletion == null) {
            firstCompletion = now;
        }
        lastCompletion = now;
    }
    
    /**
     * Gets the progress for a specific difficulty.
     * 
     * @param difficulty the difficulty tier
     * @return the difficulty progress, or null if not found
     */
    @Nullable
    public DifficultyProgress getDifficultyProgress(@NotNull String difficulty) {
        return difficultyProgress.get(difficulty);
    }
    
    /**
     * Gets all completed difficulties.
     * 
     * @return set of completed difficulty names
     */
    @NotNull
    public Set<String> getCompletedDifficulties() {
        return Collections.unmodifiableSet(difficultyProgress.keySet());
    }
    
    /**
     * Gets the best completion time across all difficulties.
     * 
     * @return the best time in milliseconds, or Long.MAX_VALUE if none
     */
    public long getBestTime() {
        return difficultyProgress.values().stream()
            .mapToLong(DifficultyProgress::getBestTime)
            .min()
            .orElse(Long.MAX_VALUE);
    }
    
    /**
     * Gets the best completion time for a specific difficulty.
     * 
     * @param difficulty the difficulty tier
     * @return the best time in milliseconds, or Long.MAX_VALUE if none
     */
    public long getBestTime(@NotNull String difficulty) {
        DifficultyProgress progress = difficultyProgress.get(difficulty);
        return progress != null ? progress.getBestTime() : Long.MAX_VALUE;
    }
    
    /**
     * Checks if a difficulty has been completed.
     * 
     * @param difficulty the difficulty tier
     * @return true if completed, false otherwise
     */
    public boolean isCompleted(@NotNull String difficulty) {
        return difficultyProgress.containsKey(difficulty);
    }
    
    /**
     * Gets the completion count for a specific difficulty.
     * 
     * @param difficulty the difficulty tier
     * @return the completion count
     */
    public int getCompletionCount(@NotNull String difficulty) {
        DifficultyProgress progress = difficultyProgress.get(difficulty);
        return progress != null ? progress.getCompletions() : 0;
    }
    
    // Getters
    
    @NotNull
    public String getDungeonId() {
        return dungeonId;
    }
    
    @NotNull
    public Map<String, DifficultyProgress> getDifficultyProgress() {
        return Collections.unmodifiableMap(difficultyProgress);
    }
    
    @Nullable
    public Instant getFirstCompletion() {
        return firstCompletion;
    }
    
    @Nullable
    public Instant getLastCompletion() {
        return lastCompletion;
    }
    
    public int getTotalCompletions() {
        return totalCompletions;
    }
    
    public int getTotalDeaths() {
        return totalDeaths;
    }
    
    public long getTotalPlayTime() {
        return totalPlayTime;
    }
    
    public int getBestScore() {
        return bestScore;
    }
    
    // Setters for data loading
    
    public void setFirstCompletion(@Nullable Instant firstCompletion) {
        this.firstCompletion = firstCompletion;
    }
    
    public void setLastCompletion(@Nullable Instant lastCompletion) {
        this.lastCompletion = lastCompletion;
    }
    
    public void setTotalCompletions(int totalCompletions) {
        this.totalCompletions = totalCompletions;
    }
    
    public void setTotalDeaths(int totalDeaths) {
        this.totalDeaths = totalDeaths;
    }
    
    public void setTotalPlayTime(long totalPlayTime) {
        this.totalPlayTime = totalPlayTime;
    }
    
    public void setBestScore(int bestScore) {
        this.bestScore = bestScore;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DungeonProgress that = (DungeonProgress) obj;
        return Objects.equals(dungeonId, that.dungeonId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(dungeonId);
    }
    
    @Override
    public String toString() {
        return "DungeonProgress{" +
            "dungeonId='" + dungeonId + '\'' +
            ", completions=" + totalCompletions +
            ", bestTime=" + getBestTime() +
            ", bestScore=" + bestScore +
            '}';
    }
    
    /**
     * Represents progress for a specific difficulty tier.
     */
    public static class DifficultyProgress {
        
        private final String difficulty;
        private int completions;
        private long bestTime;
        private int fewestDeaths;
        private int bestScore;
        private Instant firstCompletion;
        private Instant lastCompletion;
        
        public DifficultyProgress(@NotNull String difficulty) {
            this.difficulty = difficulty;
            this.completions = 0;
            this.bestTime = Long.MAX_VALUE;
            this.fewestDeaths = Integer.MAX_VALUE;
            this.bestScore = 0;
        }
        
        /**
         * Records a completion for this difficulty.
         * 
         * @param completionTime the completion time in milliseconds
         * @param deaths the number of deaths
         * @param score the score achieved
         */
        public void recordCompletion(long completionTime, int deaths, int score) {
            completions++;
            
            if (completionTime < bestTime) {
                bestTime = completionTime;
            }
            
            if (deaths < fewestDeaths) {
                fewestDeaths = deaths;
            }
            
            if (score > bestScore) {
                bestScore = score;
            }
            
            Instant now = Instant.now();
            if (firstCompletion == null) {
                firstCompletion = now;
            }
            lastCompletion = now;
        }
        
        // Getters
        
        @NotNull
        public String getDifficulty() {
            return difficulty;
        }
        
        public int getCompletions() {
            return completions;
        }
        
        public long getBestTime() {
            return bestTime;
        }
        
        public int getFewestDeaths() {
            return fewestDeaths;
        }
        
        public int getBestScore() {
            return bestScore;
        }
        
        @Nullable
        public Instant getFirstCompletion() {
            return firstCompletion;
        }
        
        @Nullable
        public Instant getLastCompletion() {
            return lastCompletion;
        }
        
        // Setters for data loading
        
        public void setCompletions(int completions) {
            this.completions = completions;
        }
        
        public void setBestTime(long bestTime) {
            this.bestTime = bestTime;
        }
        
        public void setFewestDeaths(int fewestDeaths) {
            this.fewestDeaths = fewestDeaths;
        }
        
        public void setBestScore(int bestScore) {
            this.bestScore = bestScore;
        }
        
        public void setFirstCompletion(@Nullable Instant firstCompletion) {
            this.firstCompletion = firstCompletion;
        }
        
        public void setLastCompletion(@Nullable Instant lastCompletion) {
            this.lastCompletion = lastCompletion;
        }
        
        @Override
        public String toString() {
            return "DifficultyProgress{" +
                "difficulty='" + difficulty + '\'' +
                ", completions=" + completions +
                ", bestTime=" + bestTime +
                ", bestScore=" + bestScore +
                '}';
        }
    }
}
