package com.ultimatedungeon.udx.api.loot;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing loot tables and rewards.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public interface LootService {
    
    /**
     * Gets all available loot tables.
     * 
     * @return List of all loot tables
     */
    @NotNull
    List<LootTableData> getAllLootTables();
    
    /**
     * Gets a loot table by ID.
     * 
     * @param tableId The loot table ID
     * @return The loot table, or null if not found
     */
    @Nullable
    LootTableData getLootTable(@NotNull String tableId);
    
    /**
     * Rolls loot from a specific table.
     * 
     * @param tableId The loot table ID
     * @param player The player rolling loot (for modifiers)
     * @return List of rolled items
     */
    @NotNull
    List<ItemStack> rollLoot(@NotNull String tableId, @NotNull Player player);
    
    /**
     * Rolls loot from a specific table with custom modifiers.
     * 
     * @param tableId The loot table ID
     * @param player The player rolling loot
     * @param luckModifier Luck modifier (1.0 = normal, higher = better)
     * @param quantityModifier Quantity modifier (1.0 = normal, higher = more items)
     * @return List of rolled items
     */
    @NotNull
    List<ItemStack> rollLoot(@NotNull String tableId, @NotNull Player player, double luckModifier, double quantityModifier);
    
    /**
     * Gets the rarity of an item.
     * 
     * @param item The item to check
     * @return The item rarity, or null if not a special item
     */
    @Nullable
    ItemRarity getItemRarity(@NotNull ItemStack item);
    
    /**
     * Applies rarity effects to an item (glow, name coloring, etc.).
     * 
     * @param item The item to modify
     * @param rarity The rarity to apply
     * @return The modified item
     */
    @NotNull
    ItemStack applyRarityEffects(@NotNull ItemStack item, @NotNull ItemRarity rarity);
    
    /**
     * Creates a completion crate for a dungeon.
     * 
     * @param dungeonId The dungeon ID
     * @param difficulty The difficulty tier
     * @param player The player who completed the dungeon
     * @return The completion crate item
     */
    @NotNull
    ItemStack createCompletionCrate(@NotNull String dungeonId, @NotNull String difficulty, @NotNull Player player);
    
    /**
     * Opens a completion crate and gives rewards to the player.
     * 
     * @param player The player opening the crate
     * @param crateItem The crate item
     * @return True if the crate was opened successfully
     */
    boolean openCompletionCrate(@NotNull Player player, @NotNull ItemStack crateItem);
    
    /**
     * Gets the drop rates for a specific loot table.
     * 
     * @param tableId The loot table ID
     * @return List of drop rate information
     */
    @NotNull
    List<DropRateInfo> getDropRates(@NotNull String tableId);
    
    /**
     * Checks if a chest should regenerate loot.
     * 
     * @param chestId The unique chest identifier
     * @param dungeonInstanceId The dungeon instance ID
     * @return True if the chest should regenerate
     */
    boolean shouldRegenerateLoot(@NotNull String chestId, @NotNull UUID dungeonInstanceId);
    
    /**
     * Marks a chest as looted by a player.
     * 
     * @param chestId The unique chest identifier
     * @param dungeonInstanceId The dungeon instance ID
     * @param playerId The player who looted the chest
     */
    void markChestLooted(@NotNull String chestId, @NotNull UUID dungeonInstanceId, @NotNull UUID playerId);
    
    /**
     * Gets the loot history for a player.
     * 
     * @param playerId The player UUID
     * @param limit The maximum number of entries to return
     * @return List of loot history entries
     */
    @NotNull
    List<LootHistoryEntry> getLootHistory(@NotNull UUID playerId, int limit);
    
    /**
     * Awards currency to a player (requires Vault).
     * 
     * @param playerId The player UUID
     * @param amount The amount to award
     * @return True if currency was awarded successfully
     */
    boolean awardCurrency(@NotNull UUID playerId, double amount);
    
    /**
     * Gets the total value of items in a loot roll.
     * 
     * @param items The items to evaluate
     * @return The total estimated value
     */
    double calculateLootValue(@NotNull List<ItemStack> items);
}
