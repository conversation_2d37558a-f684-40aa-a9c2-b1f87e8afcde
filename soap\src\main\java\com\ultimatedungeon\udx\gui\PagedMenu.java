package com.ultimatedungeon.udx.gui;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Extended menu class that supports pagination for large lists of items.
 * 
 * <p>This class provides automatic pagination with navigation buttons
 * and customizable item rendering for each page.</p>
 * 
 * @param <T> the type of items being displayed
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public abstract class PagedMenu<T> extends Menu {
    
    protected final List<T> items;
    protected int currentPage;
    protected final int itemsPerPage;
    protected final int startSlot;
    protected final int endSlot;
    
    /**
     * Creates a new paged menu.
     * 
     * @param player the player viewing the menu
     * @param title the menu title
     * @param size the menu size (must be multiple of 9)
     * @param menuRegistry the menu registry
     * @param items the list of items to display
     */
    protected PagedMenu(@NotNull Player player, @NotNull Component title, int size, 
                       @NotNull MenuRegistry menuRegistry, @NotNull List<T> items) {
        super(player, title, size, menuRegistry);
        this.items = new ArrayList<>(items);
        this.currentPage = 0;
        
        // Calculate pagination area (excluding navigation rows)
        this.startSlot = 9; // Skip top row for headers
        this.endSlot = size - 18; // Skip bottom two rows for navigation
        this.itemsPerPage = endSlot - startSlot + 1;
    }
    
    /**
     * Creates a new paged menu with default pagination area.
     * 
     * @param player the player viewing the menu
     * @param title the menu title
     * @param menuRegistry the menu registry
     * @param items the list of items to display
     */
    protected PagedMenu(@NotNull Player player, @NotNull Component title, 
                       @NotNull MenuRegistry menuRegistry, @NotNull List<T> items) {
        this(player, title, 54, menuRegistry, items);
    }
    
    @Override
    protected void setupMenu() {
        // Clear previous content
        inventory.clear();
        clickHandlers.clear();
        
        // Setup header
        setupHeader();
        
        // Setup pagination
        setupPagination();
        
        // Setup items for current page
        setupPageItems();
        
        // Setup navigation
        setupNavigation();
        
        // Fill empty slots
        fillEmptySlots();
    }
    
    /**
     * Sets up the header area (top row).
     */
    protected void setupHeader() {
        // Default implementation - override in subclasses
        Component pageInfo = Component.text("Page " + (currentPage + 1) + " of " + getTotalPages())
            .color(NamedTextColor.YELLOW);
        
        setItem(4, createItem(Material.BOOK, pageInfo));
    }
    
    /**
     * Sets up pagination navigation buttons.
     */
    protected void setupPagination() {
        // Previous page button
        if (hasPreviousPage()) {
            ItemStack prevButton = createItem(
                Material.ARROW,
                Component.text("← Previous Page").color(NamedTextColor.GREEN),
                createLore("Click to go to page " + currentPage)
            );
            
            setItem(size - 18, prevButton, clickType -> {
                if (clickType == ClickType.LEFT) {
                    previousPage();
                }
            });
        }
        
        // Next page button
        if (hasNextPage()) {
            ItemStack nextButton = createItem(
                Material.ARROW,
                Component.text("Next Page →").color(NamedTextColor.GREEN),
                createLore("Click to go to page " + (currentPage + 2))
            );
            
            setItem(size - 10, nextButton, clickType -> {
                if (clickType == ClickType.LEFT) {
                    nextPage();
                }
            });
        }
        
        // Page info in center
        Component pageInfo = Component.text("Page " + (currentPage + 1) + "/" + getTotalPages())
            .color(NamedTextColor.YELLOW);
        
        setItem(size - 14, createItem(Material.PAPER, pageInfo));
    }
    
    /**
     * Sets up items for the current page.
     */
    protected void setupPageItems() {
        List<T> pageItems = getPageItems();
        
        for (int i = 0; i < pageItems.size() && i < itemsPerPage; i++) {
            T item = pageItems.get(i);
            int slot = startSlot + i;
            
            ItemStack displayItem = createDisplayItem(item, i);
            ClickHandler handler = createClickHandler(item, i);
            
            setItem(slot, displayItem, handler);
        }
    }
    
    /**
     * Creates the display item for a given data item.
     * 
     * @param item the data item
     * @param index the index within the current page
     * @return the ItemStack to display
     */
    @NotNull
    protected abstract ItemStack createDisplayItem(@NotNull T item, int index);
    
    /**
     * Creates the click handler for a given data item.
     * 
     * @param item the data item
     * @param index the index within the current page
     * @return the click handler, or null if no handler needed
     */
    @NotNull
    protected abstract ClickHandler createClickHandler(@NotNull T item, int index);
    
    /**
     * Gets the items for the current page.
     */
    @NotNull
    protected List<T> getPageItems() {
        int startIndex = currentPage * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, items.size());
        
        if (startIndex >= items.size()) {
            return new ArrayList<>();
        }
        
        return items.subList(startIndex, endIndex);
    }
    
    /**
     * Goes to the next page.
     */
    public void nextPage() {
        if (hasNextPage()) {
            currentPage++;
            refresh();
        }
    }
    
    /**
     * Goes to the previous page.
     */
    public void previousPage() {
        if (hasPreviousPage()) {
            currentPage--;
            refresh();
        }
    }
    
    /**
     * Goes to a specific page.
     * 
     * @param page the page number (0-based)
     */
    public void goToPage(int page) {
        if (page >= 0 && page < getTotalPages()) {
            currentPage = page;
            refresh();
        }
    }
    
    /**
     * Checks if there is a next page.
     */
    public boolean hasNextPage() {
        return currentPage < getTotalPages() - 1;
    }
    
    /**
     * Checks if there is a previous page.
     */
    public boolean hasPreviousPage() {
        return currentPage > 0;
    }
    
    /**
     * Gets the total number of pages.
     */
    public int getTotalPages() {
        if (items.isEmpty()) {
            return 1;
        }
        return (int) Math.ceil((double) items.size() / itemsPerPage);
    }
    
    /**
     * Gets the current page number (0-based).
     */
    public int getCurrentPage() {
        return currentPage;
    }
    
    /**
     * Gets the total number of items.
     */
    public int getTotalItems() {
        return items.size();
    }
    
    /**
     * Adds an item to the list and refreshes if necessary.
     */
    public void addItem(@NotNull T item) {
        items.add(item);
        refresh();
    }
    
    /**
     * Removes an item from the list and refreshes if necessary.
     */
    public boolean removeItem(@NotNull T item) {
        boolean removed = items.remove(item);
        if (removed) {
            // Adjust current page if we're now beyond the last page
            int maxPage = getTotalPages() - 1;
            if (currentPage > maxPage) {
                currentPage = Math.max(0, maxPage);
            }
            refresh();
        }
        return removed;
    }
    
    /**
     * Clears all items and refreshes.
     */
    public void clearItems() {
        items.clear();
        currentPage = 0;
        refresh();
    }
    
    /**
     * Updates the item list and refreshes.
     */
    public void updateItems(@NotNull List<T> newItems) {
        items.clear();
        items.addAll(newItems);
        
        // Adjust current page if necessary
        int maxPage = getTotalPages() - 1;
        if (currentPage > maxPage) {
            currentPage = Math.max(0, maxPage);
        }
        
        refresh();
    }
    
    /**
     * Gets a copy of the current items list.
     */
    @NotNull
    public List<T> getItems() {
        return new ArrayList<>(items);
    }
    
    /**
     * Checks if the menu is empty.
     */
    public boolean isEmpty() {
        return items.isEmpty();
    }
    
    /**
     * Gets the number of items per page.
     */
    public int getItemsPerPage() {
        return itemsPerPage;
    }
    
    /**
     * Gets the start slot for items.
     */
    public int getStartSlot() {
        return startSlot;
    }
    
    /**
     * Gets the end slot for items.
     */
    public int getEndSlot() {
        return endSlot;
    }
}
