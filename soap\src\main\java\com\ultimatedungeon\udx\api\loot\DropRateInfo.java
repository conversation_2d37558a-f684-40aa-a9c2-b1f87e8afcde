package com.ultimatedungeon.udx.api.loot;

import org.jetbrains.annotations.NotNull;

/**
 * Immutable representation of drop rate information for API consumers.
 */
public record DropRateInfo(
    @NotNull String entryId,
    @NotNull String itemName,
    double weight,
    double percentage,
    @NotNull ItemRarity rarity,
    int minQuantity,
    int maxQuantity
) {
    
    public DropRateInfo {
        if (entryId == null || entryId.isBlank()) {
            throw new IllegalArgumentException("Entry ID cannot be null or blank");
        }
        if (itemName == null || itemName.isBlank()) {
            throw new IllegalArgumentException("Item name cannot be null or blank");
        }
        if (weight < 0) {
            throw new IllegalArgumentException("Weight cannot be negative");
        }
        if (percentage < 0 || percentage > 100) {
            throw new IllegalArgumentException("Percentage must be between 0 and 100");
        }
        if (rarity == null) {
            throw new IllegalArgumentException("Rarity cannot be null");
        }
        if (minQuantity < 0) {
            throw new IllegalArgumentException("Min quantity cannot be negative");
        }
        if (maxQuantity < minQuantity) {
            throw new IllegalArgumentException("Max quantity must be >= min quantity");
        }
    }
    
    /**
     * Gets a formatted string representation of the drop rate.
     * 
     * @return Formatted drop rate string
     */
    @NotNull
    public String getFormattedDropRate() {
        if (percentage >= 10) {
            return String.format("%.1f%%", percentage);
        } else if (percentage >= 1) {
            return String.format("%.2f%%", percentage);
        } else {
            return String.format("%.3f%%", percentage);
        }
    }
    
    /**
     * Gets a formatted string representation of the quantity range.
     * 
     * @return Formatted quantity string
     */
    @NotNull
    public String getFormattedQuantity() {
        if (minQuantity == maxQuantity) {
            return String.valueOf(minQuantity);
        } else {
            return minQuantity + "-" + maxQuantity;
        }
    }
}
