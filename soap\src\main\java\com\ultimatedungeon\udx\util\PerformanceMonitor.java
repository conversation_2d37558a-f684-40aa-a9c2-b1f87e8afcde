package com.ultimatedungeon.udx.util;

import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Logger;

/**
 * Monitors server performance and provides metrics for optimization.
 * 
 * <p>This class tracks tick times, memory usage, entity counts, and other
 * performance-critical metrics to help identify bottlenecks and optimize
 * plugin performance.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class PerformanceMonitor {
    
    private final Plugin plugin;
    private final Logger logger;
    private final MemoryMXBean memoryBean;
    
    // Monitoring state
    private BukkitTask monitoringTask;
    private boolean isMonitoring;
    
    // Performance metrics
    private final AtomicLong totalTickTime;
    private final AtomicLong tickCount;
    private final ConcurrentMap<String, Long> operationTimes;
    private final ConcurrentMap<String, Integer> operationCounts;
    
    // Memory tracking
    private long lastHeapUsed;
    private long lastNonHeapUsed;
    private long peakHeapUsed;
    private long peakNonHeapUsed;
    
    // Thresholds for warnings
    private static final double TICK_TIME_WARNING_MS = 50.0; // Warn if tick takes >50ms
    private static final double MEMORY_WARNING_PERCENT = 85.0; // Warn if memory usage >85%
    private static final long MONITORING_INTERVAL_TICKS = 20; // Monitor every second
    
    public PerformanceMonitor(@NotNull Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.memoryBean = ManagementFactory.getMemoryMXBean();
        
        this.totalTickTime = new AtomicLong(0);
        this.tickCount = new AtomicLong(0);
        this.operationTimes = new ConcurrentHashMap<>();
        this.operationCounts = new ConcurrentHashMap<>();
        
        this.isMonitoring = false;
        updateMemoryMetrics();
    }
    
    /**
     * Starts performance monitoring.
     */
    public void startMonitoring() {
        if (isMonitoring) {
            return;
        }
        
        isMonitoring = true;
        monitoringTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateMetrics();
            }
        }.runTaskTimer(plugin, 0L, MONITORING_INTERVAL_TICKS);
        
        logger.info("Performance monitoring started");
    }
    
    /**
     * Stops performance monitoring.
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            return;
        }
        
        isMonitoring = false;
        if (monitoringTask != null) {
            monitoringTask.cancel();
            monitoringTask = null;
        }
        
        logger.info("Performance monitoring stopped");
    }
    
    /**
     * Records the execution time of an operation.
     * 
     * @param operationName Name of the operation
     * @param executionTimeNanos Execution time in nanoseconds
     */
    public void recordOperation(@NotNull String operationName, long executionTimeNanos) {
        operationTimes.merge(operationName, executionTimeNanos, Long::sum);
        operationCounts.merge(operationName, 1, Integer::sum);
        
        // Check for performance warnings
        double executionTimeMs = executionTimeNanos / 1_000_000.0;
        if (executionTimeMs > TICK_TIME_WARNING_MS) {
            logger.warning(String.format("Slow operation detected: %s took %.2fms", 
                operationName, executionTimeMs));
        }
    }
    
    /**
     * Records a tick execution time.
     * 
     * @param tickTimeNanos Tick time in nanoseconds
     */
    public void recordTick(long tickTimeNanos) {
        totalTickTime.addAndGet(tickTimeNanos);
        tickCount.incrementAndGet();
        
        // Check for tick time warnings
        double tickTimeMs = tickTimeNanos / 1_000_000.0;
        if (tickTimeMs > TICK_TIME_WARNING_MS) {
            logger.warning(String.format("Slow tick detected: %.2fms (target: 50ms)", tickTimeMs));
        }
    }
    
    /**
     * Updates all performance metrics.
     */
    private void updateMetrics() {
        updateMemoryMetrics();
        checkMemoryWarnings();
    }
    
    /**
     * Updates memory usage metrics.
     */
    private void updateMemoryMetrics() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        lastHeapUsed = heapUsage.getUsed();
        lastNonHeapUsed = nonHeapUsage.getUsed();
        
        // Track peaks
        if (lastHeapUsed > peakHeapUsed) {
            peakHeapUsed = lastHeapUsed;
        }
        if (lastNonHeapUsed > peakNonHeapUsed) {
            peakNonHeapUsed = lastNonHeapUsed;
        }
    }
    
    /**
     * Checks for memory usage warnings.
     */
    private void checkMemoryWarnings() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        double heapPercent = (double) heapUsage.getUsed() / heapUsage.getMax() * 100.0;
        
        if (heapPercent > MEMORY_WARNING_PERCENT) {
            logger.warning(String.format("High memory usage detected: %.1f%% heap used (%d MB / %d MB)",
                heapPercent, heapUsage.getUsed() / 1024 / 1024, heapUsage.getMax() / 1024 / 1024));
        }
    }
    
    /**
     * Gets the average tick time in milliseconds.
     */
    public double getAverageTickTime() {
        long ticks = tickCount.get();
        if (ticks == 0) return 0.0;
        
        return (totalTickTime.get() / 1_000_000.0) / ticks;
    }
    
    /**
     * Gets the total number of ticks recorded.
     */
    public long getTotalTicks() {
        return tickCount.get();
    }
    
    /**
     * Gets the average execution time for an operation in milliseconds.
     */
    public double getAverageOperationTime(@NotNull String operationName) {
        Long totalTime = operationTimes.get(operationName);
        Integer count = operationCounts.get(operationName);
        
        if (totalTime == null || count == null || count == 0) {
            return 0.0;
        }
        
        return (totalTime / 1_000_000.0) / count;
    }
    
    /**
     * Gets the total execution count for an operation.
     */
    public int getOperationCount(@NotNull String operationName) {
        return operationCounts.getOrDefault(operationName, 0);
    }
    
    /**
     * Gets the total execution time for an operation in milliseconds.
     */
    public double getTotalOperationTime(@NotNull String operationName) {
        Long totalTime = operationTimes.get(operationName);
        return totalTime != null ? totalTime / 1_000_000.0 : 0.0;
    }
    
    /**
     * Gets all tracked operation names.
     */
    @NotNull
    public java.util.Set<String> getTrackedOperations() {
        return operationCounts.keySet();
    }
    
    /**
     * Gets current heap memory usage in bytes.
     */
    public long getHeapMemoryUsed() {
        return lastHeapUsed;
    }
    
    /**
     * Gets current non-heap memory usage in bytes.
     */
    public long getNonHeapMemoryUsed() {
        return lastNonHeapUsed;
    }
    
    /**
     * Gets peak heap memory usage in bytes.
     */
    public long getPeakHeapMemoryUsed() {
        return peakHeapUsed;
    }
    
    /**
     * Gets peak non-heap memory usage in bytes.
     */
    public long getPeakNonHeapMemoryUsed() {
        return peakNonHeapUsed;
    }
    
    /**
     * Gets current heap memory usage as a percentage.
     */
    public double getHeapMemoryPercent() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        return (double) heapUsage.getUsed() / heapUsage.getMax() * 100.0;
    }
    
    /**
     * Gets maximum heap memory in bytes.
     */
    public long getMaxHeapMemory() {
        return memoryBean.getHeapMemoryUsage().getMax();
    }
    
    /**
     * Forces garbage collection and updates metrics.
     */
    public void forceGarbageCollection() {
        logger.info("Forcing garbage collection...");
        long beforeHeap = getHeapMemoryUsed();
        
        System.gc();
        
        // Wait a moment for GC to complete
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        updateMemoryMetrics();
        long afterHeap = getHeapMemoryUsed();
        long freed = beforeHeap - afterHeap;
        
        logger.info(String.format("Garbage collection completed. Freed %d MB of memory", 
            freed / 1024 / 1024));
    }
    
    /**
     * Clears all performance metrics.
     */
    public void clearMetrics() {
        totalTickTime.set(0);
        tickCount.set(0);
        operationTimes.clear();
        operationCounts.clear();
        peakHeapUsed = lastHeapUsed;
        peakNonHeapUsed = lastNonHeapUsed;
        
        logger.info("Performance metrics cleared");
    }
    
    /**
     * Generates a performance report.
     */
    @NotNull
    public String generateReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== UltimateDungeonX Performance Report ===\n");
        
        // Tick performance
        report.append(String.format("Average Tick Time: %.2fms (Target: 50ms)\n", getAverageTickTime()));
        report.append(String.format("Total Ticks Recorded: %d\n", getTotalTicks()));
        report.append("\n");
        
        // Memory usage
        report.append(String.format("Heap Memory: %d MB / %d MB (%.1f%%)\n",
            getHeapMemoryUsed() / 1024 / 1024,
            getMaxHeapMemory() / 1024 / 1024,
            getHeapMemoryPercent()));
        report.append(String.format("Peak Heap Usage: %d MB\n", getPeakHeapMemoryUsed() / 1024 / 1024));
        report.append(String.format("Non-Heap Memory: %d MB\n", getNonHeapMemoryUsed() / 1024 / 1024));
        report.append("\n");
        
        // Operation performance
        report.append("Operation Performance:\n");
        for (String operation : getTrackedOperations()) {
            report.append(String.format("  %s: %d executions, %.2fms average, %.2fms total\n",
                operation, getOperationCount(operation),
                getAverageOperationTime(operation), getTotalOperationTime(operation)));
        }
        
        return report.toString();
    }
    
    /**
     * Shuts down the performance monitor.
     */
    public void shutdown() {
        stopMonitoring();
        
        // Log final performance report
        logger.info("Final performance report:\n" + generateReport());
        
        clearMetrics();
        logger.info("Performance monitor shut down");
    }
}
