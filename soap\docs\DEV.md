# UltimateDungeonX Developer Guide

This document provides comprehensive information for developers working on UltimateDungeonX, including building, testing, contributing, and extending the plugin.

## Development Environment Setup

### Prerequisites

- **Java 21** or higher (OpenJDK recommended)
- **Git** for version control
- **IDE** with Java support (IntelliJ IDEA recommended)
- **Paper 1.21.x** test server
- **Gradle** (wrapper included)

### Initial Setup

1. **Clone Repository**:
   ```bash
   git clone https://github.com/ultimatedungeon/UltimateDungeonX.git
   cd UltimateDungeonX
   ```

2. **Import to IDE**:
   - Open project in IntelliJ IDEA
   - Import as Gradle project
   - Wait for dependency resolution

3. **Configure Test Server**:
   ```bash
   # Create test server directory
   mkdir test-server
   cd test-server
   
   # Download Paper 1.21.x
   wget https://api.papermc.io/v2/projects/paper/versions/1.21.3/builds/latest/downloads/paper-1.21.3-latest.jar
   
   # Create start script
   echo "java -Xmx2G -Xms1G -jar paper-1.21.3-latest.jar --nogui" > start.sh
   chmod +x start.sh
   ```

4. **First Build**:
   ```bash
   ./gradlew build
   ```

## Project Structure

### Source Organization

```
src/
├── main/
│   ├── java/com/ultimatedungeon/udx/
│   │   ├── bootstrap/           # Plugin main class and lifecycle
│   │   ├── command/            # Command handling
│   │   ├── config/             # Configuration management
│   │   ├── data/               # Database and persistence
│   │   ├── dungeon/            # Dungeon definitions and management
│   │   ├── instance/           # Instance world management
│   │   ├── gen/                # Procedural generation
│   │   ├── room/               # Room templates and UDX format
│   │   ├── paste/              # Block placement and FastPaster
│   │   ├── spawner/            # Mob spawning system
│   │   ├── boss/               # Boss framework
│   │   ├── combat/             # Combat and abilities
│   │   ├── party/              # Party and matchmaking
│   │   ├── gui/                # Menu system
│   │   ├── loot/               # Loot generation
│   │   ├── progression/        # Player progression
│   │   ├── affix/              # Affix system
│   │   ├── api/                # Public API
│   │   └── util/               # Utilities and helpers
│   └── resources/
│       ├── plugin.yml          # Plugin metadata
│       ├── config.yml          # Default configuration
│       └── lang/               # Language files
├── test/
│   ├── java/                   # Unit tests
│   └── resources/              # Test resources
└── integration-test/
    ├── java/                   # Integration tests
    └── resources/              # Test server configs
```

### Key Architectural Patterns

#### Service Pattern
Core functionality is organized into services with clear interfaces:

```java
public interface DungeonService {
    CompletableFuture<DungeonInstance> createInstance(String dungeonId, PartyData party);
    // Other methods...
}

@Component
public class DungeonServiceImpl implements DungeonService {
    // Implementation
}
```

#### Event-Driven Architecture
Heavy use of events for loose coupling:

```java
public class DungeonCreateEvent extends Event implements Cancellable {
    // Event data and methods
}

// Services fire events
eventBus.fire(new DungeonCreateEvent(dungeon, party));

// Other components listen
@EventHandler
public void onDungeonCreate(DungeonCreateEvent event) {
    // Handle event
}
```

#### Immutable Data Models
All data models are immutable records:

```java
public record DungeonDefinition(
    String id,
    String name,
    // Other fields...
) {
    // Validation and utility methods
}
```

#### Async-First Design
All I/O operations are asynchronous:

```java
public CompletableFuture<PlayerProfile> loadPlayerProfile(UUID playerId) {
    return CompletableFuture.supplyAsync(() -> {
        // Database operation
    }, asyncExecutor);
}
```

## Building and Testing

### Gradle Tasks

```bash
# Clean build
./gradlew clean build

# Run tests only
./gradlew test

# Run integration tests
./gradlew integrationTest

# Generate documentation
./gradlew javadoc

# Create release jar
./gradlew shadowJar

# Run test server with plugin
./gradlew runServer
```

### Build Configuration

The build is configured in `build.gradle.kts`:

```kotlin
dependencies {
    // Paper API
    compileOnly("io.papermc.paper:paper-api:1.21.3-R0.1-SNAPSHOT")
    
    // Embedded dependencies
    implementation("org.xerial:sqlite-jdbc:3.44.1.0")
    implementation("com.google.code.gson:gson:2.10.1")
    
    // Test dependencies
    testImplementation("org.junit.jupiter:junit-jupiter:5.10.1")
    testImplementation("org.mockito:mockito-core:5.8.0")
}

tasks.shadowJar {
    // Relocate dependencies to avoid conflicts
    relocate("org.xerial", "com.ultimatedungeon.udx.libs.xerial")
    relocate("com.google.gson", "com.ultimatedungeon.udx.libs.gson")
    
    // Minimize jar size
    minimize()
}
```

### Testing Strategy

#### Unit Tests
Test individual components in isolation:

```java
@ExtendWith(MockitoExtension.class)
class DungeonServiceTest {
    
    @Mock
    private ConfigService configService;
    
    @Mock
    private DatabaseService databaseService;
    
    @InjectMocks
    private DungeonServiceImpl dungeonService;
    
    @Test
    void shouldCreateDungeonInstance() {
        // Given
        String dungeonId = "test_dungeon";
        PartyData party = createTestParty();
        
        // When
        CompletableFuture<DungeonInstance> result = dungeonService.createInstance(dungeonId, party);
        
        // Then
        assertThat(result).succeedsWithin(Duration.ofSeconds(5));
        DungeonInstance instance = result.join();
        assertThat(instance.dungeonId()).isEqualTo(dungeonId);
    }
}
```

#### Integration Tests
Test complete workflows with mock server:

```java
@ExtendWith(MockServerExtension.class)
class DungeonIntegrationTest {
    
    @Test
    void shouldCompleteDungeonRun(MockServer server) {
        // Given
        Player player = server.createMockPlayer("TestPlayer");
        DungeonDefinition dungeon = createTestDungeon();
        
        // When
        CompletableFuture<Void> completion = runDungeon(player, dungeon);
        
        // Then
        assertThat(completion).succeedsWithin(Duration.ofMinutes(1));
        
        // Verify player progression
        PlayerProfile profile = getPlayerProfile(player.getUniqueId()).join();
        assertThat(profile.completedRuns()).isEqualTo(1);
    }
}
```

#### Performance Tests
Validate performance requirements:

```java
@Test
void shouldMaintainPerformanceUnderLoad() {
    // Create multiple concurrent instances
    List<CompletableFuture<DungeonInstance>> futures = IntStream.range(0, 10)
        .mapToObj(i -> dungeonService.createInstance("test_dungeon", createTestParty()))
        .collect(toList());
    
    // Measure completion time
    long startTime = System.currentTimeMillis();
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    long duration = System.currentTimeMillis() - startTime;
    
    // Assert performance requirement
    assertThat(duration).isLessThan(5000); // 5 seconds max
}
```

## Code Style and Standards

### Java Conventions

- **Naming**: Use descriptive names, avoid abbreviations
- **Methods**: Verb phrases (e.g., `createInstance`, `loadPlayerData`)
- **Classes**: Noun phrases (e.g., `DungeonService`, `PlayerProfile`)
- **Constants**: UPPER_SNAKE_CASE
- **Packages**: lowercase, no underscores

### Documentation Standards

#### Javadoc Requirements
All public APIs must have comprehensive Javadoc:

```java
/**
 * Creates a new dungeon instance for the specified party.
 * 
 * <p>This method asynchronously creates a new isolated world for the dungeon,
 * generates the room layout, and prepares all necessary game objects.
 * 
 * @param dungeonId the unique identifier of the dungeon to instantiate
 * @param party the party that will enter the dungeon
 * @param difficulty the difficulty tier to apply
 * @return a CompletableFuture that completes with the created instance
 * @throws IllegalArgumentException if dungeonId is invalid or party is null
 * @throws DungeonCreationException if instance creation fails
 * @since 1.0.0
 */
public CompletableFuture<DungeonInstance> createInstance(
    @NotNull String dungeonId, 
    @NotNull PartyData party,
    @NotNull DifficultyTier difficulty
) {
    // Implementation
}
```

#### Code Comments
Use comments sparingly, prefer self-documenting code:

```java
// Good: Explains WHY, not WHAT
// Use exponential backoff to avoid overwhelming the database during high load
int retryDelay = Math.min(1000 * (1 << attempt), MAX_RETRY_DELAY);

// Bad: Explains WHAT (obvious from code)
// Increment the counter by 1
counter++;
```

### Error Handling

#### Exception Strategy
- Use checked exceptions for recoverable errors
- Use unchecked exceptions for programming errors
- Provide meaningful error messages with context

```java
public class DungeonCreationException extends Exception {
    public DungeonCreationException(String dungeonId, String reason, Throwable cause) {
        super(String.format("Failed to create dungeon '%s': %s", dungeonId, reason), cause);
    }
}
```

#### Logging Guidelines
Use structured logging with appropriate levels:

```java
private static final Logger logger = LoggerFactory.getLogger(DungeonService.class);

public CompletableFuture<DungeonInstance> createInstance(String dungeonId, PartyData party) {
    logger.info("Creating dungeon instance: dungeonId={}, partySize={}", 
        dungeonId, party.memberIds().size());
    
    return CompletableFuture.supplyAsync(() -> {
        try {
            // Creation logic
            logger.debug("Instance created successfully: instanceId={}", instance.instanceId());
            return instance;
        } catch (Exception e) {
            logger.error("Failed to create dungeon instance: dungeonId={}", dungeonId, e);
            throw new RuntimeException(e);
        }
    });
}
```

## Performance Guidelines

### Memory Management

#### Object Pooling
Use object pools for frequently created objects:

```java
@Component
public class ParticleEffectPool {
    private final Queue<ParticleEffect> pool = new ConcurrentLinkedQueue<>();
    
    public ParticleEffect acquire() {
        ParticleEffect effect = pool.poll();
        return effect != null ? effect : new ParticleEffect();
    }
    
    public void release(ParticleEffect effect) {
        effect.reset();
        pool.offer(effect);
    }
}
```

#### Weak References
Use weak references for caches to allow garbage collection:

```java
private final Map<UUID, WeakReference<PlayerProfile>> profileCache = new ConcurrentHashMap<>();

public PlayerProfile getCachedProfile(UUID playerId) {
    WeakReference<PlayerProfile> ref = profileCache.get(playerId);
    return ref != null ? ref.get() : null;
}
```

### Async Operations

#### Thread Pool Management
Use appropriate thread pools for different operation types:

```java
@Configuration
public class AsyncConfig {
    
    @Bean("dungeonExecutor")
    public Executor dungeonExecutor() {
        return new ThreadPoolTaskExecutor() {{
            setCorePoolSize(2);
            setMaxPoolSize(8);
            setQueueCapacity(100);
            setThreadNamePrefix("dungeon-");
            initialize();
        }};
    }
    
    @Bean("databaseExecutor")
    public Executor databaseExecutor() {
        return new ThreadPoolTaskExecutor() {{
            setCorePoolSize(1);
            setMaxPoolSize(4);
            setQueueCapacity(50);
            setThreadNamePrefix("database-");
            initialize();
        }};
    }
}
```

#### Tick Budget Management
Respect server tick budgets for block operations:

```java
public class FastPaster {
    private static final int DEFAULT_BLOCKS_PER_TICK = 4096;
    private final TickBudgeter tickBudgeter;
    
    public CompletableFuture<Void> pasteAsync(RoomTemplate template, Location location) {
        return CompletableFuture.runAsync(() -> {
            BlockQueue queue = createBlockQueue(template, location);
            
            BukkitRunnable task = new BukkitRunnable() {
                @Override
                public void run() {
                    long startTime = System.nanoTime();
                    int blocksPlaced = 0;
                    
                    while (!queue.isEmpty() && blocksPlaced < DEFAULT_BLOCKS_PER_TICK) {
                        if (tickBudgeter.hasTimeRemaining()) {
                            queue.placeNext();
                            blocksPlaced++;
                        } else {
                            break; // Yield to next tick
                        }
                    }
                    
                    if (queue.isEmpty()) {
                        cancel(); // Pasting complete
                    }
                }
            };
            
            task.runTaskTimer(plugin, 0L, 1L);
        });
    }
}
```

## Database Schema and Migrations

### Schema Design

The plugin uses SQLite by default with the following schema:

```sql
-- Player profiles
CREATE TABLE player_profiles (
    player_id TEXT PRIMARY KEY,
    player_name TEXT NOT NULL,
    first_join INTEGER NOT NULL,
    last_seen INTEGER NOT NULL,
    total_runs INTEGER DEFAULT 0,
    completed_runs INTEGER DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Dungeon progress
CREATE TABLE dungeon_progress (
    player_id TEXT NOT NULL,
    dungeon_id TEXT NOT NULL,
    best_time INTEGER,
    best_difficulty TEXT,
    completion_count INTEGER DEFAULT 0,
    first_completion INTEGER,
    last_attempt INTEGER,
    PRIMARY KEY (player_id, dungeon_id),
    FOREIGN KEY (player_id) REFERENCES player_profiles(player_id)
);

-- Achievement progress
CREATE TABLE achievement_progress (
    player_id TEXT NOT NULL,
    achievement_id TEXT NOT NULL,
    progress INTEGER DEFAULT 0,
    completed INTEGER DEFAULT 0,
    completion_date INTEGER,
    PRIMARY KEY (player_id, achievement_id),
    FOREIGN KEY (player_id) REFERENCES player_profiles(player_id)
);

-- Season statistics
CREATE TABLE season_stats (
    player_id TEXT NOT NULL,
    season_id TEXT NOT NULL,
    score INTEGER DEFAULT 0,
    runs_completed INTEGER DEFAULT 0,
    best_time INTEGER,
    achievements_earned INTEGER DEFAULT 0,
    PRIMARY KEY (player_id, season_id),
    FOREIGN KEY (player_id) REFERENCES player_profiles(player_id)
);
```

### Migration System

Migrations are handled automatically using version numbers:

```java
@Component
public class DatabaseMigrator {
    
    public void migrate(Connection connection) throws SQLException {
        int currentVersion = getCurrentVersion(connection);
        int targetVersion = getTargetVersion();
        
        for (int version = currentVersion + 1; version <= targetVersion; version++) {
            applyMigration(connection, version);
        }
    }
    
    private void applyMigration(Connection connection, int version) throws SQLException {
        String migrationSql = loadMigrationSql(version);
        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(migrationSql);
        }
        updateVersion(connection, version);
    }
}
```

Migration files are stored in `src/main/resources/migrations/`:

```sql
-- V002__add_season_stats.sql
CREATE TABLE season_stats (
    player_id TEXT NOT NULL,
    season_id TEXT NOT NULL,
    score INTEGER DEFAULT 0,
    -- Additional columns...
    PRIMARY KEY (player_id, season_id)
);

CREATE INDEX idx_season_stats_score ON season_stats(season_id, score DESC);
```

## Configuration Management

### Configuration Schema

Configuration uses a hierarchical structure with validation:

```java
@ConfigurationProperties("udx")
public class UdxConfig {
    
    @NotBlank
    private String buildWorld = "udx_build";
    
    @Min(1) @Max(50)
    private int maxConcurrentInstances = 10;
    
    @Valid
    private PerformanceConfig performance = new PerformanceConfig();
    
    @Valid
    private DatabaseConfig database = new DatabaseConfig();
    
    // Getters and setters...
}

public class PerformanceConfig {
    @Min(1000) @Max(50000)
    private int tickBudgetPerFrame = 5000;
    
    @Min(10) @Max(1000)
    private int maxEntitiesPerInstance = 100;
    
    // Getters and setters...
}
```

### Configuration Loading

Configuration is loaded with validation and migration:

```java
@Component
public class ConfigService {
    
    public UdxConfig loadConfig() {
        File configFile = new File(plugin.getDataFolder(), "config.yml");
        
        if (!configFile.exists()) {
            saveDefaultConfig();
        }
        
        YamlConfiguration yaml = YamlConfiguration.loadConfiguration(configFile);
        
        // Check version and migrate if needed
        int configVersion = yaml.getInt("config-version", 0);
        if (configVersion < CURRENT_CONFIG_VERSION) {
            migrateConfig(yaml, configVersion);
        }
        
        // Validate and return
        return validateConfig(parseConfig(yaml));
    }
    
    private UdxConfig validateConfig(UdxConfig config) {
        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        Set<ConstraintViolation<UdxConfig>> violations = validator.validate(config);
        
        if (!violations.isEmpty()) {
            throw new ConfigurationException("Invalid configuration: " + violations);
        }
        
        return config;
    }
}
```

## Debugging and Profiling

### Debug Tools

The plugin includes comprehensive debug tools accessible via `/udx admin`:

```java
@Component
public class DebugService {
    
    public PerformanceReport generatePerformanceReport() {
        return PerformanceReport.builder()
            .tickTime(getAverageTickTime())
            .memoryUsage(getMemoryUsage())
            .activeInstances(getActiveInstanceCount())
            .entityCounts(getEntityCounts())
            .databaseStats(getDatabaseStats())
            .build();
    }
    
    public void dumpThreadStacks() {
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        ThreadInfo[] threadInfos = threadBean.dumpAllThreads(true, true);
        
        for (ThreadInfo threadInfo : threadInfos) {
            logger.debug("Thread: {} ({})", threadInfo.getThreadName(), threadInfo.getThreadState());
            for (StackTraceElement element : threadInfo.getStackTrace()) {
                logger.debug("  at {}", element);
            }
        }
    }
}
```

### Profiling Integration

Integration with profiling tools for performance analysis:

```java
@Component
@ConditionalOnProperty("udx.profiling.enabled")
public class ProfilingService {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void onDungeonCreate(DungeonCreateEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        event.getInstance().whenComplete((instance, throwable) -> {
            sample.stop(Timer.builder("dungeon.creation.time")
                .tag("dungeon", event.getDungeonId())
                .register(meterRegistry));
        });
    }
    
    @Scheduled(fixedRate = 30000) // Every 30 seconds
    public void recordMetrics() {
        Gauge.builder("dungeon.instances.active")
            .register(meterRegistry, this, ProfilingService::getActiveInstanceCount);
            
        Gauge.builder("dungeon.memory.usage")
            .register(meterRegistry, this, ProfilingService::getMemoryUsage);
    }
}
```

## Release Process

### Version Management

Versions follow semantic versioning (MAJOR.MINOR.PATCH):

- **MAJOR**: Breaking API changes
- **MINOR**: New features, backward compatible
- **PATCH**: Bug fixes, backward compatible

### Release Checklist

1. **Code Quality**:
   - [ ] All tests pass
   - [ ] Code coverage > 80%
   - [ ] No critical security vulnerabilities
   - [ ] Performance benchmarks met

2. **Documentation**:
   - [ ] API documentation updated
   - [ ] User guide updated
   - [ ] Configuration examples updated
   - [ ] Migration guide (if needed)

3. **Testing**:
   - [ ] Integration tests pass
   - [ ] Performance tests pass
   - [ ] Manual testing on test server
   - [ ] Compatibility testing with common plugins

4. **Build**:
   - [ ] Clean build successful
   - [ ] Jar size within limits
   - [ ] Dependencies properly relocated
   - [ ] Plugin.yml version updated

### Automated Release

GitHub Actions workflow for automated releases:

```yaml
name: Release
on:
  push:
    tags: ['v*']

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          java-version: '21'
          distribution: 'temurin'
          
      - name: Build with Gradle
        run: ./gradlew build shadowJar
        
      - name: Run tests
        run: ./gradlew test integrationTest
        
      - name: Create Release
        uses: actions/create-release@v1
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
          
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./releases/UltimateDungeonX-${{ github.ref_name }}.jar
          asset_name: UltimateDungeonX-${{ github.ref_name }}.jar
          asset_content_type: application/java-archive
```

## Contributing Guidelines

### Pull Request Process

1. **Fork and Branch**:
   ```bash
   git checkout -b feature/my-new-feature
   ```

2. **Development**:
   - Write tests for new functionality
   - Follow code style guidelines
   - Update documentation as needed

3. **Testing**:
   ```bash
   ./gradlew test integrationTest
   ```

4. **Commit**:
   ```bash
   git commit -m "feat: add new dungeon generation algorithm"
   ```

5. **Pull Request**:
   - Clear description of changes
   - Link to related issues
   - Include test results

### Code Review Criteria

- **Functionality**: Does it work as intended?
- **Performance**: Does it meet performance requirements?
- **Security**: Are there any security vulnerabilities?
- **Maintainability**: Is the code readable and well-structured?
- **Testing**: Are there adequate tests?
- **Documentation**: Is it properly documented?

### Issue Reporting

When reporting issues, include:

- **Environment**: Server version, Java version, plugin version
- **Steps to Reproduce**: Clear, numbered steps
- **Expected Behavior**: What should happen
- **Actual Behavior**: What actually happens
- **Logs**: Relevant error messages and stack traces
- **Configuration**: Relevant configuration snippets

## Troubleshooting Common Issues

### Build Issues

**Problem**: `Could not resolve dependencies`
```bash
# Solution: Clear Gradle cache
./gradlew clean --refresh-dependencies
```

**Problem**: `OutOfMemoryError during build`
```bash
# Solution: Increase Gradle memory
export GRADLE_OPTS="-Xmx4g -XX:MaxMetaspaceSize=512m"
./gradlew build
```

### Runtime Issues

**Problem**: `ClassNotFoundException` for relocated dependencies
```kotlin
// Solution: Check shadowJar configuration
shadowJar {
    relocate("org.xerial", "com.ultimatedungeon.udx.libs.xerial")
    // Ensure all dependencies are relocated
}
```

**Problem**: Database connection issues
```java
// Solution: Add connection validation
@Bean
public DataSource dataSource() {
    HikariConfig config = new HikariConfig();
    config.setConnectionTestQuery("SELECT 1");
    config.setValidationTimeout(3000);
    return new HikariDataSource(config);
}
```

### Performance Issues

**Problem**: Tick lag during world generation
```java
// Solution: Implement proper tick budgeting
public void pasteBlocks() {
    long startTime = System.nanoTime();
    int blocksPlaced = 0;
    
    while (hasMoreBlocks() && blocksPlaced < maxBlocksPerTick) {
        long elapsed = System.nanoTime() - startTime;
        if (elapsed > maxNanosPerTick) {
            break; // Yield to next tick
        }
        
        placeNextBlock();
        blocksPlaced++;
    }
}
```

---

*This developer guide provides the foundation for contributing to and extending UltimateDungeonX. For additional help, consult the API documentation and community resources.*
