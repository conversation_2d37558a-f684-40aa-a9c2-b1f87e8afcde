package com.ultimatedungeon.udx.api.loot;

import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import org.jetbrains.annotations.NotNull;

/**
 * Enumeration of item rarities with associated colors and properties.
 */
public enum ItemRarity {
    
    COMMON("Common", NamedTextColor.WHITE, 1.0, false),
    UNCOMMON("Uncommon", NamedTextColor.GREEN, 1.2, false),
    RARE("Rare", NamedTextColor.BLUE, 1.5, false),
    EPIC("Epic", NamedTextColor.DARK_PURPLE, 2.0, true),
    LEGENDARY("Legendary", NamedTextColor.GOLD, 3.0, true),
    MYTHIC("Mythic", NamedTextColor.RED, 5.0, true);
    
    private final String displayName;
    private final TextColor color;
    private final double valueMultiplier;
    private final boolean hasGlow;
    
    ItemRarity(@NotNull String displayName, @NotNull TextColor color, double valueMultiplier, boolean hasGlow) {
        this.displayName = displayName;
        this.color = color;
        this.valueMultiplier = valueMultiplier;
        this.hasGlow = hasGlow;
    }
    
    /**
     * Gets the display name of the rarity.
     * 
     * @return The display name
     */
    @NotNull
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the color associated with this rarity.
     * 
     * @return The text color
     */
    @NotNull
    public TextColor getColor() {
        return color;
    }
    
    /**
     * Gets the value multiplier for this rarity.
     * 
     * @return The value multiplier
     */
    public double getValueMultiplier() {
        return valueMultiplier;
    }
    
    /**
     * Checks if items of this rarity should have a glow effect.
     * 
     * @return True if items should glow
     */
    public boolean hasGlow() {
        return hasGlow;
    }
    
    /**
     * Gets the drop chance modifier for this rarity.
     * Higher rarity = lower chance.
     * 
     * @return The drop chance modifier (0.0 to 1.0)
     */
    public double getDropChanceModifier() {
        return switch (this) {
            case COMMON -> 1.0;
            case UNCOMMON -> 0.6;
            case RARE -> 0.3;
            case EPIC -> 0.15;
            case LEGENDARY -> 0.05;
            case MYTHIC -> 0.01;
        };
    }
    
    /**
     * Gets a rarity by its display name.
     * 
     * @param name The display name
     * @return The rarity, or COMMON if not found
     */
    @NotNull
    public static ItemRarity fromDisplayName(@NotNull String name) {
        for (ItemRarity rarity : values()) {
            if (rarity.displayName.equalsIgnoreCase(name)) {
                return rarity;
            }
        }
        return COMMON;
    }
}
