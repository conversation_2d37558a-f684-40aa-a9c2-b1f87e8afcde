package com.ultimatedungeon.udx.api.loot;

import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * Immutable representation of loot entry data for API consumers.
 */
public record LootEntryData(
    @NotNull String id,
    @NotNull LootEntryType type,
    @Nullable ItemStack item,
    @Nullable String command,
    double weight,
    int minQuantity,
    int maxQuantity,
    double currencyAmount,
    @NotNull ItemRarity rarity
) {
    
    public enum LootEntryType {
        ITEM,
        COMMAND,
        CURRENCY
    }
    
    public LootEntryData {
        if (id == null || id.isBlank()) throw new IllegalArgumentException("ID cannot be null or blank");
        if (type == null) throw new IllegalArgumentException("Type cannot be null");
        if (weight < 0) throw new IllegalArgumentException("Weight cannot be negative");
        if (minQuantity < 0) throw new IllegalArgumentException("Min quantity cannot be negative");
        if (maxQuantity < minQuantity) throw new IllegalArgumentException("Max quantity must be >= min quantity");
        if (currencyAmount < 0) throw new IllegalArgumentException("Currency amount cannot be negative");
        if (rarity == null) throw new IllegalArgumentException("Rarity cannot be null");
        
        // Validate type-specific requirements
        if (type == LootEntryType.ITEM && item == null) {
            throw new IllegalArgumentException("Item cannot be null for ITEM type");
        }
        if (type == LootEntryType.COMMAND && (command == null || command.isBlank())) {
            throw new IllegalArgumentException("Command cannot be null or blank for COMMAND type");
        }
    }
}
