package com.ultimatedungeon.udx.party;

import com.ultimatedungeon.udx.data.DataService;
import com.ultimatedungeon.udx.instance.InstanceManager;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * Service for managing parties and matchmaking.
 * 
 * <p>This service handles party creation, invitations, ready checks,
 * and matchmaking for dungeon runs.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class PartyService implements Listener {
    
    private static final long INVITATION_TIMEOUT_MS = TimeUnit.MINUTES.toMillis(2);
    private static final long READY_CHECK_TIMEOUT_MS = TimeUnit.SECONDS.toMillis(30);
    private static final long REJOIN_GRACE_PERIOD_MS = TimeUnit.MINUTES.toMillis(5);
    private static final int MAX_PARTY_SIZE = 5;
    
    private final Plugin plugin;
    private final DataService dataService;
    private final SchedulerUtil schedulerUtil;
    private final InstanceManager instanceManager;
    
    // Party management
    private final Map<UUID, Party> parties = new ConcurrentHashMap<>();
    private final Map<UUID, UUID> playerToParty = new ConcurrentHashMap<>();
    
    // Invitations
    private final Map<UUID, PartyInvitation> invitations = new ConcurrentHashMap<>();
    private final Map<UUID, Set<UUID>> playerInvitations = new ConcurrentHashMap<>();
    
    // Matchmaking
    private final Map<UUID, MatchmakingQueue> matchmakingQueues = new ConcurrentHashMap<>();
    private final Map<UUID, UUID> partyToQueue = new ConcurrentHashMap<>();
    
    // Rejoin system
    private final Map<UUID, RejoinData> rejoinData = new ConcurrentHashMap<>();
    
    // Background tasks
    private BukkitTask cleanupTask;
    private BukkitTask matchmakingTask;
    
    public PartyService(@NotNull Plugin plugin, @NotNull DataService dataService, 
                       @NotNull SchedulerUtil schedulerUtil, @NotNull InstanceManager instanceManager) {
        this.plugin = plugin;
        this.dataService = dataService;
        this.schedulerUtil = schedulerUtil;
        this.instanceManager = instanceManager;
        
        startBackgroundTasks();
    }
    
    // ===== PARTY MANAGEMENT =====
    
    /**
     * Creates a new party with the given player as leader.
     * 
     * @param leader the party leader
     * @return the created party, or null if player is already in a party
     */
    @Nullable
    public Party createParty(@NotNull Player leader) {
        UUID leaderId = leader.getUniqueId();
        
        if (playerToParty.containsKey(leaderId)) {
            return null; // Already in a party
        }
        
        Party party = new Party(leader);
        parties.put(party.getPartyId(), party);
        playerToParty.put(leaderId, party.getPartyId());
        
        leader.sendMessage(Component.text("Party created! You are now the party leader.")
            .color(NamedTextColor.GREEN));
        
        plugin.getLogger().info("Party created: " + party.getPartyId() + " by " + leader.getName());
        return party;
    }
    
    /**
     * Disbands a party.
     * 
     * @param partyId the party ID
     * @return true if party was disbanded
     */
    public boolean disbandParty(@NotNull UUID partyId) {
        Party party = parties.remove(partyId);
        if (party == null) {
            return false;
        }
        
        // Remove all members from tracking
        for (UUID memberId : party.getMembers()) {
            playerToParty.remove(memberId);
            
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(Component.text("Party has been disbanded.")
                    .color(NamedTextColor.YELLOW));
            }
        }
        
        // Cancel any active queue
        UUID queueId = partyToQueue.remove(partyId);
        if (queueId != null) {
            matchmakingQueues.remove(queueId);
        }
        
        plugin.getLogger().info("Party disbanded: " + partyId);
        return true;
    }
    
    /**
     * Gets a party by ID.
     * 
     * @param partyId the party ID
     * @return the party, or null if not found
     */
    @Nullable
    public Party getParty(@NotNull UUID partyId) {
        return parties.get(partyId);
    }
    
    /**
     * Gets the party a player is in.
     * 
     * @param playerId the player ID
     * @return the party, or null if not in a party
     */
    @Nullable
    public Party getPlayerParty(@NotNull UUID playerId) {
        UUID partyId = playerToParty.get(playerId);
        return partyId != null ? parties.get(partyId) : null;
    }
    
    /**
     * Gets all active parties.
     * 
     * @return collection of all parties
     */
    @NotNull
    public Collection<Party> getAllParties() {
        return new ArrayList<>(parties.values());
    }
    
    /**
     * Leaves a party.
     * 
     * @param playerId the player ID
     * @return true if player left a party
     */
    public boolean leaveParty(@NotNull UUID playerId) {
        UUID partyId = playerToParty.remove(playerId);
        if (partyId == null) {
            return false;
        }
        
        Party party = parties.get(partyId);
        if (party == null) {
            return false;
        }
        
        party.removeMember(playerId);
        
        Player player = Bukkit.getPlayer(playerId);
        if (player != null) {
            player.sendMessage(Component.text("You left the party.")
                .color(NamedTextColor.YELLOW));
        }
        
        // Notify remaining members
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(Component.text(player != null ? player.getName() : "Player")
                    .append(Component.text(" left the party."))
                    .color(NamedTextColor.YELLOW));
            }
        }
        
        // Disband if empty
        if (party.isEmpty()) {
            disbandParty(partyId);
        }
        
        return true;
    }
    
    /**
     * Kicks a player from a party.
     * 
     * @param kickerId the kicker's ID (must be party leader)
     * @param targetId the target player ID
     * @return true if player was kicked
     */
    public boolean kickPlayer(@NotNull UUID kickerId, @NotNull UUID targetId) {
        Party party = getPlayerParty(kickerId);
        if (party == null || !party.getLeader().equals(kickerId)) {
            return false;
        }
        
        if (!party.getMembers().contains(targetId)) {
            return false;
        }
        
        // Remove from party
        party.removeMember(targetId);
        playerToParty.remove(targetId);
        
        // Notify players
        Player kicker = Bukkit.getPlayer(kickerId);
        Player target = Bukkit.getPlayer(targetId);
        
        String kickerName = kicker != null ? kicker.getName() : "Party Leader";
        String targetName = target != null ? target.getName() : "Player";
        
        if (target != null) {
            target.sendMessage(Component.text("You were kicked from the party by " + kickerName + ".")
                .color(NamedTextColor.RED));
        }
        
        // Notify remaining members
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(Component.text(targetName + " was kicked from the party.")
                    .color(NamedTextColor.YELLOW));
            }
        }
        
        return true;
    }
    
    /**
     * Promotes a player to party leader.
     * 
     * @param currentLeaderId the current leader's ID
     * @param newLeaderId the new leader's ID
     * @return true if promotion was successful
     */
    public boolean promoteToLeader(@NotNull UUID currentLeaderId, @NotNull UUID newLeaderId) {
        Party party = getPlayerParty(currentLeaderId);
        if (party == null || !party.getLeader().equals(currentLeaderId)) {
            return false;
        }
        
        if (!party.getMembers().contains(newLeaderId)) {
            return false;
        }
        
        party.setLeader(newLeaderId);
        
        // Notify party members
        Player currentLeader = Bukkit.getPlayer(currentLeaderId);
        Player newLeader = Bukkit.getPlayer(newLeaderId);
        
        String currentName = currentLeader != null ? currentLeader.getName() : "Player";
        String newName = newLeader != null ? newLeader.getName() : "Player";
        
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                if (memberId.equals(newLeaderId)) {
                    member.sendMessage(Component.text("You are now the party leader!")
                        .color(NamedTextColor.GREEN));
                } else {
                    member.sendMessage(Component.text(newName + " is now the party leader.")
                        .color(NamedTextColor.YELLOW));
                }
            }
        }
        
        return true;
    }
    
    // ===== INVITATION SYSTEM =====
    
    /**
     * Invites a player to a party.
     * 
     * @param inviter the inviting player
     * @param invitee the player to invite
     * @return true if invitation was sent
     */
    public boolean invitePlayer(@NotNull Player inviter, @NotNull Player invitee) {
        UUID inviterId = inviter.getUniqueId();
        UUID inviteeId = invitee.getUniqueId();
        
        // Check if inviter is in a party and is the leader
        Party party = getPlayerParty(inviterId);
        if (party == null) {
            inviter.sendMessage(Component.text("You must be in a party to invite players.")
                .color(NamedTextColor.RED));
            return false;
        }
        
        if (!party.getLeader().equals(inviterId)) {
            inviter.sendMessage(Component.text("Only the party leader can invite players.")
                .color(NamedTextColor.RED));
            return false;
        }
        
        // Check if invitee is already in a party
        if (playerToParty.containsKey(inviteeId)) {
            inviter.sendMessage(Component.text(invitee.getName() + " is already in a party.")
                .color(NamedTextColor.RED));
            return false;
        }
        
        // Check party size limit
        if (party.getSize() >= MAX_PARTY_SIZE) {
            inviter.sendMessage(Component.text("Party is full (max " + MAX_PARTY_SIZE + " players).")
                .color(NamedTextColor.RED));
            return false;
        }
        
        // Check if already invited
        Set<UUID> playerInvites = playerInvitations.computeIfAbsent(inviteeId, k -> ConcurrentHashMap.newKeySet());
        if (playerInvites.contains(party.getPartyId())) {
            inviter.sendMessage(Component.text(invitee.getName() + " already has a pending invitation.")
                .color(NamedTextColor.RED));
            return false;
        }
        
        // Create invitation
        PartyInvitation invitation = PartyInvitation.create(
            party.getPartyId(), inviterId, inviteeId, INVITATION_TIMEOUT_MS);
        
        invitations.put(invitation.invitationId(), invitation);
        playerInvites.add(party.getPartyId());
        
        // Send messages
        inviter.sendMessage(Component.text("Invitation sent to " + invitee.getName() + ".")
            .color(NamedTextColor.GREEN));
        
        invitee.sendMessage(Component.text("Party invitation from " + inviter.getName() + "!")
            .color(NamedTextColor.GREEN)
            .append(Component.newline())
            .append(Component.text("Use /udx to accept or decline.")
                .color(NamedTextColor.GRAY)));
        
        return true;
    }
    
    /**
     * Accepts a party invitation.
     * 
     * @param playerId the player accepting
     * @param partyId the party ID
     * @return true if invitation was accepted
     */
    public boolean acceptInvitation(@NotNull UUID playerId, @NotNull UUID partyId) {
        // Find the invitation
        PartyInvitation invitation = invitations.values().stream()
            .filter(inv -> inv.invitee().equals(playerId) && inv.partyId().equals(partyId))
            .findFirst()
            .orElse(null);
        
        if (invitation == null || invitation.isExpired()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null) {
                player.sendMessage(Component.text("Invitation not found or expired.")
                    .color(NamedTextColor.RED));
            }
            return false;
        }
        
        Party party = parties.get(partyId);
        if (party == null) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null) {
                player.sendMessage(Component.text("Party no longer exists.")
                    .color(NamedTextColor.RED));
            }
            return false;
        }
        
        // Check if player is already in a party
        if (playerToParty.containsKey(playerId)) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null) {
                player.sendMessage(Component.text("You are already in a party.")
                    .color(NamedTextColor.RED));
            }
            return false;
        }
        
        // Add to party
        party.addMember(playerId);
        playerToParty.put(playerId, partyId);
        
        // Clean up invitation
        invitations.remove(invitation.invitationId());
        Set<UUID> playerInvites = playerInvitations.get(playerId);
        if (playerInvites != null) {
            playerInvites.remove(partyId);
        }
        
        // Notify all party members
        Player joiningPlayer = Bukkit.getPlayer(playerId);
        String joiningName = joiningPlayer != null ? joiningPlayer.getName() : "Player";
        
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                if (memberId.equals(playerId)) {
                    member.sendMessage(Component.text("You joined the party!")
                        .color(NamedTextColor.GREEN));
                } else {
                    member.sendMessage(Component.text(joiningName + " joined the party!")
                        .color(NamedTextColor.GREEN));
                }
            }
        }
        
        return true;
    }
    
    /**
     * Declines a party invitation.
     * 
     * @param playerId the player declining
     * @param partyId the party ID
     * @return true if invitation was declined
     */
    public boolean declineInvitation(@NotNull UUID playerId, @NotNull UUID partyId) {
        // Find and remove the invitation
        PartyInvitation invitation = invitations.values().stream()
            .filter(inv -> inv.invitee().equals(playerId) && inv.partyId().equals(partyId))
            .findFirst()
            .orElse(null);
        
        if (invitation == null) {
            return false;
        }
        
        invitations.remove(invitation.invitationId());
        Set<UUID> playerInvites = playerInvitations.get(playerId);
        if (playerInvites != null) {
            playerInvites.remove(partyId);
        }
        
        // Notify players
        Player decliningPlayer = Bukkit.getPlayer(playerId);
        if (decliningPlayer != null) {
            decliningPlayer.sendMessage(Component.text("Invitation declined.")
                .color(NamedTextColor.YELLOW));
        }
        
        Player inviter = Bukkit.getPlayer(invitation.inviter());
        if (inviter != null) {
            String decliningName = decliningPlayer != null ? decliningPlayer.getName() : "Player";
            inviter.sendMessage(Component.text(decliningName + " declined the party invitation.")
                .color(NamedTextColor.YELLOW));
        }
        
        return true;
    }
    
    /**
     * Gets pending invitations for a player.
     * 
     * @param playerId the player ID
     * @return list of pending invitations
     */
    @NotNull
    public List<PartyInvitation> getPendingInvitations(@NotNull UUID playerId) {
        return invitations.values().stream()
            .filter(inv -> inv.invitee().equals(playerId) && !inv.isExpired())
            .collect(Collectors.toList());
    }
    
    // ===== MATCHMAKING SYSTEM =====
    
    /**
     * Queues a party for matchmaking.
     * 
     * @param partyId the party ID
     * @param dungeonId the dungeon ID
     * @param difficulty the difficulty level
     * @return true if queued successfully
     */
    public boolean queueForMatchmaking(@NotNull UUID partyId, @NotNull String dungeonId, @NotNull String difficulty) {
        Party party = parties.get(partyId);
        if (party == null) {
            return false;
        }
        
        // Check if already in queue
        if (partyToQueue.containsKey(partyId)) {
            return false;
        }
        
        MatchmakingQueue queue = MatchmakingQueue.create(partyId, dungeonId, difficulty);
        matchmakingQueues.put(queue.queueId(), queue);
        partyToQueue.put(partyId, queue.queueId());
        
        // Notify party members
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(Component.text("Queued for " + dungeonId + " (" + difficulty + ")")
                    .color(NamedTextColor.GREEN));
            }
        }
        
        plugin.getLogger().info("Party " + partyId + " queued for " + dungeonId + " (" + difficulty + ")");
        return true;
    }
    
    /**
     * Removes a party from the matchmaking queue.
     * 
     * @param partyId the party ID
     * @return true if removed from queue
     */
    public boolean leaveQueue(@NotNull UUID partyId) {
        UUID queueId = partyToQueue.remove(partyId);
        if (queueId == null) {
            return false;
        }
        
        MatchmakingQueue queue = matchmakingQueues.remove(queueId);
        if (queue == null) {
            return false;
        }
        
        Party party = parties.get(partyId);
        if (party != null) {
            for (UUID memberId : party.getMembers()) {
                Player member = Bukkit.getPlayer(memberId);
                if (member != null) {
                    member.sendMessage(Component.text("Left matchmaking queue.")
                        .color(NamedTextColor.YELLOW));
                }
            }
        }
        
        return true;
    }
    
    /**
     * Gets the queue for a party.
     * 
     * @param partyId the party ID
     * @return the queue, or null if not queued
     */
    @Nullable
    public MatchmakingQueue getPartyQueue(@NotNull UUID partyId) {
        UUID queueId = partyToQueue.get(partyId);
        return queueId != null ? matchmakingQueues.get(queueId) : null;
    }
    
    /**
     * Gets matchmaking statistics.
     * 
     * @return map of queue statistics
     */
    @NotNull
    public Map<String, Integer> getQueueStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        
        for (MatchmakingQueue queue : matchmakingQueues.values()) {
            String key = queue.dungeonId() + ":" + queue.difficulty();
            stats.merge(key, 1, Integer::sum);
        }
        
        return stats;
    }
    
    /**
     * Starts a ready check for a party.
     * 
     * @param partyId the party ID
     * @return true if ready check started
     */
    public boolean startReadyCheck(@NotNull UUID partyId) {
        UUID queueId = partyToQueue.get(partyId);
        if (queueId == null) {
            return false;
        }
        
        MatchmakingQueue queue = matchmakingQueues.get(queueId);
        if (queue == null || queue.status() != MatchmakingQueue.QueueStatus.QUEUED) {
            return false;
        }
        
        Party party = parties.get(partyId);
        if (party == null) {
            return false;
        }
        
        // Create ready check
        MatchmakingQueue.ReadyCheckData readyCheck = MatchmakingQueue.ReadyCheckData.create(
            party.getMembers(), READY_CHECK_TIMEOUT_MS);
        
        MatchmakingQueue updatedQueue = queue.withStatus(MatchmakingQueue.QueueStatus.READY_CHECK)
            .withReadyCheck(readyCheck);
        matchmakingQueues.put(queueId, updatedQueue);
        
        // Notify party members
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(Component.text("READY CHECK!")
                    .color(NamedTextColor.GOLD)
                    .decorate(TextDecoration.BOLD)
                    .append(Component.newline())
                    .append(Component.text("Use /udx to ready up! (30 seconds)")
                        .color(NamedTextColor.YELLOW)
                        .decoration(TextDecoration.BOLD, false)));
            }
        }
        
        return true;
    }
    
    /**
     * Marks a player as ready for the ready check.
     * 
     * @param playerId the player ID
     * @return true if marked ready
     */
    public boolean markPlayerReady(@NotNull UUID playerId) {
        UUID partyId = playerToParty.get(playerId);
        if (partyId == null) {
            return false;
        }
        
        UUID queueId = partyToQueue.get(partyId);
        if (queueId == null) {
            return false;
        }
        
        MatchmakingQueue queue = matchmakingQueues.get(queueId);
        if (queue == null || queue.status() != MatchmakingQueue.QueueStatus.READY_CHECK || queue.readyCheck() == null) {
            return false;
        }
        
        boolean marked = queue.readyCheck().markReady(playerId);
        if (!marked) {
            return false;
        }
        
        Player player = Bukkit.getPlayer(playerId);
        if (player != null) {
            player.sendMessage(Component.text("You are ready!")
                .color(NamedTextColor.GREEN));
        }
        
        // Check if all players are ready
        if (queue.readyCheck().isComplete()) {
            completeReadyCheck(partyId, queueId);
        }
        
        return true;
    }
    
    private void completeReadyCheck(@NotNull UUID partyId, @NotNull UUID queueId) {
        MatchmakingQueue queue = matchmakingQueues.get(queueId);
        if (queue == null) {
            return;
        }
        
        MatchmakingQueue updatedQueue = queue.withStatus(MatchmakingQueue.QueueStatus.STARTING);
        matchmakingQueues.put(queueId, updatedQueue);
        
        Party party = parties.get(partyId);
        if (party != null) {
            for (UUID memberId : party.getMembers()) {
                Player member = Bukkit.getPlayer(memberId);
                if (member != null) {
                    member.sendMessage(Component.text("All players ready! Starting dungeon...")
                        .color(NamedTextColor.GREEN));
                }
            }
        }
        
        // Start the dungeon (integrate with InstanceManager)
        schedulerUtil.runTaskLater(() -> {
            try {
                // TODO: Integrate with InstanceManager to create dungeon instance
                // instanceManager.createInstance(queue.dungeonId(), queue.difficulty(), party);
                
                // Clean up queue
                matchmakingQueues.remove(queueId);
                partyToQueue.remove(partyId);
                
                plugin.getLogger().info("Started dungeon for party " + partyId);
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to start dungeon for party " + partyId, e);
                
                // Notify party of failure
                if (party != null) {
                    for (UUID memberId : party.getMembers()) {
                        Player member = Bukkit.getPlayer(memberId);
                        if (member != null) {
                            member.sendMessage(Component.text("Failed to start dungeon. Please try again.")
                                .color(NamedTextColor.RED));
                        }
                    }
                }
                
                // Return to queue
                MatchmakingQueue failedQueue = queue.withStatus(MatchmakingQueue.QueueStatus.QUEUED)
                    .withReadyCheck(null);
                matchmakingQueues.put(queueId, failedQueue);
            }
        }, 20L); // 1 second delay
    }
    
    // ===== REJOIN SYSTEM =====
    
    /**
     * Handles player disconnect for rejoin system.
     * 
     * @param playerId the disconnected player ID
     */
    public void handlePlayerDisconnect(@NotNull UUID playerId) {
        UUID partyId = playerToParty.get(playerId);
        if (partyId == null) {
            return;
        }
        
        Party party = parties.get(partyId);
        if (party == null) {
            return;
        }
        
        // Check if party is in an active instance
        // TODO: Integrate with InstanceManager to check active instances
        // For now, create rejoin data for any party member disconnect
        
        RejoinData rejoin = RejoinData.create(playerId, partyId, UUID.randomUUID(), REJOIN_GRACE_PERIOD_MS);
        rejoinData.put(playerId, rejoin);
        
        // Notify remaining party members
        Player disconnectedPlayer = Bukkit.getPlayer(playerId);
        String playerName = disconnectedPlayer != null ? disconnectedPlayer.getName() : "Player";
        
        for (UUID memberId : party.getMembers()) {
            if (!memberId.equals(playerId)) {
                Player member = Bukkit.getPlayer(memberId);
                if (member != null) {
                    member.sendMessage(Component.text(playerName + " disconnected. They have 5 minutes to rejoin.")
                        .color(NamedTextColor.YELLOW));
                }
            }
        }
        
        plugin.getLogger().info("Player " + playerId + " disconnected from party " + partyId + ", rejoin available");
    }
    
    /**
     * Handles player rejoin.
     * 
     * @param playerId the rejoining player ID
     * @return true if rejoin was successful
     */
    public boolean handlePlayerRejoin(@NotNull UUID playerId) {
        RejoinData rejoin = rejoinData.get(playerId);
        if (rejoin == null || !rejoin.canRejoin()) {
            return false;
        }
        
        Party party = parties.get(rejoin.partyId());
        if (party == null) {
            rejoinData.remove(playerId);
            return false;
        }
        
        // Update rejoin status
        rejoinData.put(playerId, rejoin.withStatus(RejoinData.RejoinStatus.REJOINED));
        
        Player player = Bukkit.getPlayer(playerId);
        if (player != null) {
            player.sendMessage(Component.text("Successfully rejoined your party!")
                .color(NamedTextColor.GREEN));
            
            // TODO: Teleport player back to instance if applicable
            // instanceManager.teleportToInstance(player, rejoin.instanceId());
        }
        
        // Notify party members
        String playerName = player != null ? player.getName() : "Player";
        for (UUID memberId : party.getMembers()) {
            if (!memberId.equals(playerId)) {
                Player member = Bukkit.getPlayer(memberId);
                if (member != null) {
                    member.sendMessage(Component.text(playerName + " rejoined the party!")
                        .color(NamedTextColor.GREEN));
                }
            }
        }
        
        return true;
    }
    
    /**
     * Gets rejoin data for a player.
     * 
     * @param playerId the player ID
     * @return rejoin data, or null if none
     */
    @Nullable
    public RejoinData getRejoinData(@NotNull UUID playerId) {
        return rejoinData.get(playerId);
    }
    
    // ===== EVENT HANDLERS =====
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        UUID playerId = event.getPlayer().getUniqueId();
        
        // Check for rejoin opportunity
        if (handlePlayerRejoin(playerId)) {
            return;
        }
        
        // Check for pending invitations
        List<PartyInvitation> pending = getPendingInvitations(playerId);
        if (!pending.isEmpty()) {
            event.getPlayer().sendMessage(Component.text("You have " + pending.size() + " pending party invitation(s)!")
                .color(NamedTextColor.YELLOW)
                .append(Component.newline())
                .append(Component.text("Use /udx to view them.")
                    .color(NamedTextColor.GRAY)));
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        handlePlayerDisconnect(event.getPlayer().getUniqueId());
    }
    
    // ===== BACKGROUND TASKS =====
    
    private void startBackgroundTasks() {
        // Cleanup task - runs every 30 seconds
        cleanupTask = schedulerUtil.runTaskTimerAsynchronously(() -> {
            try {
                cleanupExpiredData();
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "Error in party cleanup task", e);
            }
        }, 600L, 600L); // 30 seconds
        
        // Matchmaking task - runs every 5 seconds
        matchmakingTask = schedulerUtil.runTaskTimerAsynchronously(() -> {
            try {
                processMatchmaking();
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "Error in matchmaking task", e);
            }
        }, 100L, 100L); // 5 seconds
    }
    
    private void cleanupExpiredData() {
        // Clean up expired invitations
        invitations.entrySet().removeIf(entry -> {
            PartyInvitation invitation = entry.getValue();
            if (invitation.isExpired()) {
                // Clean up player invitation tracking
                Set<UUID> playerInvites = playerInvitations.get(invitation.invitee());
                if (playerInvites != null) {
                    playerInvites.remove(invitation.partyId());
                    if (playerInvites.isEmpty()) {
                        playerInvitations.remove(invitation.invitee());
                    }
                }
                return true;
            }
            return false;
        });
        
        // Clean up expired rejoin data
        rejoinData.entrySet().removeIf(entry -> {
            RejoinData rejoin = entry.getValue();
            if (rejoin.isExpired()) {
                return true;
            }
            return false;
        });
        
        // Clean up failed ready checks
        matchmakingQueues.entrySet().removeIf(entry -> {
            MatchmakingQueue queue = entry.getValue();
            if (queue.status() == MatchmakingQueue.QueueStatus.READY_CHECK && 
                queue.readyCheck() != null && queue.readyCheck().isTimedOut()) {
                
                // Notify party of failed ready check
                Party party = parties.get(queue.partyId());
                if (party != null) {
                    for (UUID memberId : party.getMembers()) {
                        Player member = Bukkit.getPlayer(memberId);
                        if (member != null) {
                            member.sendMessage(Component.text("Ready check failed! Returning to queue...")
                                .color(NamedTextColor.RED));
                        }
                    }
                }
                
                // Return to queue
                MatchmakingQueue failedQueue = queue.withStatus(MatchmakingQueue.QueueStatus.QUEUED)
                    .withReadyCheck(null);
                matchmakingQueues.put(entry.getKey(), failedQueue);
                return false; // Don't remove, just update
            }
            return false;
        });
        
        // Clean up offline players from parties (after grace period)
        for (Party party : new ArrayList<>(parties.values())) {
            Set<UUID> toRemove = new HashSet<>();
            
            for (UUID memberId : party.getMembers()) {
                Player member = Bukkit.getPlayer(memberId);
                if (member == null) {
                    // Check if they have rejoin data
                    RejoinData rejoin = rejoinData.get(memberId);
                    if (rejoin == null || rejoin.isExpired()) {
                        toRemove.add(memberId);
                    }
                }
            }
            
            for (UUID memberId : toRemove) {
                party.removeMember(memberId);
                playerToParty.remove(memberId);
                rejoinData.remove(memberId);
            }
            
            if (party.isEmpty()) {
                disbandParty(party.getPartyId());
            }
        }
    }
    
    private void processMatchmaking() {
        // Simple matchmaking - start ready checks for parties that have been queued for a while
        for (MatchmakingQueue queue : new ArrayList<>(matchmakingQueues.values())) {
            if (queue.status() == MatchmakingQueue.QueueStatus.QUEUED) {
                // Check if party is still valid
                Party party = parties.get(queue.partyId());
                if (party == null) {
                    // Clean up invalid queue
                    matchmakingQueues.remove(queue.queueId());
                    partyToQueue.remove(queue.partyId());
                    continue;
                }
                
                // Start ready check after 5 seconds in queue
                if (queue.getQueueDuration() > 5000) {
                    startReadyCheck(queue.partyId());
                }
            }
        }
    }
    
    /**
     * Shuts down the party service.
     */
    public void shutdown() {
        // Cancel background tasks
        if (cleanupTask != null) {
            cleanupTask.cancel();
        }
        if (matchmakingTask != null) {
            matchmakingTask.cancel();
        }
        
        // Disband all parties
        for (UUID partyId : new HashSet<>(parties.keySet())) {
            disbandParty(partyId);
        }
        
        // Clear all data
        parties.clear();
        playerToParty.clear();
        invitations.clear();
        playerInvitations.clear();
        matchmakingQueues.clear();
        partyToQueue.clear();
        rejoinData.clear();
        
        plugin.getLogger().info("Party service shut down");
    }
}
