package com.ultimatedungeon.udx.room;

import org.bukkit.Material;
import org.bukkit.block.data.BlockData;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.*;

/**
 * Represents a complete room template with blocks, entities, and metadata.
 * 
 * <p>Room templates are the building blocks of procedurally generated dungeons.
 * They contain all the information needed to paste a room into a world,
 * including block data, connectors, markers, and metadata.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class RoomTemplate {
    
    private final String id;
    private final String name;
    private final String description;
    private final String author;
    private final Instant createdAt;
    private final int version;
    
    // Dimensions
    private final int width;
    private final int height;
    private final int depth;
    
    // Anchor point (origin for placement)
    private final int anchorX;
    private final int anchorY;
    private final int anchorZ;
    
    // Block data
    private final Material[] blockPalette;
    private final int[] blockData; // Palette indices
    private final Map<Integer, String> tileEntityData; // NBT data for tile entities
    
    // Room metadata
    private final List<Connector> connectors;
    private final List<Marker> markers;
    private final Set<String> tags;
    private final Map<String, Object> properties;
    
    // Generation settings
    private final int weight;
    private final int minDepth;
    private final int maxDepth;
    private final RoomType roomType;
    
    /**
     * Room type classification.
     */
    public enum RoomType {
        ENTRANCE,       // Starting room
        CORRIDOR,       // Connecting hallway
        CHAMBER,        // Standard room
        BOSS,           // Boss encounter room
        TREASURE,       // Loot room
        SECRET,         // Hidden room
        EXIT,           // Ending room
        SPECIAL         // Custom/unique room
    }
    
    private RoomTemplate(@NotNull Builder builder) {
        this.id = builder.id;
        this.name = builder.name;
        this.description = builder.description;
        this.author = builder.author;
        this.createdAt = builder.createdAt;
        this.version = builder.version;
        
        this.width = builder.width;
        this.height = builder.height;
        this.depth = builder.depth;
        
        this.anchorX = builder.anchorX;
        this.anchorY = builder.anchorY;
        this.anchorZ = builder.anchorZ;
        
        this.blockPalette = builder.blockPalette.toArray(new Material[0]);
        this.blockData = builder.blockData.clone();
        this.tileEntityData = Map.copyOf(builder.tileEntityData);
        
        this.connectors = List.copyOf(builder.connectors);
        this.markers = List.copyOf(builder.markers);
        this.tags = Set.copyOf(builder.tags);
        this.properties = Map.copyOf(builder.properties);
        
        this.weight = builder.weight;
        this.minDepth = builder.minDepth;
        this.maxDepth = builder.maxDepth;
        this.roomType = builder.roomType;
    }
    
    // Getters
    
    @NotNull
    public String getId() { return id; }
    
    @NotNull
    public String getName() { return name; }
    
    @NotNull
    public String getDescription() { return description; }
    
    @NotNull
    public String getAuthor() { return author; }
    
    @NotNull
    public Instant getCreatedAt() { return createdAt; }
    
    public int getVersion() { return version; }
    
    public int getWidth() { return width; }
    public int getHeight() { return height; }
    public int getDepth() { return depth; }
    
    public int getAnchorX() { return anchorX; }
    public int getAnchorY() { return anchorY; }
    public int getAnchorZ() { return anchorZ; }
    
    @NotNull
    public Material[] getBlockPalette() { return blockPalette.clone(); }
    
    @NotNull
    public int[] getBlockData() { return blockData.clone(); }
    
    @NotNull
    public Map<Integer, String> getTileEntityData() { return tileEntityData; }
    
    @NotNull
    public List<Connector> getConnectors() { return connectors; }
    
    @NotNull
    public List<Marker> getMarkers() { return markers; }
    
    @NotNull
    public Set<String> getTags() { return tags; }
    
    @NotNull
    public Map<String, Object> getProperties() { return properties; }
    
    public int getWeight() { return weight; }
    public int getMinDepth() { return minDepth; }
    public int getMaxDepth() { return maxDepth; }
    
    @NotNull
    public RoomType getRoomType() { return roomType; }
    
    /**
     * Gets the material at the specified local coordinates.
     * 
     * @param x local X coordinate
     * @param y local Y coordinate
     * @param z local Z coordinate
     * @return the material, or AIR if out of bounds
     */
    @NotNull
    public Material getBlockAt(int x, int y, int z) {
        if (x < 0 || x >= width || y < 0 || y >= height || z < 0 || z >= depth) {
            return Material.AIR;
        }
        
        int index = getBlockIndex(x, y, z);
        int paletteIndex = blockData[index];
        
        if (paletteIndex < 0 || paletteIndex >= blockPalette.length) {
            return Material.AIR;
        }
        
        return blockPalette[paletteIndex];
    }
    
    /**
     * Gets the tile entity data at the specified coordinates.
     * 
     * @param x local X coordinate
     * @param y local Y coordinate
     * @param z local Z coordinate
     * @return the NBT data, or null if none
     */
    @Nullable
    public String getTileEntityAt(int x, int y, int z) {
        int index = getBlockIndex(x, y, z);
        return tileEntityData.get(index);
    }
    
    /**
     * Calculates the block index for the given coordinates.
     */
    private int getBlockIndex(int x, int y, int z) {
        return y * width * depth + z * width + x;
    }
    
    /**
     * Gets markers of a specific type.
     * 
     * @param type the marker type
     * @return list of markers of the specified type
     */
    @NotNull
    public List<Marker> getMarkersByType(@NotNull Marker.MarkerType type) {
        return markers.stream()
            .filter(marker -> marker.type() == type)
            .toList();
    }
    
    /**
     * Gets connectors of a specific direction.
     * 
     * @param direction the connector direction
     * @return list of connectors in the specified direction
     */
    @NotNull
    public List<Connector> getConnectorsByDirection(@NotNull Connector.Direction direction) {
        return connectors.stream()
            .filter(connector -> connector.direction() == direction)
            .toList();
    }
    
    /**
     * Checks if this room has a specific tag.
     * 
     * @param tag the tag to check
     * @return true if the room has the tag
     */
    public boolean hasTag(@NotNull String tag) {
        return tags.contains(tag);
    }
    
    /**
     * Gets a property value.
     * 
     * @param key the property key
     * @return the property value, or null if not found
     */
    @Nullable
    public Object getProperty(@NotNull String key) {
        return properties.get(key);
    }
    
    /**
     * Calculates the total volume of the room.
     * 
     * @return the volume in blocks
     */
    public int getVolume() {
        return width * height * depth;
    }
    
    /**
     * Checks if the room is suitable for the given depth level.
     * 
     * @param depth the dungeon depth
     * @return true if suitable
     */
    public boolean isSuitableForDepth(int depth) {
        return depth >= minDepth && depth <= maxDepth;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof RoomTemplate other)) return false;
        return id.equals(other.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
    
    @Override
    public String toString() {
        return String.format("RoomTemplate{id='%s', name='%s', type=%s, size=%dx%dx%d, connectors=%d, markers=%d}", 
            id, name, roomType, width, height, depth, connectors.size(), markers.size());
    }
    
    /**
     * Builder for creating room templates.
     */
    public static final class Builder {
        private String id;
        private String name = "";
        private String description = "";
        private String author = "";
        private Instant createdAt = Instant.now();
        private int version = 1;
        
        private int width;
        private int height;
        private int depth;
        
        private int anchorX = 0;
        private int anchorY = 0;
        private int anchorZ = 0;
        
        private final List<Material> blockPalette = new ArrayList<>();
        private int[] blockData;
        private final Map<Integer, String> tileEntityData = new HashMap<>();
        
        private final List<Connector> connectors = new ArrayList<>();
        private final List<Marker> markers = new ArrayList<>();
        private final Set<String> tags = new HashSet<>();
        private final Map<String, Object> properties = new HashMap<>();
        
        private int weight = 1;
        private int minDepth = 0;
        private int maxDepth = Integer.MAX_VALUE;
        private RoomType roomType = RoomType.CHAMBER;
        
        public Builder(@NotNull String id) {
            this.id = id;
        }
        
        public Builder name(@NotNull String name) {
            this.name = name;
            return this;
        }
        
        public Builder description(@NotNull String description) {
            this.description = description;
            return this;
        }
        
        public Builder author(@NotNull String author) {
            this.author = author;
            return this;
        }
        
        public Builder createdAt(@NotNull Instant createdAt) {
            this.createdAt = createdAt;
            return this;
        }
        
        public Builder version(int version) {
            this.version = version;
            return this;
        }
        
        public Builder dimensions(int width, int height, int depth) {
            this.width = width;
            this.height = height;
            this.depth = depth;
            this.blockData = new int[width * height * depth];
            return this;
        }
        
        public Builder anchor(int x, int y, int z) {
            this.anchorX = x;
            this.anchorY = y;
            this.anchorZ = z;
            return this;
        }
        
        public Builder blockPalette(@NotNull List<Material> palette) {
            this.blockPalette.clear();
            this.blockPalette.addAll(palette);
            return this;
        }
        
        public Builder blockData(@NotNull int[] data) {
            this.blockData = data.clone();
            return this;
        }
        
        public Builder tileEntity(int index, @NotNull String nbtData) {
            this.tileEntityData.put(index, nbtData);
            return this;
        }
        
        public Builder connector(@NotNull Connector connector) {
            this.connectors.add(connector);
            return this;
        }
        
        public Builder marker(@NotNull Marker marker) {
            this.markers.add(marker);
            return this;
        }
        
        public Builder tag(@NotNull String tag) {
            this.tags.add(tag);
            return this;
        }
        
        public Builder property(@NotNull String key, @NotNull Object value) {
            this.properties.put(key, value);
            return this;
        }
        
        public Builder weight(int weight) {
            this.weight = Math.max(1, weight);
            return this;
        }
        
        public Builder depthRange(int minDepth, int maxDepth) {
            this.minDepth = Math.max(0, minDepth);
            this.maxDepth = Math.max(minDepth, maxDepth);
            return this;
        }
        
        public Builder roomType(@NotNull RoomType roomType) {
            this.roomType = roomType;
            return this;
        }
        
        @NotNull
        public RoomTemplate build() {
            if (id == null || id.trim().isEmpty()) {
                throw new IllegalStateException("Room ID cannot be null or empty");
            }
            if (width <= 0 || height <= 0 || depth <= 0) {
                throw new IllegalStateException("Room dimensions must be positive");
            }
            if (blockData == null || blockData.length != width * height * depth) {
                throw new IllegalStateException("Block data array size must match dimensions");
            }
            
            return new RoomTemplate(this);
        }
    }
}
