package com.ultimatedungeon.udx.party;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;

/**
 * Represents a party in the matchmaking queue.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record MatchmakingQueue(
    @NotNull UUID queueId,
    @NotNull UUID partyId,
    @NotNull String dungeonId,
    @NotNull String difficulty,
    long queueTime,
    @NotNull QueueStatus status,
    @Nullable ReadyCheckData readyCheck
) {
    
    /**
     * Creates a new matchmaking queue entry.
     * 
     * @param partyId the party ID
     * @param dungeonId the dungeon ID
     * @param difficulty the difficulty level
     * @return new queue entry
     */
    @NotNull
    public static MatchmakingQueue create(@NotNull UUID partyId, @NotNull String dungeonId, 
                                        @NotNull String difficulty) {
        return new MatchmakingQueue(
            UUID.randomUUID(),
            partyId,
            dungeonId,
            difficulty,
            System.currentTimeMillis(),
            QueueStatus.QUEUED,
            null
        );
    }
    
    /**
     * Creates a copy with updated status.
     * 
     * @param newStatus the new status
     * @return updated queue entry
     */
    @NotNull
    public MatchmakingQueue withStatus(@NotNull QueueStatus newStatus) {
        return new MatchmakingQueue(queueId, partyId, dungeonId, difficulty, queueTime, newStatus, readyCheck);
    }
    
    /**
     * Creates a copy with ready check data.
     * 
     * @param readyCheckData the ready check data
     * @return updated queue entry
     */
    @NotNull
    public MatchmakingQueue withReadyCheck(@Nullable ReadyCheckData readyCheckData) {
        return new MatchmakingQueue(queueId, partyId, dungeonId, difficulty, queueTime, status, readyCheckData);
    }
    
    /**
     * Gets the time spent in queue in milliseconds.
     * 
     * @return queue time
     */
    public long getQueueDuration() {
        return System.currentTimeMillis() - queueTime;
    }
    
    /**
     * Queue status enumeration.
     */
    public enum QueueStatus {
        /** Party is in queue waiting for match */
        QUEUED,
        /** Match found, ready check in progress */
        READY_CHECK,
        /** Ready check passed, starting dungeon */
        STARTING,
        /** Queue entry cancelled */
        CANCELLED,
        /** Ready check failed */
        FAILED
    }
    
    /**
     * Ready check data.
     */
    public record ReadyCheckData(
        long startTime,
        long timeoutTime,
        @NotNull java.util.Set<UUID> readyPlayers,
        @NotNull java.util.Set<UUID> requiredPlayers
    ) {
        
        /**
         * Creates a new ready check.
         * 
         * @param requiredPlayers players that must ready up
         * @param timeoutMs timeout in milliseconds
         * @return new ready check data
         */
        @NotNull
        public static ReadyCheckData create(@NotNull java.util.Set<UUID> requiredPlayers, long timeoutMs) {
            long now = System.currentTimeMillis();
            return new ReadyCheckData(
                now,
                now + timeoutMs,
                new java.util.concurrent.ConcurrentHashMap<UUID, Boolean>().keySet(),
                requiredPlayers
            );
        }
        
        /**
         * Checks if the ready check has timed out.
         * 
         * @return true if timed out
         */
        public boolean isTimedOut() {
            return System.currentTimeMillis() > timeoutTime;
        }
        
        /**
         * Checks if all required players are ready.
         * 
         * @return true if all ready
         */
        public boolean isComplete() {
            return readyPlayers.containsAll(requiredPlayers);
        }
        
        /**
         * Gets remaining time in milliseconds.
         * 
         * @return remaining time
         */
        public long getRemainingTime() {
            long remaining = timeoutTime - System.currentTimeMillis();
            return Math.max(0, remaining);
        }
        
        /**
         * Marks a player as ready.
         * 
         * @param playerId the player ID
         * @return true if player was added to ready list
         */
        public boolean markReady(@NotNull UUID playerId) {
            if (requiredPlayers.contains(playerId)) {
                return readyPlayers.add(playerId);
            }
            return false;
        }
        
        /**
         * Unmarks a player as ready.
         * 
         * @param playerId the player ID
         * @return true if player was removed from ready list
         */
        public boolean unmarkReady(@NotNull UUID playerId) {
            return readyPlayers.remove(playerId);
        }
    }
}
