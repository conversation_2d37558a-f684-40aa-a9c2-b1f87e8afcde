package com.ultimatedungeon.udx.gen;

import com.ultimatedungeon.udx.room.Connector;
import com.ultimatedungeon.udx.room.RoomTemplate;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * Represents a room layout in a generated dungeon.
 * 
 * <p>This class holds information about a room's position, template,
 * and connections to other rooms in the dungeon layout.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class RoomLayout {
    
    private final int id;
    private final RoomTemplate template;
    private final int x, y, z;
    private final Map<Connector.Direction, RoomLayout> connections;
    private final Set<Connector> usedConnectors;
    private final int depth;
    
    /**
     * Creates a new room layout.
     * 
     * @param id the unique room ID in the layout
     * @param template the room template
     * @param x the X coordinate
     * @param y the Y coordinate
     * @param z the Z coordinate
     * @param depth the depth from entrance (0 = entrance)
     */
    public RoomLayout(int id, @NotNull RoomTemplate template, int x, int y, int z, int depth) {
        this.id = id;
        this.template = template;
        this.x = x;
        this.y = y;
        this.z = z;
        this.depth = depth;
        this.connections = new EnumMap<>(Connector.Direction.class);
        this.usedConnectors = new HashSet<>();
    }
    
    /**
     * Gets the room ID.
     * 
     * @return the room ID
     */
    public int getId() {
        return id;
    }
    
    /**
     * Gets the room template.
     * 
     * @return the room template
     */
    @NotNull
    public RoomTemplate getTemplate() {
        return template;
    }
    
    /**
     * Gets the X coordinate.
     * 
     * @return the X coordinate
     */
    public int getX() {
        return x;
    }
    
    /**
     * Gets the Y coordinate.
     * 
     * @return the Y coordinate
     */
    public int getY() {
        return y;
    }
    
    /**
     * Gets the Z coordinate.
     * 
     * @return the Z coordinate
     */
    public int getZ() {
        return z;
    }
    
    /**
     * Gets the depth from entrance.
     * 
     * @return the depth (0 = entrance)
     */
    public int getDepth() {
        return depth;
    }
    
    /**
     * Gets all connections to other rooms.
     * 
     * @return map of direction to connected room
     */
    @NotNull
    public Map<Connector.Direction, RoomLayout> getConnections() {
        return new EnumMap<>(connections);
    }
    
    /**
     * Gets the connected room in a specific direction.
     * 
     * @param direction the direction
     * @return the connected room, or null if none
     */
    @Nullable
    public RoomLayout getConnection(@NotNull Connector.Direction direction) {
        return connections.get(direction);
    }
    
    /**
     * Connects this room to another room.
     * 
     * @param direction the direction of the connection
     * @param other the other room
     * @param connector the connector being used
     */
    public void connect(@NotNull Connector.Direction direction, @NotNull RoomLayout other, @NotNull Connector connector) {
        connections.put(direction, other);
        usedConnectors.add(connector);
        
        // Create reverse connection
        Connector.Direction opposite = direction.getOpposite();
        other.connections.put(opposite, this);
        
        // Find matching connector in other room
        for (Connector otherConnector : other.template.getConnectors()) {
            if (otherConnector.direction() == opposite && 
                otherConnector.isCompatibleWith(connector) &&
                !other.usedConnectors.contains(otherConnector)) {
                other.usedConnectors.add(otherConnector);
                break;
            }
        }
    }
    
    /**
     * Checks if this room is connected in a specific direction.
     * 
     * @param direction the direction to check
     * @return true if connected
     */
    public boolean isConnected(@NotNull Connector.Direction direction) {
        return connections.containsKey(direction);
    }
    
    /**
     * Gets all available (unused) connectors for a specific direction.
     * 
     * @param direction the direction
     * @return list of available connectors
     */
    @NotNull
    public List<Connector> getAvailableConnectors(@NotNull Connector.Direction direction) {
        return template.getConnectors().stream()
            .filter(connector -> connector.direction() == direction)
            .filter(connector -> !usedConnectors.contains(connector))
            .toList();
    }
    
    /**
     * Gets all available connectors in any direction.
     * 
     * @return list of available connectors
     */
    @NotNull
    public List<Connector> getAllAvailableConnectors() {
        return template.getConnectors().stream()
            .filter(connector -> !usedConnectors.contains(connector))
            .toList();
    }
    
    /**
     * Checks if this room has any available connectors.
     * 
     * @return true if has available connectors
     */
    public boolean hasAvailableConnectors() {
        return usedConnectors.size() < template.getConnectors().size();
    }
    
    /**
     * Gets the number of connections.
     * 
     * @return connection count
     */
    public int getConnectionCount() {
        return connections.size();
    }
    
    /**
     * Checks if this room is a dead end (only one connection).
     * 
     * @return true if dead end
     */
    public boolean isDeadEnd() {
        return connections.size() == 1;
    }
    
    /**
     * Checks if this room is the entrance (depth 0).
     * 
     * @return true if entrance
     */
    public boolean isEntrance() {
        return depth == 0;
    }
    
    /**
     * Gets the world position of a connector.
     * 
     * @param connector the connector
     * @return world coordinates [x, y, z]
     */
    @NotNull
    public int[] getConnectorWorldPosition(@NotNull Connector connector) {
        return new int[] {
            x + connector.x(),
            y + connector.y(),
            z + connector.z()
        };
    }
    
    /**
     * Calculates the bounding box of this room.
     * 
     * @return bounding box [minX, minY, minZ, maxX, maxY, maxZ]
     */
    @NotNull
    public int[] getBoundingBox() {
        return new int[] {
            x,
            y,
            z,
            x + template.getWidth() - 1,
            y + template.getHeight() - 1,
            z + template.getDepth() - 1
        };
    }
    
    /**
     * Checks if this room overlaps with another room.
     * 
     * @param other the other room
     * @return true if overlapping
     */
    public boolean overlaps(@NotNull RoomLayout other) {
        int[] thisBounds = getBoundingBox();
        int[] otherBounds = other.getBoundingBox();
        
        return !(thisBounds[3] < otherBounds[0] || // this.maxX < other.minX
                 thisBounds[0] > otherBounds[3] || // this.minX > other.maxX
                 thisBounds[4] < otherBounds[1] || // this.maxY < other.minY
                 thisBounds[1] > otherBounds[4] || // this.minY > other.maxY
                 thisBounds[5] < otherBounds[2] || // this.maxZ < other.minZ
                 thisBounds[2] > otherBounds[5]);  // this.minZ > other.maxZ
    }
    
    /**
     * Calculates the distance to another room.
     * 
     * @param other the other room
     * @return euclidean distance
     */
    public double distanceTo(@NotNull RoomLayout other) {
        double dx = x - other.x;
        double dy = y - other.y;
        double dz = z - other.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        RoomLayout that = (RoomLayout) obj;
        return id == that.id;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "RoomLayout{" +
               "id=" + id +
               ", template=" + template.getId() +
               ", pos=(" + x + "," + y + "," + z + ")" +
               ", depth=" + depth +
               ", connections=" + connections.size() +
               '}';
    }
}
