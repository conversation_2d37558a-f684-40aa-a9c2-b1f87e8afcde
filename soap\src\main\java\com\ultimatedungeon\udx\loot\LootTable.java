package com.ultimatedungeon.udx.loot;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * Represents a loot table containing weighted entries and roll configuration.
 * 
 * <p>Loot tables define what items can be dropped, their weights,
 * and how many rolls should be performed when generating loot.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class LootTable {
    
    private final String id;
    private final String name;
    private final List<LootEntry> entries;
    private final int minRolls;
    private final int maxRolls;
    private final double jackpotChance;
    private final String jackpotTableId;
    private final Map<String, Object> conditions;
    private final boolean perPlayer;
    
    /**
     * Creates a new loot table.
     * 
     * @param id the table ID
     * @param name the display name
     * @param entries the loot entries
     * @param minRolls minimum number of rolls
     * @param maxRolls maximum number of rolls
     * @param jackpotChance chance for jackpot roll (0.0-1.0)
     * @param jackpotTableId ID of jackpot table (nullable)
     * @param conditions roll conditions
     * @param perPlayer whether loot is per-player or shared
     */
    public LootTable(@NotNull String id, @NotNull String name, 
                     @NotNull List<LootEntry> entries, int minRolls, int maxRolls,
                     double jackpotChance, @Nullable String jackpotTableId,
                     @NotNull Map<String, Object> conditions, boolean perPlayer) {
        this.id = id;
        this.name = name;
        this.entries = new ArrayList<>(entries);
        this.minRolls = Math.max(0, minRolls);
        this.maxRolls = Math.max(this.minRolls, maxRolls);
        this.jackpotChance = Math.max(0.0, Math.min(1.0, jackpotChance));
        this.jackpotTableId = jackpotTableId;
        this.conditions = new HashMap<>(conditions);
        this.perPlayer = perPlayer;
    }
    
    /**
     * Gets the table ID.
     * 
     * @return the table ID
     */
    @NotNull
    public String getId() {
        return id;
    }
    
    /**
     * Gets the display name.
     * 
     * @return the display name
     */
    @NotNull
    public String getName() {
        return name;
    }
    
    /**
     * Gets all loot entries.
     * 
     * @return immutable list of entries
     */
    @NotNull
    public List<LootEntry> getEntries() {
        return Collections.unmodifiableList(entries);
    }
    
    /**
     * Gets the minimum number of rolls.
     * 
     * @return the minimum rolls
     */
    public int getMinRolls() {
        return minRolls;
    }
    
    /**
     * Gets the maximum number of rolls.
     * 
     * @return the maximum rolls
     */
    public int getMaxRolls() {
        return maxRolls;
    }
    
    /**
     * Gets the jackpot chance.
     * 
     * @return the jackpot chance (0.0-1.0)
     */
    public double getJackpotChance() {
        return jackpotChance;
    }
    
    /**
     * Gets the jackpot table ID.
     * 
     * @return the jackpot table ID, or null if none
     */
    @Nullable
    public String getJackpotTableId() {
        return jackpotTableId;
    }
    
    /**
     * Gets the roll conditions.
     * 
     * @return immutable map of conditions
     */
    @NotNull
    public Map<String, Object> getConditions() {
        return Collections.unmodifiableMap(conditions);
    }
    
    /**
     * Checks if this table generates per-player loot.
     * 
     * @return true if per-player, false if shared
     */
    public boolean isPerPlayer() {
        return perPlayer;
    }
    
    /**
     * Checks if this table has a jackpot.
     * 
     * @return true if jackpot is configured
     */
    public boolean hasJackpot() {
        return jackpotChance > 0.0 && jackpotTableId != null;
    }
    
    /**
     * Gets entries filtered by rarity.
     * 
     * @param rarity the rarity to filter by
     * @return list of entries with the specified rarity
     */
    @NotNull
    public List<LootEntry> getEntriesByRarity(@NotNull Rarity rarity) {
        return entries.stream()
                .filter(entry -> entry.rarity() == rarity)
                .toList();
    }
    
    /**
     * Gets entries filtered by type.
     * 
     * @param type the type to filter by
     * @return list of entries with the specified type
     */
    @NotNull
    public List<LootEntry> getEntriesByType(@NotNull LootEntry.LootEntryType type) {
        return entries.stream()
                .filter(entry -> entry.type() == type)
                .toList();
    }
    
    /**
     * Calculates the total weight of all entries.
     * 
     * @return the total weight
     */
    public double getTotalWeight() {
        return entries.stream()
                .mapToDouble(LootEntry::weight)
                .sum();
    }
    
    /**
     * Gets entries that meet the specified conditions.
     * 
     * @param context the context for condition evaluation
     * @return list of valid entries
     */
    @NotNull
    public List<LootEntry> getValidEntries(@NotNull Map<String, Object> context) {
        return entries.stream()
                .filter(entry -> evaluateCondition(entry, context))
                .toList();
    }
    
    /**
     * Adds a new entry to this table.
     * 
     * @param entry the entry to add
     */
    public void addEntry(@NotNull LootEntry entry) {
        entries.add(entry);
    }
    
    /**
     * Removes an entry by ID.
     * 
     * @param entryId the entry ID to remove
     * @return true if an entry was removed
     */
    public boolean removeEntry(@NotNull String entryId) {
        return entries.removeIf(entry -> entry.id().equals(entryId));
    }
    
    /**
     * Gets an entry by ID.
     * 
     * @param entryId the entry ID
     * @return the entry, or null if not found
     */
    @Nullable
    public LootEntry getEntry(@NotNull String entryId) {
        return entries.stream()
                .filter(entry -> entry.id().equals(entryId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Creates a builder for constructing loot tables.
     * 
     * @param id the table ID
     * @param name the table name
     * @return a new builder
     */
    @NotNull
    public static Builder builder(@NotNull String id, @NotNull String name) {
        return new Builder(id, name);
    }
    
    /**
     * Evaluates whether an entry's condition is met.
     * 
     * @param entry the entry to check
     * @param context the evaluation context
     * @return true if the condition is met or no condition exists
     */
    private boolean evaluateCondition(@NotNull LootEntry entry, @NotNull Map<String, Object> context) {
        if (!entry.hasCondition()) {
            return true;
        }
        
        // Simple condition evaluation - can be expanded
        String condition = entry.condition();
        
        // Example conditions:
        // "difficulty:hard" - requires difficulty to be "hard"
        // "player_level:>=10" - requires player level >= 10
        // "boss_killed:true" - requires boss to be killed
        
        if (condition.contains(":")) {
            String[] parts = condition.split(":", 2);
            String key = parts[0];
            String value = parts[1];
            
            Object contextValue = context.get(key);
            if (contextValue == null) {
                return false;
            }
            
            // Handle different comparison types
            if (value.startsWith(">=")) {
                try {
                    double required = Double.parseDouble(value.substring(2));
                    double actual = Double.parseDouble(contextValue.toString());
                    return actual >= required;
                } catch (NumberFormatException e) {
                    return false;
                }
            } else if (value.startsWith("<=")) {
                try {
                    double required = Double.parseDouble(value.substring(2));
                    double actual = Double.parseDouble(contextValue.toString());
                    return actual <= required;
                } catch (NumberFormatException e) {
                    return false;
                }
            } else if (value.startsWith(">")) {
                try {
                    double required = Double.parseDouble(value.substring(1));
                    double actual = Double.parseDouble(contextValue.toString());
                    return actual > required;
                } catch (NumberFormatException e) {
                    return false;
                }
            } else if (value.startsWith("<")) {
                try {
                    double required = Double.parseDouble(value.substring(1));
                    double actual = Double.parseDouble(contextValue.toString());
                    return actual < required;
                } catch (NumberFormatException e) {
                    return false;
                }
            } else {
                // Exact match
                return value.equals(contextValue.toString());
            }
        }
        
        return true;
    }
    
    /**
     * Builder class for constructing loot tables.
     */
    public static class Builder {
        private final String id;
        private final String name;
        private final List<LootEntry> entries = new ArrayList<>();
        private int minRolls = 1;
        private int maxRolls = 1;
        private double jackpotChance = 0.0;
        private String jackpotTableId = null;
        private final Map<String, Object> conditions = new HashMap<>();
        private boolean perPlayer = false;
        
        private Builder(@NotNull String id, @NotNull String name) {
            this.id = id;
            this.name = name;
        }
        
        /**
         * Adds an entry to the table.
         * 
         * @param entry the entry to add
         * @return this builder
         */
        @NotNull
        public Builder addEntry(@NotNull LootEntry entry) {
            entries.add(entry);
            return this;
        }
        
        /**
         * Sets the roll range.
         * 
         * @param minRolls minimum rolls
         * @param maxRolls maximum rolls
         * @return this builder
         */
        @NotNull
        public Builder rolls(int minRolls, int maxRolls) {
            this.minRolls = minRolls;
            this.maxRolls = maxRolls;
            return this;
        }
        
        /**
         * Sets the jackpot configuration.
         * 
         * @param chance the jackpot chance
         * @param tableId the jackpot table ID
         * @return this builder
         */
        @NotNull
        public Builder jackpot(double chance, @NotNull String tableId) {
            this.jackpotChance = chance;
            this.jackpotTableId = tableId;
            return this;
        }
        
        /**
         * Adds a condition.
         * 
         * @param key the condition key
         * @param value the condition value
         * @return this builder
         */
        @NotNull
        public Builder condition(@NotNull String key, @NotNull Object value) {
            conditions.put(key, value);
            return this;
        }
        
        /**
         * Sets whether loot is per-player.
         * 
         * @param perPlayer true for per-player loot
         * @return this builder
         */
        @NotNull
        public Builder perPlayer(boolean perPlayer) {
            this.perPlayer = perPlayer;
            return this;
        }
        
        /**
         * Builds the loot table.
         * 
         * @return the constructed loot table
         */
        @NotNull
        public LootTable build() {
            return new LootTable(id, name, entries, minRolls, maxRolls, 
                               jackpotChance, jackpotTableId, conditions, perPlayer);
        }
    }
}
