package com.ultimatedungeon.udx.data;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * Immutable record representing a player's profile data.
 * 
 * <p>This record contains all persistent player data including statistics,
 * progression, and settings. It follows an immutable pattern where updates
 * create new instances rather than modifying existing ones.</p>
 * 
 * @param uuid the player's unique identifier
 * @param username the player's current username
 * @param firstJoin timestamp of first join
 * @param lastSeen timestamp of last seen
 * @param totalPlaytime total playtime in milliseconds
 * @param dungeonsCompleted total dungeons completed
 * @param deaths total deaths
 * @param seasonScore current season score
 * @param settings JSON string of player settings
 * @param createdAt timestamp when profile was created
 * @param updatedAt timestamp when profile was last updated
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record PlayerProfile(
    @NotNull UUID uuid,
    @NotNull String username,
    long firstJoin,
    long lastSeen,
    long totalPlaytime,
    int dungeonsCompleted,
    int deaths,
    int seasonScore,
    @NotNull String settings,
    long createdAt,
    long updatedAt
) {
    
    /**
     * Creates a PlayerProfile from a database ResultSet.
     * 
     * @param rs the ResultSet containing player data
     * @return a new PlayerProfile instance
     * @throws SQLException if database access fails
     */
    @NotNull
    public static PlayerProfile fromResultSet(@NotNull ResultSet rs) throws SQLException {
        return new PlayerProfile(
            UUID.fromString(rs.getString("uuid")),
            rs.getString("username"),
            rs.getLong("first_join"),
            rs.getLong("last_seen"),
            rs.getLong("total_playtime"),
            rs.getInt("dungeons_completed"),
            rs.getInt("deaths"),
            rs.getInt("season_score"),
            rs.getString("settings"),
            rs.getLong("created_at"),
            rs.getLong("updated_at")
        );
    }
    
    /**
     * Creates a new PlayerProfile with updated username.
     * 
     * @param newUsername the new username
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withUsername(@NotNull String newUsername) {
        return new PlayerProfile(
            uuid,
            newUsername,
            firstJoin,
            System.currentTimeMillis(),
            totalPlaytime,
            dungeonsCompleted,
            deaths,
            seasonScore,
            settings,
            createdAt,
            System.currentTimeMillis()
        );
    }
    
    /**
     * Creates a new PlayerProfile with updated last seen timestamp.
     * 
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withLastSeen() {
        return withLastSeen(System.currentTimeMillis());
    }
    
    /**
     * Creates a new PlayerProfile with updated last seen timestamp.
     * 
     * @param timestamp the timestamp to set
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withLastSeen(long timestamp) {
        return new PlayerProfile(
            uuid,
            username,
            firstJoin,
            timestamp,
            totalPlaytime,
            dungeonsCompleted,
            deaths,
            seasonScore,
            settings,
            createdAt,
            System.currentTimeMillis()
        );
    }
    
    /**
     * Creates a new PlayerProfile with added playtime.
     * 
     * @param additionalTime the playtime to add in milliseconds
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withAddedPlaytime(long additionalTime) {
        return new PlayerProfile(
            uuid,
            username,
            firstJoin,
            lastSeen,
            totalPlaytime + additionalTime,
            dungeonsCompleted,
            deaths,
            seasonScore,
            settings,
            createdAt,
            System.currentTimeMillis()
        );
    }
    
    /**
     * Creates a new PlayerProfile with incremented dungeons completed.
     * 
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withCompletedDungeon() {
        return new PlayerProfile(
            uuid,
            username,
            firstJoin,
            lastSeen,
            totalPlaytime,
            dungeonsCompleted + 1,
            deaths,
            seasonScore,
            settings,
            createdAt,
            System.currentTimeMillis()
        );
    }
    
    /**
     * Creates a new PlayerProfile with incremented deaths.
     * 
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withDeath() {
        return new PlayerProfile(
            uuid,
            username,
            firstJoin,
            lastSeen,
            totalPlaytime,
            dungeonsCompleted,
            deaths + 1,
            seasonScore,
            settings,
            createdAt,
            System.currentTimeMillis()
        );
    }
    
    /**
     * Creates a new PlayerProfile with added season score.
     * 
     * @param points the points to add
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withAddedSeasonScore(int points) {
        return new PlayerProfile(
            uuid,
            username,
            firstJoin,
            lastSeen,
            totalPlaytime,
            dungeonsCompleted,
            deaths,
            seasonScore + points,
            settings,
            createdAt,
            System.currentTimeMillis()
        );
    }
    
    /**
     * Creates a new PlayerProfile with updated settings.
     * 
     * @param newSettings the new settings JSON string
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withSettings(@NotNull String newSettings) {
        return new PlayerProfile(
            uuid,
            username,
            firstJoin,
            lastSeen,
            totalPlaytime,
            dungeonsCompleted,
            deaths,
            seasonScore,
            newSettings,
            createdAt,
            System.currentTimeMillis()
        );
    }
    
    /**
     * Creates a new PlayerProfile with reset season score.
     * 
     * @return a new PlayerProfile instance
     */
    @NotNull
    public PlayerProfile withResetSeasonScore() {
        return new PlayerProfile(
            uuid,
            username,
            firstJoin,
            lastSeen,
            totalPlaytime,
            dungeonsCompleted,
            deaths,
            0,
            settings,
            createdAt,
            System.currentTimeMillis()
        );
    }
    
    /**
     * Gets the player's playtime in hours.
     * 
     * @return playtime in hours
     */
    public double getPlaytimeHours() {
        return totalPlaytime / (1000.0 * 60.0 * 60.0);
    }
    
    /**
     * Gets the player's average deaths per dungeon.
     * 
     * @return average deaths per dungeon, or 0 if no dungeons completed
     */
    public double getAverageDeathsPerDungeon() {
        if (dungeonsCompleted == 0) {
            return 0.0;
        }
        return (double) deaths / dungeonsCompleted;
    }
    
    /**
     * Checks if this is a new player (first join within last hour).
     * 
     * @return true if new player, false otherwise
     */
    public boolean isNewPlayer() {
        long oneHourAgo = System.currentTimeMillis() - (60 * 60 * 1000);
        return firstJoin > oneHourAgo;
    }
    
    /**
     * Checks if the player is currently online (last seen within 5 minutes).
     * 
     * @return true if likely online, false otherwise
     */
    public boolean isLikelyOnline() {
        long fiveMinutesAgo = System.currentTimeMillis() - (5 * 60 * 1000);
        return lastSeen > fiveMinutesAgo;
    }
    
    /**
     * Gets a display-friendly playtime string.
     * 
     * @return formatted playtime string
     */
    @NotNull
    public String getFormattedPlaytime() {
        long seconds = totalPlaytime / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%dd %dh %dm", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%dh %dm", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds % 60);
        } else {
            return String.format("%ds", seconds);
        }
    }
    
    @Override
    public String toString() {
        return String.format("PlayerProfile{uuid=%s, username='%s', dungeons=%d, deaths=%d, score=%d}",
            uuid, username, dungeonsCompleted, deaths, seasonScore);
    }
}
