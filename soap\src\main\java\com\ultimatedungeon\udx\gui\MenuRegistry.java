package com.ultimatedungeon.udx.gui;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.menus.PlayerMainMenu;
import com.ultimatedungeon.udx.gui.menus.AdminHubMenu;
import com.ultimatedungeon.udx.gui.menus.DungeonBrowserMenu;
import com.ultimatedungeon.udx.gui.menus.PartyMenu;
import com.ultimatedungeon.udx.gui.menus.QueueMenu;
import com.ultimatedungeon.udx.gui.menus.SettingsMenu;
import com.ultimatedungeon.udx.gui.menus.AchievementsMenu;
import com.ultimatedungeon.udx.gui.menus.LeaderboardsMenu;
import com.ultimatedungeon.udx.gui.menus.EditorHubMenu;
import com.ultimatedungeon.udx.gui.menus.RoomEditorMenu;
import com.ultimatedungeon.udx.gui.menus.SpawnerEditorMenu;
import com.ultimatedungeon.udx.gui.menus.BossEditorMenu;
import com.ultimatedungeon.udx.gui.menus.LootEditorMenu;
import com.ultimatedungeon.udx.gui.menus.CreateDungeonMenu;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Registry and manager for all GUI menus in UltimateDungeonX.
 * 
 * <p>This class handles menu creation, event routing, and provides
 * convenient methods for opening specific menus.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class MenuRegistry implements Listener {
    
    private final UltimateDungeonX plugin;
    private final ConcurrentMap<Player, Menu> activeMenus;
    
    public MenuRegistry(@NotNull UltimateDungeonX plugin) {
        this.plugin = plugin;
        this.activeMenus = new ConcurrentHashMap<>();
    }
    
    /**
     * Gives a portal keystone item to the player.
     */
    public void givePortalKeystone(@NotNull Player player) {
        ItemStack keystone = createPortalKeystone();
        
        if (player.getInventory().firstEmpty() != -1) {
            player.getInventory().addItem(keystone);
            player.sendMessage(Component.text("Portal Keystone given! Right-click to place a dungeon portal.")
                .color(NamedTextColor.GREEN));
        } else {
            player.sendMessage(Component.text("Your inventory is full! Clear some space and try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Creates a portal keystone item.
     */
    @NotNull
    private ItemStack createPortalKeystone() {
        ItemStack keystone = new ItemStack(Material.NETHER_STAR);
        ItemMeta meta = keystone.getItemMeta();
        
        if (meta != null) {
            meta.displayName(Component.text("Portal Keystone").color(NamedTextColor.LIGHT_PURPLE));
            meta.lore(List.of(
                Component.empty(),
                Component.text("Right-click to place a dungeon portal").color(NamedTextColor.GRAY),
                Component.text("Players can use the portal to access dungeons").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("UltimateDungeonX").color(NamedTextColor.DARK_PURPLE)
            ));
            keystone.setItemMeta(meta);
        }
        
        return keystone;
    }
    
    /**
     * Opens a menu for a player and tracks it.
     */
    public void openMenu(@NotNull Player player, @NotNull Menu menu) {
        // Close any existing menu
        Menu existing = activeMenus.get(player);
        if (existing != null) {
            existing.close();
        }
        
        // Track the new menu
        activeMenus.put(player, menu);
        
        // Open the menu
        menu.open();
    }
    
    /**
     * Gets the active menu for a player.
     */
    @Nullable
    public Menu getActiveMenu(@NotNull Player player) {
        return activeMenus.get(player);
    }
    
    /**
     * Checks if a player has an active menu.
     */
    public boolean hasActiveMenu(@NotNull Player player) {
        return activeMenus.containsKey(player);
    }
    
    /**
     * Removes a player's active menu tracking.
     */
    public void removeActiveMenu(@NotNull Player player) {
        activeMenus.remove(player);
    }
    
    /**
     * Handles inventory click events for menus.
     */
    @EventHandler
    public void onInventoryClick(@NotNull InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) {
            return;
        }
        
        // Check if the player has an active menu
        Menu menu = activeMenus.get(player);
        if (menu == null) {
            return;
        }
        
        // CRITICAL FIX: Cancel ALL clicks when player has an active menu
        // This prevents items from being moved between inventories
        event.setCancelled(true);
        
        // Only handle clicks in the menu inventory itself
        if (event.getClickedInventory() == menu.getInventory()) {
            // Let the menu handle the click
            menu.handleClick(event);
        }
        
        // For clicks outside the menu (player inventory), we just cancel them
        // This prevents shift-clicking items into the menu or moving menu items to player inventory
    }
    
    /**
     * Handles inventory close events for menus.
     */
    @EventHandler
    public void onInventoryClose(@NotNull InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player player)) {
            return;
        }
        
        // Check if the player has an active menu
        Menu menu = activeMenus.get(player);
        if (menu == null) {
            return;
        }
        
        // Check if the closed inventory is the menu inventory
        if (event.getInventory() != menu.getInventory()) {
            return;
        }
        
        // Remove from tracking
        activeMenus.remove(player);
        
        // Let the menu handle the close
        menu.onClose();
    }
    
    /**
     * Shuts down the menu registry and closes all active menus.
     */
    public void shutdown() {
        // Close all active menus
        for (Menu menu : activeMenus.values()) {
            try {
                menu.close();
            } catch (Exception e) {
                plugin.getLogger().warning("Error closing menu during shutdown: " + e.getMessage());
            }
        }
        
        activeMenus.clear();
        plugin.getLogger().info("Menu registry shut down, closed " + activeMenus.size() + " active menus");
    }
    
    /**
     * Gets the plugin instance.
     */
    @NotNull
    public Plugin getPlugin() {
        return plugin;
    }
    
    /**
     * Gets the number of active menus.
     */
    public int getActiveMenuCount() {
        return activeMenus.size();
    }
    
    // ===== MENU OPENING METHODS =====
    
    /**
     * Opens the main menu for a player.
     */
    public void openMainMenu(@NotNull Player player) {
        try {
            PlayerMainMenu menu = new PlayerMainMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening main menu for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening main menu. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the dungeon browser for a player.
     */
    public void openDungeonBrowser(@NotNull Player player) {
        try {
            DungeonBrowserMenu menu = new DungeonBrowserMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening dungeon browser for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening dungeon browser. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the party menu for a player.
     */
    public void openPartyMenu(@NotNull Player player) {
        try {
            PartyMenu menu = new PartyMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening party menu for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening party menu. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the achievements menu for a player.
     */
    public void openAchievementsMenu(@NotNull Player player) {
        try {
            AchievementsMenu menu = new AchievementsMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening achievements menu for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening achievements menu. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the leaderboards menu for a player.
     */
    public void openLeaderboardsMenu(@NotNull Player player) {
        try {
            LeaderboardsMenu menu = new LeaderboardsMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening leaderboards menu for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening leaderboards menu. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the settings menu for a player.
     */
    public void openSettingsMenu(@NotNull Player player) {
        try {
            SettingsMenu menu = new SettingsMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening settings menu for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening settings menu. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the admin hub for a player.
     */
    public void openAdminHub(@NotNull Player player) {
        try {
            AdminHubMenu menu = new AdminHubMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening admin hub for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening admin hub. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the editor hub for a player.
     */
    public void openEditorHub(@NotNull Player player) {
        try {
            EditorHubMenu menu = new EditorHubMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening editor hub for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening editor hub. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the room editor for a player.
     */
    public void openRoomEditor(@NotNull Player player) {
        try {
            RoomEditorMenu menu = new RoomEditorMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening room editor for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening room editor. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the spawner editor for a player.
     */
    public void openSpawnerEditor(@NotNull Player player) {
        try {
            SpawnerEditorMenu menu = new SpawnerEditorMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening spawner editor for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening spawner editor. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the boss editor for a player.
     */
    public void openBossEditor(@NotNull Player player) {
        try {
            BossEditorMenu menu = new BossEditorMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening boss editor for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening boss editor. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the loot editor for a player.
     */
    public void openLootEditor(@NotNull Player player) {
        try {
            LootEditorMenu menu = new LootEditorMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening loot editor for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening loot editor. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Opens the create dungeon menu for a player.
     */
    public void openCreateDungeonMenu(@NotNull Player player) {
        try {
            CreateDungeonMenu menu = new CreateDungeonMenu(plugin, player);
            openMenu(player, menu);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening create dungeon menu for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Error opening create dungeon menu. Please try again.")
                .color(NamedTextColor.RED));
        }
    }
}
