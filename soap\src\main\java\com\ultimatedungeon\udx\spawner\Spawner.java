package com.ultimatedungeon.udx.spawner;

import org.bukkit.Location;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * Defines a mob spawner with waves and trigger conditions.
 * 
 * <p>Spawners control when and how mobs are spawned in dungeon rooms,
 * supporting wave-based spawning with various trigger conditions.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class Spawner {
    
    private final String id;
    private final Location location;
    private final List<Wave> waves;
    private final TriggerCondition triggerCondition;
    private final RespawnPolicy respawnPolicy;
    private final double spawnRadius;
    private final int maxConcurrentMobs;
    private final Set<String> tags;
    private final Map<String, Object> metadata;
    
    // Runtime state
    private int currentWave = 0;
    private boolean isActive = false;
    private boolean isCompleted = false;
    private long lastSpawnTime = 0;
    private final Set<UUID> spawnedMobs = new HashSet<>();
    
    private Spawner(@NotNull Builder builder) {
        this.id = builder.id;
        this.location = builder.location.clone();
        this.waves = List.copyOf(builder.waves);
        this.triggerCondition = builder.triggerCondition;
        this.respawnPolicy = builder.respawnPolicy;
        this.spawnRadius = builder.spawnRadius;
        this.maxConcurrentMobs = builder.maxConcurrentMobs;
        this.tags = Set.copyOf(builder.tags);
        this.metadata = new HashMap<>(builder.metadata);
    }
    
    /**
     * Gets the spawner ID.
     * 
     * @return the spawner ID
     */
    @NotNull
    public String getId() {
        return id;
    }
    
    /**
     * Gets the spawn location.
     * 
     * @return the spawn location
     */
    @NotNull
    public Location getLocation() {
        return location.clone();
    }
    
    /**
     * Gets all waves.
     * 
     * @return list of waves
     */
    @NotNull
    public List<Wave> getWaves() {
        return waves;
    }
    
    /**
     * Gets the current wave.
     * 
     * @return the current wave, or null if none
     */
    @Nullable
    public Wave getCurrentWave() {
        if (currentWave >= 0 && currentWave < waves.size()) {
            return waves.get(currentWave);
        }
        return null;
    }
    
    /**
     * Gets the trigger condition.
     * 
     * @return the trigger condition
     */
    @NotNull
    public TriggerCondition getTriggerCondition() {
        return triggerCondition;
    }
    
    /**
     * Gets the respawn policy.
     * 
     * @return the respawn policy
     */
    @NotNull
    public RespawnPolicy getRespawnPolicy() {
        return respawnPolicy;
    }
    
    /**
     * Gets the spawn radius.
     * 
     * @return the spawn radius in blocks
     */
    public double getSpawnRadius() {
        return spawnRadius;
    }
    
    /**
     * Gets the maximum concurrent mobs.
     * 
     * @return the max concurrent mobs
     */
    public int getMaxConcurrentMobs() {
        return maxConcurrentMobs;
    }
    
    /**
     * Gets the tags.
     * 
     * @return set of tags
     */
    @NotNull
    public Set<String> getTags() {
        return tags;
    }
    
    /**
     * Checks if spawner has a tag.
     * 
     * @param tag the tag
     * @return true if has tag
     */
    public boolean hasTag(@NotNull String tag) {
        return tags.contains(tag);
    }
    
    /**
     * Gets the current wave number.
     * 
     * @return the current wave number (0-based)
     */
    public int getCurrentWaveNumber() {
        return currentWave;
    }
    
    /**
     * Gets the total number of waves.
     * 
     * @return the total wave count
     */
    public int getTotalWaves() {
        return waves.size();
    }
    
    /**
     * Checks if the spawner is active.
     * 
     * @return true if active
     */
    public boolean isActive() {
        return isActive;
    }
    
    /**
     * Checks if the spawner is completed.
     * 
     * @return true if completed
     */
    public boolean isCompleted() {
        return isCompleted;
    }
    
    /**
     * Gets the last spawn time.
     * 
     * @return the last spawn time in milliseconds
     */
    public long getLastSpawnTime() {
        return lastSpawnTime;
    }
    
    /**
     * Gets the spawned mob UUIDs.
     * 
     * @return set of spawned mob UUIDs
     */
    @NotNull
    public Set<UUID> getSpawnedMobs() {
        return new HashSet<>(spawnedMobs);
    }
    
    /**
     * Gets the number of alive spawned mobs.
     * 
     * @return the alive mob count
     */
    public int getAliveMobCount() {
        return (int) spawnedMobs.stream()
            .map(uuid -> org.bukkit.Bukkit.getEntity(uuid))
            .filter(Objects::nonNull)
            .filter(entity -> !entity.isDead())
            .count();
    }
    
    /**
     * Activates the spawner.
     */
    public void activate() {
        this.isActive = true;
        this.isCompleted = false;
    }
    
    /**
     * Deactivates the spawner.
     */
    public void deactivate() {
        this.isActive = false;
    }
    
    /**
     * Completes the spawner.
     */
    public void complete() {
        this.isCompleted = true;
        this.isActive = false;
    }
    
    /**
     * Resets the spawner to initial state.
     */
    public void reset() {
        this.currentWave = 0;
        this.isActive = false;
        this.isCompleted = false;
        this.lastSpawnTime = 0;
        this.spawnedMobs.clear();
    }
    
    /**
     * Advances to the next wave.
     * 
     * @return true if advanced, false if no more waves
     */
    public boolean nextWave() {
        if (currentWave + 1 < waves.size()) {
            currentWave++;
            return true;
        } else {
            complete();
            return false;
        }
    }
    
    /**
     * Adds a spawned mob UUID.
     * 
     * @param mobUuid the mob UUID
     */
    public void addSpawnedMob(@NotNull UUID mobUuid) {
        spawnedMobs.add(mobUuid);
        lastSpawnTime = System.currentTimeMillis();
    }
    
    /**
     * Removes a spawned mob UUID.
     * 
     * @param mobUuid the mob UUID
     */
    public void removeSpawnedMob(@NotNull UUID mobUuid) {
        spawnedMobs.remove(mobUuid);
    }
    
    /**
     * Gets metadata value.
     * 
     * @param key the metadata key
     * @return the value, or null if not found
     */
    @Nullable
    public Object getMetadata(@NotNull String key) {
        return metadata.get(key);
    }
    
    /**
     * Sets metadata value.
     * 
     * @param key the metadata key
     * @param value the value
     */
    public void setMetadata(@NotNull String key, @Nullable Object value) {
        if (value == null) {
            metadata.remove(key);
        } else {
            metadata.put(key, value);
        }
    }
    
    /**
     * Creates a new builder.
     * 
     * @param id the spawner ID
     * @param location the spawn location
     * @return new builder instance
     */
    @NotNull
    public static Builder builder(@NotNull String id, @NotNull Location location) {
        return new Builder(id, location);
    }
    
    /**
     * Trigger condition types.
     */
    public enum TriggerCondition {
        ON_ENTER,       // When players enter the room
        ON_CLEAR,       // When previous wave is cleared
        TIMER,          // After a time delay
        LEVER,          // When a lever is activated
        PRESSURE_PLATE, // When pressure plate is stepped on
        MANUAL          // Manually triggered
    }
    
    /**
     * Respawn policy types.
     */
    public enum RespawnPolicy {
        ONCE,           // Spawn once per dungeon run
        PER_RUN,        // Respawn each dungeon run
        TIMED,          // Respawn after time interval
        ON_CLEAR        // Respawn when room is cleared
    }
    
    /**
     * Represents a wave of mobs to spawn.
     */
    public static final class Wave {
        private final List<MobSpawn> mobSpawns;
        private final long delay;
        private final TriggerCondition trigger;
        private final Map<String, Object> conditions;
        
        public Wave(@NotNull List<MobSpawn> mobSpawns, long delay, @NotNull TriggerCondition trigger, @NotNull Map<String, Object> conditions) {
            this.mobSpawns = List.copyOf(mobSpawns);
            this.delay = delay;
            this.trigger = trigger;
            this.conditions = Map.copyOf(conditions);
        }
        
        @NotNull public List<MobSpawn> getMobSpawns() { return mobSpawns; }
        public long getDelay() { return delay; }
        @NotNull public TriggerCondition getTrigger() { return trigger; }
        @NotNull public Map<String, Object> getConditions() { return conditions; }
        
        public int getTotalMobCount() {
            return mobSpawns.stream().mapToInt(MobSpawn::getCount).sum();
        }
    }
    
    /**
     * Represents a mob spawn entry in a wave.
     */
    public static final class MobSpawn {
        private final String mobId;
        private final int count;
        private final double weight;
        private final long spawnDelay;
        private final double spreadRadius;
        private final boolean isElite;
        private final Map<String, Object> parameters;
        
        public MobSpawn(@NotNull String mobId, int count, double weight, long spawnDelay, double spreadRadius, boolean isElite, @NotNull Map<String, Object> parameters) {
            this.mobId = mobId;
            this.count = count;
            this.weight = weight;
            this.spawnDelay = spawnDelay;
            this.spreadRadius = spreadRadius;
            this.isElite = isElite;
            this.parameters = Map.copyOf(parameters);
        }
        
        @NotNull public String getMobId() { return mobId; }
        public int getCount() { return count; }
        public double getWeight() { return weight; }
        public long getSpawnDelay() { return spawnDelay; }
        public double getSpreadRadius() { return spreadRadius; }
        public boolean isElite() { return isElite; }
        @NotNull public Map<String, Object> getParameters() { return parameters; }
    }
    
    /**
     * Builder for spawners.
     */
    public static final class Builder {
        private final String id;
        private final Location location;
        private final List<Wave> waves = new ArrayList<>();
        private TriggerCondition triggerCondition = TriggerCondition.ON_ENTER;
        private RespawnPolicy respawnPolicy = RespawnPolicy.ONCE;
        private double spawnRadius = 3.0;
        private int maxConcurrentMobs = 10;
        private final Set<String> tags = new HashSet<>();
        private final Map<String, Object> metadata = new HashMap<>();
        
        private Builder(@NotNull String id, @NotNull Location location) {
            this.id = id;
            this.location = location;
        }
        
        @NotNull
        public Builder wave(@NotNull Wave wave) {
            this.waves.add(wave);
            return this;
        }
        
        @NotNull
        public Builder waves(@NotNull Collection<Wave> waves) {
            this.waves.addAll(waves);
            return this;
        }
        
        @NotNull
        public Builder triggerCondition(@NotNull TriggerCondition condition) {
            this.triggerCondition = condition;
            return this;
        }
        
        @NotNull
        public Builder respawnPolicy(@NotNull RespawnPolicy policy) {
            this.respawnPolicy = policy;
            return this;
        }
        
        @NotNull
        public Builder spawnRadius(double radius) {
            this.spawnRadius = radius;
            return this;
        }
        
        @NotNull
        public Builder maxConcurrentMobs(int max) {
            this.maxConcurrentMobs = max;
            return this;
        }
        
        @NotNull
        public Builder tag(@NotNull String tag) {
            this.tags.add(tag);
            return this;
        }
        
        @NotNull
        public Builder tags(@NotNull Collection<String> tags) {
            this.tags.addAll(tags);
            return this;
        }
        
        @NotNull
        public Builder metadata(@NotNull String key, @Nullable Object value) {
            if (value == null) {
                this.metadata.remove(key);
            } else {
                this.metadata.put(key, value);
            }
            return this;
        }
        
        @NotNull
        public Spawner build() {
            if (waves.isEmpty()) {
                throw new IllegalArgumentException("Spawner must have at least one wave");
            }
            return new Spawner(this);
        }
    }
    
    @Override
    public String toString() {
        return "Spawner{" +
               "id='" + id + '\'' +
               ", waves=" + waves.size() +
               ", currentWave=" + currentWave +
               ", active=" + isActive +
               ", completed=" + isCompleted +
               '}';
    }
}
