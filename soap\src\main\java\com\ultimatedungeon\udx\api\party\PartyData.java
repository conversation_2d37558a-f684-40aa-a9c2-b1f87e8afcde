package com.ultimatedungeon.udx.api.party;

import org.jetbrains.annotations.NotNull;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Immutable representation of party data for API consumers.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record PartyData(
    @NotNull UUID partyId,
    @NotNull UUID leaderId,
    @NotNull List<UUID> members,
    @NotNull Instant createdAt,
    int maxSize,
    boolean isPublic,
    boolean isQueued
) {
    
    /**
     * Creates new party data.
     */
    public PartyData {
        if (partyId == null) {
            throw new IllegalArgumentException("Party ID cannot be null");
        }
        if (leaderId == null) {
            throw new IllegalArgumentException("Leader ID cannot be null");
        }
        if (members == null) {
            throw new IllegalArgumentException("Members list cannot be null");
        }
        if (createdAt == null) {
            throw new IllegalArgumentException("Created at cannot be null");
        }
        if (maxSize < 1) {
            throw new IllegalArgumentException("Max size must be at least 1");
        }
        if (!members.contains(leaderId)) {
            throw new IllegalArgumentException("Leader must be in members list");
        }
    }
    
    /**
     * Gets the current party size.
     * 
     * @return The number of members in the party
     */
    public int getCurrentSize() {
        return members.size();
    }
    
    /**
     * Checks if the party is full.
     * 
     * @return True if the party has reached max capacity
     */
    public boolean isFull() {
        return getCurrentSize() >= maxSize;
    }
    
    /**
     * Checks if a player is the party leader.
     * 
     * @param playerId The player UUID
     * @return True if the player is the leader
     */
    public boolean isLeader(@NotNull UUID playerId) {
        return leaderId.equals(playerId);
    }
    
    /**
     * Checks if a player is a member of the party.
     * 
     * @param playerId The player UUID
     * @return True if the player is a member
     */
    public boolean isMember(@NotNull UUID playerId) {
        return members.contains(playerId);
    }
    
    /**
     * Gets the number of available slots in the party.
     * 
     * @return The number of free slots
     */
    public int getAvailableSlots() {
        return maxSize - getCurrentSize();
    }
    
    /**
     * Gets the age of the party in milliseconds.
     * 
     * @return The party age in milliseconds
     */
    public long getAgeMs() {
        return Instant.now().toEpochMilli() - createdAt.toEpochMilli();
    }
    
    /**
     * Checks if the party can accept new members.
     * 
     * @return True if the party can accept new members
     */
    public boolean canAcceptMembers() {
        return !isFull() && !isQueued;
    }
}
