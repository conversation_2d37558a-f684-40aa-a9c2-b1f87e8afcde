package com.ultimatedungeon.udx.api.progression;

import org.jetbrains.annotations.NotNull;
import java.util.UUID;

public record SeasonStatsData(
    @NotNull UUID playerId,
    int seasonNumber,
    int score,
    int dungeonsCompleted,
    int achievementsEarned,
    long totalPlayTimeMs,
    int globalRank,
    double averageCompletionTime
) {
    public SeasonStatsData {
        if (playerId == null) throw new IllegalArgumentException("Player ID cannot be null");
        if (seasonNumber < 1) throw new IllegalArgumentException("Season number must be positive");
        if (score < 0) throw new IllegalArgumentException("Score cannot be negative");
        if (dungeonsCompleted < 0) throw new IllegalArgumentException("Dungeons completed cannot be negative");
        if (achievementsEarned < 0) throw new IllegalArgumentException("Achievements earned cannot be negative");
        if (totalPlayTimeMs < 0) throw new IllegalArgumentException("Total play time cannot be negative");
        if (globalRank < 0) throw new IllegalArgumentException("Global rank cannot be negative");
        if (averageCompletionTime < 0) throw new IllegalArgumentException("Average completion time cannot be negative");
    }
}
