# UltimateDungeonX Configuration Guide

This document provides detailed information about all configuration files and formats used by UltimateDungeonX.

## Configuration Files Overview

```
plugins/UltimateDungeonX/
├── config.yml                 # Main plugin configuration
├── players.db                 # SQLite database (or players.yml)
├── dungeons/                  # Dungeon definitions
│   ├── <dungeon_id>/
│   │   ├── dungeon.yml        # Dungeon configuration
│   │   ├── rooms/             # Room templates (.udxroom files)
│   │   ├── loot/              # Loot table definitions (.json)
│   │   ├── mobs/              # Mob definitions (.json)
│   │   └── bosses/            # Boss definitions (.json)
└── affixes/                   # Global affix definitions
    └── *.yml                  # Affix configuration files
```

## Main Configuration (`config.yml`)

### Basic Settings

```yaml
# Plugin version (auto-managed)
config-version: 1

# General Settings
build_world: "udx_build"
max_concurrent_instances: 10
instance_timeout_minutes: 60
default_language: "en_US"

# Debug and Logging
debug: false
log_level: "INFO"  # TRACE, DEBUG, INFO, WARN, ERROR
performance_logging: true
```

### Performance Settings

```yaml
performance:
  # Tick budgeting (microseconds per tick)
  tick_budget_per_frame: 5000
  
  # Entity limits
  max_entities_per_instance: 100
  entity_cleanup_interval: 200  # ticks
  
  # World generation
  async_world_generation: true
  world_generation_threads: 2
  chunk_preload_radius: 3
  
  # Memory management
  memory_cleanup_interval: 6000  # ticks (5 minutes)
  gc_threshold_mb: 512
  object_pool_enabled: true
  
  # Block placement batching
  blocks_per_tick: 4096
  lighting_update_delay: 5  # ticks
```

### Database Configuration

```yaml
database:
  # Database type: "sqlite" or "mysql"
  type: "sqlite"
  
  # SQLite settings
  file: "players.db"
  connection_pool_size: 5
  
  # MySQL settings (when type is "mysql")
  mysql:
    host: "localhost"
    port: 3306
    database: "udx"
    username: "udx_user"
    password: "secure_password"
    connection_pool_size: 10
    connection_timeout: 30000
    
  # Backup settings
  backup:
    enabled: true
    interval_hours: 24
    keep_backups: 7
```

### GUI Settings

```yaml
gui:
  # Update intervals
  menu_update_interval: 20  # ticks
  animation_speed: 10       # ticks per frame
  
  # Effects
  sound_effects_enabled: true
  particle_effects_enabled: true
  click_sound: "UI_BUTTON_CLICK"
  
  # Menu behavior
  auto_close_delay: 300     # ticks (15 seconds)
  confirm_destructive_actions: true
  
  # Pagination
  items_per_page: 45
  navigation_slots: [45, 46, 47, 48, 49, 50, 51, 52, 53]
```

### Integration Settings

```yaml
integrations:
  # Vault economy integration
  vault:
    enabled: true
    currency_symbol: "$"
    
  # PlaceholderAPI integration
  placeholderapi:
    enabled: true
    
  # Discord integration (if available)
  discord:
    enabled: false
    webhook_url: ""
    log_completions: true
```

### Affix System

```yaml
affixes:
  # Rotation schedule
  rotation:
    daily_count: 2
    weekly_count: 4
    monthly_count: 6
    rotation_time: "00:00"  # UTC time
    
  # Rarity weights
  rarity_weights:
    COMMON: 50.0
    UNCOMMON: 30.0
    RARE: 15.0
    EPIC: 4.0
    LEGENDARY: 1.0
    
  # Seasonal events
  seasonal_events:
    enabled: true
    halloween:
      start_date: "10-01"
      end_date: "11-01"
      special_affixes: ["spooky_mobs", "cursed_ground"]
      bonus_multiplier: 1.5
```

## Dungeon Configuration (`dungeon.yml`)

### Basic Properties

```yaml
# Unique identifier
id: "my_dungeon"

# Display information
name: "My Custom Dungeon"
description: "A challenging dungeon for experienced adventurers"
lore:
  - "§7Ancient ruins filled with"
  - "§7dangerous creatures and"
  - "§7valuable treasures."

# Theme and environment
theme: "medieval"        # medieval, fire, ice, nature, shadow, etc.
environment: "indoor"    # indoor, outdoor, underground, aerial
biome: "PLAINS"         # Minecraft biome for the instance world

# Difficulty settings
difficulty_base: 5       # Base difficulty (1-10)
length_min: 6           # Minimum number of rooms
length_max: 10          # Maximum number of rooms
branching_factor: 2     # Maximum branches from main path
```

### Requirements and Restrictions

```yaml
# Level requirements
level_requirement:
  min: 20
  max: 50
  recommended: 35
  
# Party configuration
party:
  min_size: 2
  max_size: 5
  recommended_size: 4
  scaling_enabled: true
  
# Prerequisites
prerequisites:
  dungeons: ["tutorial_dungeon"]  # Must complete these first
  achievements: ["first_steps"]   # Must have these achievements
  
# Cooldowns
cooldowns:
  per_player: 3600      # seconds between runs (per player)
  per_party: 1800       # seconds between runs (per party)
```

### Room Configuration

```yaml
rooms:
  # Special rooms
  start_room: "entrance_hall"
  boss_room: "throne_room"
  
  # Available room pool
  available_rooms:
    - id: "corridor_basic"
      weight: 10.0
      tags: ["corridor", "basic"]
    - id: "chamber_large"
      weight: 5.0
      tags: ["chamber", "combat"]
    - id: "treasure_room"
      weight: 2.0
      tags: ["treasure", "rare"]
      
  # Room selection rules
  selection_rules:
    max_same_type: 3      # Max rooms of same type
    required_tags: ["combat"]  # Must include rooms with these tags
    forbidden_combinations:   # These room types cannot be adjacent
      - ["treasure_room", "treasure_room"]
```

### Difficulty Scaling

```yaml
difficulty_tiers:
  normal:
    mob_health_multiplier: 1.0
    mob_damage_multiplier: 1.0
    mob_count_multiplier: 1.0
    loot_multiplier: 1.0
    experience_multiplier: 1.0
    
  hard:
    mob_health_multiplier: 1.5
    mob_damage_multiplier: 1.3
    mob_count_multiplier: 1.2
    loot_multiplier: 1.3
    experience_multiplier: 1.4
    
  mythic:
    mob_health_multiplier: 2.0
    mob_damage_multiplier: 1.6
    mob_count_multiplier: 1.5
    loot_multiplier: 1.6
    experience_multiplier: 1.7
    
  nightmare:
    mob_health_multiplier: 3.0
    mob_damage_multiplier: 2.0
    mob_count_multiplier: 2.0
    loot_multiplier: 2.0
    experience_multiplier: 2.0
```

### Environmental Settings

```yaml
environment:
  # Lighting and atmosphere
  lighting: "dim"         # bright, normal, dim, dark
  weather: "clear"        # clear, rain, storm, snow
  temperature: "normal"   # hot, warm, normal, cool, cold
  
  # Visual effects
  fog_enabled: true
  fog_color: "#808080"
  fog_density: "medium"   # light, medium, heavy
  
  # Ambient effects
  ambient_sounds:
    - sound: "AMBIENT_CAVE"
      volume: 0.5
      pitch: 1.0
      interval: 100        # ticks between plays
      
  particle_effects:
    - type: "SMOKE_NORMAL"
      density: "low"       # low, medium, high
      areas: ["torches", "braziers"]
      
  # Environmental hazards
  hazards:
    - type: "poison_gas"
      damage: 2.0
      interval: 40         # ticks
      areas: ["swamp_rooms"]
      effects:
        - type: "POISON"
          duration: 100
          amplifier: 0
```

### Objectives and Rewards

```yaml
objectives:
  primary:
    - type: "kill_boss"
      target: "dungeon_boss"
      description: "Defeat the dungeon boss"
      
  secondary:
    - type: "collect_items"
      target: "ancient_relic"
      count: 3
      description: "Collect 3 Ancient Relics"
      
    - type: "speed_run"
      time_limit: 900      # 15 minutes
      description: "Complete within 15 minutes"
      
    - type: "no_deaths"
      description: "Complete without any deaths"

rewards:
  # Currency rewards
  currency:
    base_amount: 100
    bonus_per_difficulty: 50
    
  # Experience rewards
  experience:
    base_amount: 500
    bonus_per_difficulty: 200
    
  # Title rewards
  titles:
    - condition: "first_completion"
      title: "Dungeon Explorer"
    - condition: "nightmare_completion"
      title: "Nightmare Conqueror"
      
  # Achievement unlocks
  achievements:
    - "dungeon_master"
    - "treasure_hunter"
```

## Loot Table Format (`.json`)

### Basic Structure

```json
{
  "id": "common_chest_loot",
  "name": "Common Chest Loot",
  "description": "Standard loot found in common chests",
  
  "min_rolls": 1,
  "max_rolls": 3,
  "jackpot_enabled": false,
  "jackpot_chance": 0.05,
  
  "entries": [
    {
      "id": "iron_sword",
      "type": "ITEM",
      "weight": 10.0,
      "min_quantity": 1,
      "max_quantity": 1,
      "rarity": "COMMON",
      "item": {
        "material": "IRON_SWORD",
        "amount": 1,
        "display_name": "§fRusty Blade",
        "lore": [
          "§7An old but reliable sword",
          "§7found in the dungeon depths."
        ],
        "enchantments": {
          "SHARPNESS": 1,
          "UNBREAKING": 2
        }
      }
    }
  ]
}
```

### Entry Types

#### Item Entries

```json
{
  "id": "magic_helmet",
  "type": "ITEM",
  "weight": 5.0,
  "rarity": "RARE",
  "item": {
    "material": "DIAMOND_HELMET",
    "amount": 1,
    "display_name": "§9Helmet of Warding",
    "lore": ["§7Provides magical protection"],
    "enchantments": {
      "PROTECTION_ENVIRONMENTAL": 3,
      "UNBREAKING": 3
    },
    "custom_model_data": 1001,
    "glow": true,
    "color": "#0066FF"  // For leather armor
  }
}
```

#### Currency Entries

```json
{
  "id": "gold_reward",
  "type": "CURRENCY",
  "weight": 15.0,
  "currency_amount": 50.0,
  "rarity": "COMMON"
}
```

#### Command Entries

```json
{
  "id": "title_grant",
  "type": "COMMAND",
  "weight": 2.0,
  "command": "udx title give %player% treasure_hunter",
  "rarity": "EPIC"
}
```

## Mob Definition Format (`.json`)

### Basic Mob Structure

```json
{
  "id": "skeleton_warrior",
  "display_name": "§fSkeleton Warrior",
  "entity_type": "SKELETON",
  "category": "undead",
  "difficulty": 3,
  
  "attributes": {
    "max_health": 30.0,
    "attack_damage": 8.0,
    "movement_speed": 0.25,
    "armor": 2.0,
    "armor_toughness": 0.0,
    "knockback_resistance": 0.2,
    "follow_range": 16.0
  },
  
  "equipment": {
    "main_hand": {
      "material": "IRON_SWORD",
      "enchantments": {
        "SHARPNESS": 2
      },
      "drop_chance": 0.1
    },
    "helmet": {
      "material": "IRON_HELMET",
      "drop_chance": 0.05
    }
  },
  
  "behaviors": [
    {
      "type": "MELEE_ATTACK",
      "priority": 1,
      "speed": 1.0
    },
    {
      "type": "LOOK_AT_PLAYER",
      "priority": 2,
      "range": 8.0
    }
  ],
  
  "abilities": [
    {
      "id": "shield_bash",
      "type": "MELEE_SPECIAL",
      "cooldown": 100,
      "damage": 12.0,
      "effects": [
        {
          "type": "SLOWNESS",
          "duration": 60,
          "amplifier": 1
        }
      ],
      "telegraph": {
        "duration": 20,
        "particle": "CRIT",
        "sound": "ENTITY_PLAYER_ATTACK_CRIT"
      }
    }
  ],
  
  "drops": {
    "table_id": "skeleton_drops",
    "experience": 10
  },
  
  "flags": [
    "FIRE_IMMUNE",
    "KNOCKBACK_RESISTANT"
  ]
}
```

## Boss Definition Format (`.json`)

### Multi-Phase Boss

```json
{
  "id": "ancient_lich",
  "display_name": "§5Ancient Lich",
  "entity_type": "WITHER_SKELETON",
  "category": "undead_boss",
  
  "attributes": {
    "max_health": 500.0,
    "attack_damage": 20.0,
    "movement_speed": 0.3,
    "armor": 10.0,
    "knockback_resistance": 1.0
  },
  
  "phases": [
    {
      "id": "phase_1",
      "name": "Awakening",
      "health_percentage": 100.0,
      "abilities": [
        {
          "id": "dark_bolt",
          "timing": {
            "type": "INTERVAL",
            "interval": 60
          }
        }
      ],
      "telegraph": {
        "title": "§5The Ancient Lich awakens!",
        "duration": 60
      }
    },
    {
      "id": "phase_2",
      "name": "Fury",
      "health_percentage": 50.0,
      "abilities": [
        {
          "id": "dark_bolt",
          "timing": {
            "type": "INTERVAL",
            "interval": 40
          }
        },
        {
          "id": "summon_minions",
          "timing": {
            "type": "INTERVAL",
            "interval": 120
          }
        }
      ]
    }
  ],
  
  "abilities": [
    {
      "id": "dark_bolt",
      "name": "Dark Bolt",
      "type": "PROJECTILE",
      "range": 30.0,
      "damage": 25.0,
      "projectile_type": "FIREBALL",
      "telegraph": {
        "duration": 40,
        "particle": "SOUL_FIRE_FLAME",
        "sound": "ENTITY_WITHER_SHOOT"
      }
    }
  ],
  
  "bossbar": {
    "title": "§5Ancient Lich",
    "color": "PURPLE",
    "style": "SEGMENTED_10"
  }
}
```

## Affix Definition Format (`.yml`)

### Affix Structure

```yaml
id: "berserk_mobs"
name: "Berserk"
description: "Mobs deal increased damage but have reduced health"
rarity: "COMMON"
type: "OFFENSIVE"
category: "mob_modifier"

# Numerical effects
effects:
  mob_damage_multiplier: 1.5
  mob_health_multiplier: 0.7
  mob_speed_multiplier: 1.2
  
# Visual effects
visual_effects:
  mob_particles: "ANGRY_VILLAGER"
  mob_glow_color: "RED"
  ambient_sound: "ENTITY_ZOMBIE_ANGRY"
  
# Duration and conditions
duration: "PERMANENT"  # PERMANENT, TIMED, CONDITIONAL
conditions:
  - "mob_type:HOSTILE"
  - "not:boss_mob"
  
# Incompatible affixes
conflicts:
  - "armored_foes"
  - "regenerating_enemies"
```

## Room Template Format (`.udxroom`)

Room templates are stored in a custom binary format, but the metadata is human-readable:

### Room Metadata

```yaml
# Room identification
id: "treasure_chamber"
name: "Treasure Chamber"
description: "A room filled with valuable loot"

# Dimensions
size:
  width: 15
  height: 6
  depth: 15
  
# Connectors (where this room can connect to others)
connectors:
  - direction: "NORTH"
    position: { x: 7, y: 1, z: 0 }
    size: { width: 3, height: 3 }
  - direction: "SOUTH"
    position: { x: 7, y: 1, z: 14 }
    size: { width: 3, height: 3 }
    
# Markers (special locations within the room)
markers:
  - type: "CHEST"
    position: { x: 7, y: 2, z: 7 }
    data: { table_id: "treasure_loot" }
  - type: "SPAWN"
    position: { x: 3, y: 1, z: 3 }
    data: { mob_id: "treasure_guardian", wave: 1 }
    
# Room properties
properties:
  weight: 5.0           # Selection weight
  tags: ["treasure", "rare", "combat"]
  min_distance_from_start: 3
  max_instances_per_dungeon: 1
  
# Environmental settings
environment:
  lighting_level: 8
  biome_override: "DESERT"
  weather_override: "CLEAR"
```

## Version Migration

UltimateDungeonX automatically handles configuration migrations between versions:

### Migration Process

1. **Backup**: Original config is backed up to `config.yml.backup.<version>`
2. **Detection**: Plugin detects `config-version` field
3. **Migration**: Applies necessary transformations
4. **Validation**: Ensures all required fields are present
5. **Update**: Updates `config-version` to current

### Custom Migration Scripts

For complex migrations, custom scripts can be placed in `migrations/`:

```yaml
# migrations/1.0.0_to_1.1.0.yml
from_version: "1.0.0"
to_version: "1.1.0"

transformations:
  - type: "rename_field"
    from: "old_field_name"
    to: "new_field_name"
    
  - type: "add_field"
    path: "performance.new_setting"
    value: true
    
  - type: "remove_field"
    path: "deprecated_setting"
```

## Validation and Error Handling

### Configuration Validation

All configuration files are validated on load:

- **Schema Validation**: Ensures required fields are present
- **Type Checking**: Validates data types and ranges
- **Reference Validation**: Checks that referenced IDs exist
- **Logical Validation**: Ensures settings make logical sense

### Error Messages

Clear error messages help identify configuration issues:

```
[UDX] Configuration Error in dungeons/my_dungeon/dungeon.yml:
  Line 15: Invalid difficulty_base value '15'. Must be between 1 and 10.
  Line 23: Referenced room 'invalid_room' does not exist.
  Line 31: loot_table 'missing_table' not found in loot/ directory.
```

### Default Fallbacks

When configuration is invalid, the plugin uses safe defaults:

- Missing optional fields use documented defaults
- Invalid values are replaced with safe alternatives
- Broken references are logged but don't prevent startup

---

*For more information, see the other documentation files in the `docs/` directory.*
