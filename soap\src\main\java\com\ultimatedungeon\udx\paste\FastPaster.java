package com.ultimatedungeon.udx.paste;

import com.ultimatedungeon.udx.room.RoomTemplate;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Level;

/**
 * High-performance block pasting system with tick budgeting.
 * 
 * <p>This service handles asynchronous block placement with proper
 * tick budgeting to prevent server lag during large operations.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class FastPaster {
    
    private final Plugin plugin;
    private final SchedulerUtil schedulerUtil;
    
    // Performance settings
    private int maxBlocksPerTick = 4096;
    private long maxNanosPerTick = 6_000_000L; // 6ms
    private int chunkPreloadRadius = 2;
    private boolean enableLightingFixes = true;
    
    // Active paste operations
    private final Queue<PasteOperation> pasteQueue;
    private final Map<UUID, PasteOperation> activeOperations;
    private BukkitTask pasteTask;
    
    // Performance monitoring
    private final AtomicLong totalBlocksPasted = new AtomicLong(0);
    private final AtomicInteger activeOperationCount = new AtomicInteger(0);
    
    public FastPaster(@NotNull Plugin plugin, @NotNull SchedulerUtil schedulerUtil) {
        this.plugin = plugin;
        this.schedulerUtil = schedulerUtil;
        
        this.pasteQueue = new ConcurrentLinkedQueue<>();
        this.activeOperations = new HashMap<>();
    }
    
    /**
     * Initializes the fast paster.
     */
    public void initialize() {
        startPasteProcessor();
        
        plugin.getLogger().info("Fast paster initialized (max " + maxBlocksPerTick + " blocks/tick, " + 
                               (maxNanosPerTick / 1_000_000.0) + "ms budget)");
    }
    
    /**
     * Starts the paste processor task.
     */
    private void startPasteProcessor() {
        pasteTask = schedulerUtil.runTaskTimer(this::processPasteQueue, 1L, 1L);
    }
    
    /**
     * Pastes a room template at the specified location.
     * 
     * @param template the room template to paste
     * @param world the target world
     * @param x the X coordinate
     * @param y the Y coordinate
     * @param z the Z coordinate
     * @return future that completes when pasting is done
     */
    @NotNull
    public CompletableFuture<Void> pasteRoom(@NotNull RoomTemplate template, @NotNull World world, int x, int y, int z) {
        return pasteRoom(template, world, x, y, z, null);
    }
    
    /**
     * Pastes a room template at the specified location with progress callback.
     * 
     * @param template the room template to paste
     * @param world the target world
     * @param x the X coordinate
     * @param y the Y coordinate
     * @param z the Z coordinate
     * @param progressCallback optional progress callback (0.0 to 1.0)
     * @return future that completes when pasting is done
     */
    @NotNull
    public CompletableFuture<Void> pasteRoom(
        @NotNull RoomTemplate template, 
        @NotNull World world, 
        int x, int y, int z,
        @Nullable ProgressCallback progressCallback
    ) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        
        // Create paste operation
        PasteOperation operation = new PasteOperation(
            UUID.randomUUID(),
            template,
            world,
            x, y, z,
            future,
            progressCallback
        );
        
        // Pre-load chunks asynchronously
        preloadChunks(world, x, y, z, template.getWidth(), template.getHeight(), template.getDepth())
            .thenRun(() -> {
                // Add to queue for processing
                pasteQueue.offer(operation);
                activeOperations.put(operation.id, operation);
                activeOperationCount.incrementAndGet();
                
                plugin.getLogger().info("Queued paste operation for room " + template.getId() + 
                                      " at (" + x + "," + y + "," + z + ")");
            })
            .exceptionally(throwable -> {
                plugin.getLogger().log(Level.SEVERE, "Failed to preload chunks for paste operation", throwable);
                future.completeExceptionally(throwable);
                return null;
            });
        
        return future;
    }
    
    /**
     * Pre-loads chunks in the specified area.
     */
    @NotNull
    private CompletableFuture<Void> preloadChunks(@NotNull World world, int x, int y, int z, int width, int height, int depth) {
        return CompletableFuture.runAsync(() -> {
            int minChunkX = (x - chunkPreloadRadius) >> 4;
            int maxChunkX = (x + width + chunkPreloadRadius) >> 4;
            int minChunkZ = (z - chunkPreloadRadius) >> 4;
            int maxChunkZ = (z + depth + chunkPreloadRadius) >> 4;
            
            List<CompletableFuture<Chunk>> chunkFutures = new ArrayList<>();
            
            for (int chunkX = minChunkX; chunkX <= maxChunkX; chunkX++) {
                for (int chunkZ = minChunkZ; chunkZ <= maxChunkZ; chunkZ++) {
                    chunkFutures.add(world.getChunkAtAsync(chunkX, chunkZ));
                }
            }
            
            // Wait for all chunks to load
            CompletableFuture.allOf(chunkFutures.toArray(new CompletableFuture[0])).join();
        });
    }
    
    /**
     * Processes the paste queue each tick.
     */
    private void processPasteQueue() {
        if (pasteQueue.isEmpty()) {
            return;
        }
        
        long startTime = System.nanoTime();
        int blocksProcessed = 0;
        
        while (!pasteQueue.isEmpty() && blocksProcessed < maxBlocksPerTick) {
            PasteOperation operation = pasteQueue.peek();
            if (operation == null) {
                break;
            }
            
            // Process blocks from this operation
            int processed = processOperationBlocks(operation, maxBlocksPerTick - blocksProcessed, maxNanosPerTick - (System.nanoTime() - startTime));
            blocksProcessed += processed;
            
            // Check if operation is complete
            if (operation.isComplete()) {
                pasteQueue.poll();
                completeOperation(operation);
            }
            
            // Check time budget
            if (System.nanoTime() - startTime >= maxNanosPerTick) {
                break;
            }
        }
        
        if (blocksProcessed > 0) {
            totalBlocksPasted.addAndGet(blocksProcessed);
        }
    }
    
    /**
     * Processes blocks for a specific operation.
     * 
     * @param operation the operation to process
     * @param maxBlocks maximum blocks to process
     * @param maxNanos maximum nanoseconds to spend
     * @return number of blocks processed
     */
    private int processOperationBlocks(@NotNull PasteOperation operation, int maxBlocks, long maxNanos) {
        long startTime = System.nanoTime();
        int processed = 0;
        
        RoomTemplate template = operation.template;
        World world = operation.world;
        int baseX = operation.x;
        int baseY = operation.y;
        int baseZ = operation.z;
        
        while (processed < maxBlocks && operation.currentIndex < operation.totalBlocks) {
            // Calculate coordinates from index
            int index = operation.currentIndex;
            int localX = index % template.getWidth();
            int localY = (index / template.getWidth()) % template.getHeight();
            int localZ = index / (template.getWidth() * template.getHeight());
            
            int worldX = baseX + localX;
            int worldY = baseY + localY;
            int worldZ = baseZ + localZ;
            
            // Get material from template
            Material material = template.getBlockAt(localX, localY, localZ);
            
            // Set block
            if (material != Material.AIR) {
                Block block = world.getBlockAt(worldX, worldY, worldZ);
                block.setType(material, false); // Don't apply physics immediately
                
                // Handle tile entity data
                String tileData = template.getTileEntityAt(localX, localY, localZ);
                if (tileData != null) {
                    // TODO: Apply NBT data to tile entity
                }
            }
            
            operation.currentIndex++;
            processed++;
            
            // Update progress
            if (operation.progressCallback != null) {
                double progress = (double) operation.currentIndex / operation.totalBlocks;
                operation.progressCallback.onProgress(progress);
            }
            
            // Check time budget
            if (System.nanoTime() - startTime >= maxNanos) {
                break;
            }
        }
        
        return processed;
    }
    
    /**
     * Completes a paste operation.
     */
    private void completeOperation(@NotNull PasteOperation operation) {
        activeOperations.remove(operation.id);
        activeOperationCount.decrementAndGet();
        
        // Apply lighting fixes if enabled
        if (enableLightingFixes) {
            schedulerUtil.runTaskAsynchronously(() -> applyLightingFixes(operation));
        }
        
        // Complete the future
        operation.future.complete(null);
        
        plugin.getLogger().info("Completed paste operation for room " + operation.template.getId() + 
                              " (" + operation.totalBlocks + " blocks)");
    }
    
    /**
     * Applies lighting fixes to the pasted area.
     */
    private void applyLightingFixes(@NotNull PasteOperation operation) {
        try {
            World world = operation.world;
            int minChunkX = operation.x >> 4;
            int maxChunkX = (operation.x + operation.template.getWidth()) >> 4;
            int minChunkZ = operation.z >> 4;
            int maxChunkZ = (operation.z + operation.template.getDepth()) >> 4;
            
            // Recalculate lighting for affected chunks
            for (int chunkX = minChunkX; chunkX <= maxChunkX; chunkX++) {
                for (int chunkZ = minChunkZ; chunkZ <= maxChunkZ; chunkZ++) {
                    Chunk chunk = world.getChunkAt(chunkX, chunkZ);
                    // Force lighting recalculation
                    schedulerUtil.runTask(() -> {
                        // This will be handled by the server automatically in most cases
                        // For more advanced lighting, we could use NMS if needed
                    });
                }
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to apply lighting fixes", e);
        }
    }
    
    /**
     * Cancels a paste operation.
     * 
     * @param operationId the operation ID
     * @return true if cancelled successfully
     */
    public boolean cancelOperation(@NotNull UUID operationId) {
        PasteOperation operation = activeOperations.get(operationId);
        if (operation != null) {
            activeOperations.remove(operationId);
            activeOperationCount.decrementAndGet();
            operation.future.cancel(false);
            
            plugin.getLogger().info("Cancelled paste operation: " + operationId);
            return true;
        }
        return false;
    }
    
    /**
     * Gets statistics about paste operations.
     * 
     * @return map of statistics
     */
    @NotNull
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalBlocksPasted", totalBlocksPasted.get());
        stats.put("activeOperations", activeOperationCount.get());
        stats.put("queuedOperations", pasteQueue.size());
        stats.put("maxBlocksPerTick", maxBlocksPerTick);
        stats.put("maxNanosPerTick", maxNanosPerTick);
        
        return stats;
    }
    
    /**
     * Updates performance settings.
     */
    public void updateSettings(int maxBlocksPerTick, long maxNanosPerTick, int chunkPreloadRadius, boolean enableLightingFixes) {
        this.maxBlocksPerTick = Math.max(1, maxBlocksPerTick);
        this.maxNanosPerTick = Math.max(1_000_000L, maxNanosPerTick); // At least 1ms
        this.chunkPreloadRadius = Math.max(0, chunkPreloadRadius);
        this.enableLightingFixes = enableLightingFixes;
        
        plugin.getLogger().info("Updated FastPaster settings: " + maxBlocksPerTick + " blocks/tick, " + 
                               (maxNanosPerTick / 1_000_000.0) + "ms budget");
    }
    
    /**
     * Shuts down the fast paster.
     */
    public void shutdown() {
        // Cancel paste task
        if (pasteTask != null && !pasteTask.isCancelled()) {
            pasteTask.cancel();
        }
        
        // Cancel all active operations
        for (PasteOperation operation : activeOperations.values()) {
            operation.future.cancel(false);
        }
        
        activeOperations.clear();
        pasteQueue.clear();
        
        plugin.getLogger().info("Fast paster shut down (pasted " + totalBlocksPasted.get() + " total blocks)");
    }
    
    /**
     * Progress callback interface.
     */
    @FunctionalInterface
    public interface ProgressCallback {
        void onProgress(double progress);
    }
    
    /**
     * Internal paste operation data.
     */
    private static class PasteOperation {
        final UUID id;
        final RoomTemplate template;
        final World world;
        final int x, y, z;
        final CompletableFuture<Void> future;
        final ProgressCallback progressCallback;
        
        final int totalBlocks;
        int currentIndex = 0;
        
        PasteOperation(UUID id, RoomTemplate template, World world, int x, int y, int z, 
                      CompletableFuture<Void> future, ProgressCallback progressCallback) {
            this.id = id;
            this.template = template;
            this.world = world;
            this.x = x;
            this.y = y;
            this.z = z;
            this.future = future;
            this.progressCallback = progressCallback;
            this.totalBlocks = template.getVolume();
        }
        
        boolean isComplete() {
            return currentIndex >= totalBlocks;
        }
    }
}
