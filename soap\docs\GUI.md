# UltimateDungeonX GUI Guide

This document provides comprehensive information about the GUI system, menu flows, and user interface design in UltimateDungeonX.

## Overview

UltimateDungeonX features a **GUI-first design** where all functionality is accessible through intuitive menus. Commands serve only as entry points to open these menus, ensuring a consistent and user-friendly experience.

## GUI Architecture

### Core Components

- **Menu System**: Inventory-based GUIs with click handling
- **Navigation**: Breadcrumb system with back/close buttons
- **Animations**: Smooth transitions and visual feedback
- **Pagination**: Automatic handling of large data sets
- **Confirmation**: Safety dialogs for destructive actions

### Design Principles

1. **Consistency**: All menus follow the same layout patterns
2. **Clarity**: Clear icons, descriptions, and visual hierarchy
3. **Accessibility**: Color-blind friendly with text alternatives
4. **Performance**: Efficient updates and minimal lag
5. **Responsiveness**: Real-time data updates where appropriate

## Menu Hierarchy

```
Main Menu (/udx)
├── Play
│   ├── Dungeon Browser
│   │   ├── Dungeon Details
│   │   └── Queue Menu
│   └── Quick Play
├── Parties
│   ├── Create Party
│   ├── Party Management
│   └── Invitations
├── Achievements
│   ├── Achievement Browser
│   ├── Progress Tracking
│   └── Leaderboards
└── Settings
    ├── GUI Preferences
    ├── Notification Settings
    └── Performance Options

Admin Hub (/udx admin)
├── Dungeons
│   ├── Dungeon List
│   ├── Create Dungeon
│   ├── Edit Dungeon
│   └── Dungeon Statistics
├── Instances
│   ├── Active Instances
│   ├── Instance Details
│   └── Force Actions
├── Content
│   ├── Room Editor
│   ├── Mob Editor
│   ├── Boss Editor
│   └── Loot Editor
├── System
│   ├── Affix Rotation
│   ├── Global Settings
│   └── Debug Tools
└── Analytics
    ├── Performance Monitor
    ├── Player Statistics
    └── Usage Reports
```

## Player Menus

### Main Menu (`/udx`)

**Layout**: 54 slots (6 rows)
**Update Frequency**: Every 20 ticks (1 second)

```
┌─────────────────────────────────────────────────────┐
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [P] [?] [T] [?] [A] [?] [S] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [?] [?] [?] [X] [?] [?] [?] [?]                 │
└─────────────────────────────────────────────────────┘

P = Play (Compass)
T = Parties (Player Head)  
A = Achievements (Nether Star)
S = Settings (Redstone)
X = Close (Barrier)
? = Decorative Glass Panes
```

**Items**:
- **Play** (Slot 10): `COMPASS` - "§6Play Dungeons"
  - Lore: Current available dungeons, queue status
  - Click: Opens Dungeon Browser
- **Parties** (Slot 12): `PLAYER_HEAD` - "§9Manage Parties"
  - Lore: Current party info, member count
  - Click: Opens Party Management
- **Achievements** (Slot 14): `NETHER_STAR` - "§eAchievements"
  - Lore: Progress summary, recent unlocks
  - Click: Opens Achievement Browser
- **Settings** (Slot 16): `REDSTONE` - "§7Settings"
  - Lore: Current preferences
  - Click: Opens Settings Menu

### Dungeon Browser

**Layout**: 54 slots with pagination
**Update Frequency**: Every 40 ticks (2 seconds)

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [?] [?] [T] [?] [?] [?] [?]                 │
│ [D] [D] [D] [D] [D] [D] [D] [D] [D]                 │
│ [D] [D] [D] [D] [D] [D] [D] [D] [D]                 │
│ [D] [D] [D] [D] [D] [D] [D] [D] [D]                 │
│ [D] [D] [D] [D] [D] [D] [D] [D] [D]                 │
│ [<] [?] [?] [?] [I] [?] [?] [?] [>]                 │
└─────────────────────────────────────────────────────┘

B = Back Button
T = Title/Filter
D = Dungeon Cards
< = Previous Page
> = Next Page  
I = Page Info
```

**Dungeon Cards**:
Each dungeon is represented by a themed item with rich information:

```yaml
Material: STRUCTURE_BLOCK (or theme-appropriate)
Display Name: "§6[Dungeon Name]"
Lore:
  - "§7[Description line 1]"
  - "§7[Description line 2]"
  - ""
  - "§fDifficulty: §e[1-10 stars]"
  - "§fLength: §e[X-Y rooms]"
  - "§fParty Size: §e[X-Y players]"
  - "§fLevel Range: §e[XX-XX]"
  - ""
  - "§aActive Affixes:"
  - "§7• [Affix 1]"
  - "§7• [Affix 2]"
  - ""
  - "§fRewards: §e[Currency] §7| §b[Experience]"
  - ""
  - "§eClick to view details!"
Enchantments: [Glow if recommended]
```

### Dungeon Details

**Layout**: 54 slots
**Purpose**: Detailed information and queue options

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [?] [?] [I] [?] [?] [?] [?]                 │
│ [?] [R] [R] [R] [R] [R] [R] [R] [?]                 │
│ [?] [O] [O] [O] [O] [O] [O] [O] [?]                 │
│ [?] [A] [A] [A] [A] [A] [A] [A] [?]                 │
│ [?] [L] [L] [L] [L] [L] [L] [L] [?]                 │
│ [?] [N] [H] [M] [Q] [E] [?] [?] [?]                 │
└─────────────────────────────────────────────────────┘

I = Dungeon Icon/Info
R = Requirements
O = Objectives  
A = Active Affixes
L = Loot Preview
N/H/M/E = Difficulty Buttons (Normal/Hard/Mythic/Epic)
Q = Queue Button
```

**Interactive Elements**:
- **Difficulty Buttons**: Show scaling information on hover
- **Queue Button**: Changes based on party status and requirements
- **Loot Preview**: Animated cycling through possible rewards
- **Affix Tooltips**: Detailed descriptions of active modifiers

### Party Management

**Layout**: 54 slots
**Update Frequency**: Every 10 ticks (0.5 seconds)

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [?] [?] [P] [?] [?] [?] [?]                 │
│ [?] [M] [M] [M] [M] [M] [?] [?] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [I] [I] [I] [I] [I] [?] [?] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [C] [D] [L] [S] [?] [?] [?] [?] [?]                 │
└─────────────────────────────────────────────────────┘

P = Party Info
M = Member Slots
I = Invitation Slots  
C = Create Party
D = Disband Party
L = Leave Party
S = Settings
```

**Member Display**:
```yaml
Material: PLAYER_HEAD
Display Name: "§a[Player Name] §7([Role])"
Lore:
  - "§7Level: §f[XX]"
  - "§7Status: §a[Online/Offline]"
  - "§7Joined: §f[Time ago]"
  - ""
  - "§7Dungeons Completed: §f[XX]"
  - "§7Success Rate: §f[XX%]"
  - ""
  - "§eRight-click for options"
Skull: Player's head texture
```

### Queue Menu

**Layout**: 27 slots (3 rows)
**Update Frequency**: Every 5 ticks (0.25 seconds)

```
┌─────────────────────────────────┐
│ [B] [?] [?] [?] [Q] [?] [?] [?] │
│ [?] [P] [P] [P] [P] [P] [?] [?] │
│ [?] [R] [C] [?] [?] [?] [?] [?] │
└─────────────────────────────────┘

Q = Queue Status
P = Party Members (Ready Status)
R = Ready Button
C = Cancel Queue
```

**Ready Check System**:
- Green glass: Player is ready
- Yellow glass: Player is thinking (10s timer)
- Red glass: Player declined or timed out
- Animated: Pulsing effect during countdown

### Achievement Browser

**Layout**: 54 slots with categories
**Update Frequency**: Every 60 ticks (3 seconds)

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [C] [C] [C] [C] [C] [?] [?]                 │
│ [?] [A] [A] [A] [A] [A] [A] [A] [?]                 │
│ [?] [A] [A] [A] [A] [A] [A] [A] [?]                 │
│ [?] [A] [A] [A] [A] [A] [A] [A] [?]                 │
│ [?] [A] [A] [A] [A] [A] [A] [A] [?]                 │
│ [<] [?] [?] [S] [?] [?] [?] [?] [>]                 │
└─────────────────────────────────────────────────────┘

C = Category Filters
A = Achievement Items
S = Statistics
```

**Achievement Display**:
```yaml
# Completed Achievement
Material: NETHER_STAR
Display Name: "§a[Achievement Name]"
Lore:
  - "§7[Description]"
  - ""
  - "§aCompleted: §f[Date]"
  - "§aReward: §f[Reward Description]"
  - ""
  - "§7Progress: §a[100%] §7(§f[X/X]§7)"
Enchantments: [Unbreaking 1] # For glow effect

# In Progress Achievement  
Material: CLOCK
Display Name: "§e[Achievement Name]"
Lore:
  - "§7[Description]"
  - ""
  - "§7Progress: §e[XX%] §7(§f[X/Y]§7)"
  - "§7Reward: §f[Reward Description]"
  - ""
  - "§eKeep going!"

# Locked Achievement
Material: BARRIER
Display Name: "§c???"
Lore:
  - "§7This achievement is locked."
  - "§7Complete prerequisites to unlock."
```

## Admin Menus

### Admin Hub (`/udx admin`)

**Layout**: 54 slots
**Access**: Requires `udx.admin` permission

```
┌─────────────────────────────────────────────────────┐
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [D] [?] [I] [?] [C] [?] [S] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [A] [?] [P] [?] [U] [?] [B] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [?] [?] [?] [X] [?] [?] [?] [?]                 │
└─────────────────────────────────────────────────────┘

D = Dungeons Management
I = Instance Management  
C = Content Editors
S = System Settings
A = Analytics
P = Performance Monitor
U = User Management
B = Backup & Maintenance
```

### Dungeon Management

**Layout**: 54 slots with actions
**Purpose**: CRUD operations for dungeons

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [?] [N] [?] [I] [?] [?] [?]                 │
│ [D] [D] [D] [D] [D] [D] [D] [D] [D]                 │
│ [D] [D] [D] [D] [D] [D] [D] [D] [D]                 │
│ [D] [D] [D] [D] [D] [D] [D] [D] [D]                 │
│ [D] [D] [D] [D] [D] [D] [D] [D] [D]                 │
│ [<] [?] [?] [?] [?] [?] [?] [?] [>]                 │
└─────────────────────────────────────────────────────┘

N = New Dungeon
I = Import Dungeon
D = Dungeon Items
```

**Dungeon Admin Cards**:
```yaml
Material: [Theme appropriate]
Display Name: "§6[Dungeon Name]"
Lore:
  - "§7ID: §f[dungeon_id]"
  - "§7Status: §a[Active/Disabled]"
  - "§7Rooms: §f[X] §7| Mobs: §f[Y] §7| Bosses: §f[Z]"
  - ""
  - "§7Active Instances: §f[X]"
  - "§7Total Runs: §f[XXX]"
  - "§7Success Rate: §f[XX%]"
  - ""
  - "§eLeft Click: §7Edit"
  - "§eRight Click: §7Actions Menu"
  - "§eShift+Click: §7Quick Disable"
```

### Instance Management

**Layout**: 54 slots with real-time data
**Update Frequency**: Every 20 ticks (1 second)

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [?] [R] [?] [F] [?] [?] [?]                 │
│ [I] [I] [I] [I] [I] [I] [I] [I] [I]                 │
│ [I] [I] [I] [I] [I] [I] [I] [I] [I]                 │
│ [I] [I] [I] [I] [I] [I] [I] [I] [I]                 │
│ [I] [I] [I] [I] [I] [I] [I] [I] [I]                 │
│ [<] [?] [?] [S] [?] [?] [?] [?] [>]                 │
└─────────────────────────────────────────────────────┘

R = Refresh
F = Filter Options
I = Instance Items
S = Statistics Summary
```

**Instance Display**:
```yaml
Material: END_PORTAL_FRAME
Display Name: "§b[Dungeon Name] §7([Instance ID])"
Lore:
  - "§7State: §a[RUNNING/CREATING/etc]"
  - "§7Players: §f[X/Y] §7([Player Names])"
  - "§7Duration: §f[MM:SS]"
  - "§7World: §f[world_name]"
  - ""
  - "§7Performance:"
  - "§7• TPS: §f[XX.X]"
  - "§7• Entities: §f[XX/100]"
  - "§7• Memory: §f[XX MB]"
  - ""
  - "§eLeft Click: §7Teleport"
  - "§eRight Click: §7Actions"
  - "§cShift+Right Click: §7Force Stop"
```

### Content Editors

#### Room Editor

**Layout**: 54 slots with tools
**Purpose**: Create and edit room templates

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [?] [T] [?] [S] [?] [?] [?]                 │
│ [?] [W] [?] [C] [?] [M] [?] [P] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [?] [R] [R] [R] [R] [R] [R] [R] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [N] [E] [D] [?] [?] [?] [?] [?] [?]                 │
└─────────────────────────────────────────────────────┘

T = Selection Tool (Golden Axe)
S = Save Room
W = Wand (Position Marker)
C = Connector Tool
M = Marker Tool  
P = Preview
R = Room List
N = New Room
E = Export
D = Delete
```

**Tools Provided**:
- **Selection Wand**: Golden Axe for marking room boundaries
- **Connector Tool**: Mark connection points between rooms
- **Marker Tool**: Place special markers (spawns, chests, etc.)
- **Preview Tool**: 3D visualization of room layout

#### Mob Editor

**Layout**: 54 slots with mob configuration
**Purpose**: Create and edit custom mobs

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [?] [N] [?] [I] [?] [T] [?]                 │
│ [M] [M] [M] [M] [M] [M] [M] [M] [M]                 │
│ [M] [M] [M] [M] [M] [M] [M] [M] [M]                 │
│ [M] [M] [M] [M] [M] [M] [M] [M] [M]                 │
│ [M] [M] [M] [M] [M] [M] [M] [M] [M]                 │
│ [<] [?] [C] [?] [S] [?] [D] [?] [>]                 │
└─────────────────────────────────────────────────────┘

N = New Mob
I = Import Mob
T = Test Spawn
M = Mob Items
C = Clone
S = Save
D = Delete
```

**Mob Configuration Sections**:
1. **Basic Info**: Name, type, category, difficulty
2. **Attributes**: Health, damage, speed, armor
3. **Equipment**: Weapons, armor, drop chances
4. **Behaviors**: AI patterns, movement, targeting
5. **Abilities**: Special attacks, cooldowns, effects
6. **Drops**: Loot tables, experience, currency

#### Boss Editor

**Layout**: 54 slots with phase management
**Purpose**: Create multi-phase boss encounters

```
┌─────────────────────────────────────────────────────┐
│ [B] [?] [?] [N] [?] [T] [?] [?] [?]                 │
│ [?] [I] [?] [P] [?] [A] [?] [E] [?]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [P] [P] [P] [P] [P] [P] [P] [P] [P]                 │
│ [?] [?] [?] [?] [?] [?] [?] [?] [?]                 │
│ [C] [S] [D] [?] [?] [?] [?] [?] [?]                 │
└─────────────────────────────────────────────────────┘

N = New Boss
T = Test Spawn
I = Boss Info
P = Phase Editor
A = Ability Editor  
E = Environment Editor
C = Clone
S = Save
D = Delete
```

**Phase Editor Features**:
- Visual timeline of abilities
- Health threshold triggers
- Environmental changes
- Minion spawn waves
- Telegraph configuration

## GUI Animations and Effects

### Animation Types

1. **Slot Animations**:
   - Pulsing glow for important items
   - Color cycling for status indicators
   - Smooth transitions between states

2. **Page Transitions**:
   - Slide effects when changing pages
   - Fade in/out for menu switches
   - Loading animations for async operations

3. **Interactive Feedback**:
   - Click particles and sounds
   - Hover highlighting
   - Success/error visual feedback

### Sound Design

**Sound Categories**:
- **Navigation**: UI_BUTTON_CLICK, UI_TOAST_IN, UI_TOAST_OUT
- **Success**: ENTITY_PLAYER_LEVELUP, BLOCK_NOTE_BLOCK_PLING
- **Error**: BLOCK_NOTE_BLOCK_BASS, ENTITY_VILLAGER_NO
- **Special**: BLOCK_ENCHANTMENT_TABLE_USE, ENTITY_EXPERIENCE_ORB_PICKUP

### Particle Effects

**Usage Guidelines**:
- Subtle effects that don't obstruct vision
- Performance-conscious (limited particles)
- Contextual to the action being performed

```java
// Example: Success feedback
player.spawnParticle(Particle.VILLAGER_HAPPY, 
    player.getLocation().add(0, 2, 0), 5, 0.5, 0.5, 0.5, 0);
player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 1.2f);
```

## Accessibility Features

### Color-Blind Support

- Text-based status indicators alongside colors
- High contrast options
- Pattern-based differentiation (stripes, dots, etc.)

### Internationalization

- Configurable language files
- Unicode support for special characters
- RTL language support where applicable

### Performance Considerations

- Configurable update frequencies
- Reduced animation modes
- Simplified layouts for lower-end clients

## Customization Options

### Admin Customization

```yaml
# gui_config.yml
menus:
  main_menu:
    layout: "default"  # default, compact, minimal
    update_interval: 20
    animations_enabled: true
    
  dungeon_browser:
    items_per_page: 45
    sort_order: "difficulty"  # name, difficulty, popularity
    show_locked: false
    
  admin_hub:
    quick_actions_enabled: true
    confirmation_dialogs: true
    debug_info_visible: true
```

### Player Preferences

Players can customize their GUI experience:
- Animation speed (off, slow, normal, fast)
- Sound effects (on/off, volume)
- Notification preferences
- Color themes (where applicable)

## Technical Implementation

### Menu Framework

```java
public abstract class Menu {
    protected final Player viewer;
    protected final Inventory inventory;
    protected final Map<Integer, ClickHandler> clickHandlers;
    
    public abstract void render();
    public abstract void handleClick(InventoryClickEvent event);
    
    protected void setItem(int slot, ItemStack item, ClickHandler handler) {
        inventory.setItem(slot, item);
        if (handler != null) {
            clickHandlers.put(slot, handler);
        }
    }
}

public class PagedMenu extends Menu {
    protected int currentPage = 0;
    protected int itemsPerPage = 45;
    
    public void nextPage() { /* Implementation */ }
    public void previousPage() { /* Implementation */ }
    protected abstract List<ItemStack> getPageItems(int page);
}
```

### Update System

```java
public class MenuUpdateTask implements Runnable {
    private final Set<Menu> activeMenus = ConcurrentHashMap.newKeySet();
    
    @Override
    public void run() {
        for (Menu menu : activeMenus) {
            if (menu.needsUpdate()) {
                menu.render();
            }
        }
    }
}
```

### Click Handling

```java
@EventHandler
public void onInventoryClick(InventoryClickEvent event) {
    if (!(event.getWhoClicked() instanceof Player player)) return;
    
    Menu menu = getPlayerMenu(player);
    if (menu != null) {
        event.setCancelled(true);
        menu.handleClick(event);
    }
}
```

## Best Practices

### Performance

1. **Lazy Loading**: Only load data when menus are opened
2. **Caching**: Cache frequently accessed data
3. **Batch Updates**: Group multiple changes together
4. **Async Operations**: Use CompletableFuture for database calls

### User Experience

1. **Consistent Navigation**: Same button positions across menus
2. **Clear Feedback**: Visual and audio confirmation of actions
3. **Error Handling**: Graceful degradation and helpful error messages
4. **Responsive Design**: Adapt to different screen sizes and client capabilities

### Security

1. **Permission Checks**: Verify permissions before showing sensitive options
2. **Input Validation**: Sanitize all user inputs
3. **Rate Limiting**: Prevent spam clicking and abuse
4. **Audit Logging**: Log important administrative actions

---

*This GUI system provides a comprehensive, user-friendly interface that makes UltimateDungeonX accessible to players of all skill levels while providing powerful tools for administrators.*
