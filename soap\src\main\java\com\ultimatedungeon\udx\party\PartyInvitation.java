package com.ultimatedungeon.udx.party;

import org.jetbrains.annotations.NotNull;

import java.util.UUID;

/**
 * Represents a party invitation.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record PartyInvitation(
    @NotNull UUID invitationId,
    @NotNull UUID partyId,
    @NotNull UUID inviter,
    @NotNull UUID invitee,
    long createdTime,
    long expiresAt
) {
    
    /**
     * Creates a new party invitation.
     * 
     * @param partyId the party ID
     * @param inviter the inviter's UUID
     * @param invitee the invitee's UUID
     * @param expirationTimeMs expiration time in milliseconds
     * @return new party invitation
     */
    @NotNull
    public static PartyInvitation create(@NotNull UUID partyId, @NotNull UUID inviter, 
                                       @NotNull UUID invitee, long expirationTimeMs) {
        long now = System.currentTimeMillis();
        return new PartyInvitation(
            UUID.randomUUID(),
            partyId,
            inviter,
            invitee,
            now,
            now + expirationTimeMs
        );
    }
    
    /**
     * Checks if this invitation has expired.
     * 
     * @return true if expired
     */
    public boolean isExpired() {
        return System.currentTimeMillis() > expiresAt;
    }
    
    /**
     * Gets the remaining time in milliseconds.
     * 
     * @return remaining time, or 0 if expired
     */
    public long getRemainingTime() {
        long remaining = expiresAt - System.currentTimeMillis();
        return Math.max(0, remaining);
    }
}
