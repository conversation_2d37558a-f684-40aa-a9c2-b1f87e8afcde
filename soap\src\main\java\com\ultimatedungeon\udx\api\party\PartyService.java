package com.ultimatedungeon.udx.api.party;

import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing parties and matchmaking.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public interface PartyService {
    
    /**
     * Creates a new party with the specified leader.
     * 
     * @param leader The party leader
     * @return The created party, or null if creation failed
     */
    @Nullable
    PartyData createParty(@NotNull Player leader);
    
    /**
     * Gets the party a player is currently in.
     * 
     * @param player The player
     * @return The party data, or null if not in a party
     */
    @Nullable
    PartyData getPlayerParty(@NotNull Player player);
    
    /**
     * Gets a party by ID.
     * 
     * @param partyId The party ID
     * @return The party data, or null if not found
     */
    @Nullable
    PartyData getParty(@NotNull UUID partyId);
    
    /**
     * Invites a player to a party.
     * 
     * @param partyId The party ID
     * @param inviter The player sending the invite
     * @param invitee The player being invited
     * @return True if the invite was sent successfully
     */
    boolean invitePlayer(@NotNull UUID partyId, @NotNull Player inviter, @NotNull Player invitee);
    
    /**
     * Accepts a party invitation.
     * 
     * @param player The player accepting the invite
     * @param partyId The party ID
     * @return True if the invite was accepted successfully
     */
    boolean acceptInvite(@NotNull Player player, @NotNull UUID partyId);
    
    /**
     * Declines a party invitation.
     * 
     * @param player The player declining the invite
     * @param partyId The party ID
     * @return True if the invite was declined successfully
     */
    boolean declineInvite(@NotNull Player player, @NotNull UUID partyId);
    
    /**
     * Removes a player from a party.
     * 
     * @param partyId The party ID
     * @param player The player to remove
     * @param kicker The player initiating the removal (must be leader)
     * @return True if the player was removed successfully
     */
    boolean removePlayer(@NotNull UUID partyId, @NotNull Player player, @NotNull Player kicker);
    
    /**
     * Leaves a party.
     * 
     * @param player The player leaving
     * @return True if the player left successfully
     */
    boolean leaveParty(@NotNull Player player);
    
    /**
     * Disbands a party.
     * 
     * @param partyId The party ID
     * @param leader The party leader
     * @return True if the party was disbanded successfully
     */
    boolean disbandParty(@NotNull UUID partyId, @NotNull Player leader);
    
    /**
     * Promotes a player to party leader.
     * 
     * @param partyId The party ID
     * @param currentLeader The current party leader
     * @param newLeader The player to promote
     * @return True if the promotion was successful
     */
    boolean promoteToLeader(@NotNull UUID partyId, @NotNull Player currentLeader, @NotNull Player newLeader);
    
    /**
     * Queues a party for matchmaking.
     * 
     * @param partyId The party ID
     * @param dungeonId The dungeon to queue for
     * @param difficulty The difficulty tier
     * @return True if the party was queued successfully
     */
    boolean queueForDungeon(@NotNull UUID partyId, @NotNull String dungeonId, @NotNull String difficulty);
    
    /**
     * Removes a party from the matchmaking queue.
     * 
     * @param partyId The party ID
     * @return True if the party was removed from queue successfully
     */
    boolean leaveQueue(@NotNull UUID partyId);
    
    /**
     * Gets all parties currently in the matchmaking queue.
     * 
     * @return List of queued parties
     */
    @NotNull
    List<PartyData> getQueuedParties();
    
    /**
     * Checks if a party is currently queued for matchmaking.
     * 
     * @param partyId The party ID
     * @return True if the party is queued
     */
    boolean isPartyQueued(@NotNull UUID partyId);
    
    /**
     * Gets the queue data for a party.
     * 
     * @param partyId The party ID
     * @return The queue data, or null if not queued
     */
    @Nullable
    QueueData getPartyQueueData(@NotNull UUID partyId);
}
