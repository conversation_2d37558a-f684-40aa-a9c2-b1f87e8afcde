package com.ultimatedungeon.udx.gui;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.function.Consumer;

/**
 * Base class for all GUI menus in UltimateDungeonX.
 * 
 * <p>This class provides a foundation for creating interactive inventory-based
 * menus with click handlers, animations, and navigation features.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public abstract class Menu implements InventoryHolder {
    
    protected final Player player;
    protected final Component title;
    protected final int size;
    protected final Inventory inventory;
    protected final Map<Integer, ClickHandler> clickHandlers;
    protected final Map<Integer, AnimatedItem> animatedItems;
    protected final MenuRegistry menuRegistry;
    
    private Menu parent;
    private boolean soundsEnabled = true;
    private boolean animationsEnabled = true;
    
    /**
     * Creates a new menu.
     * 
     * @param player the player viewing the menu
     * @param title the menu title
     * @param size the menu size (must be multiple of 9)
     * @param menuRegistry the menu registry
     */
    protected Menu(@NotNull Player player, @NotNull Component title, int size, @NotNull MenuRegistry menuRegistry) {
        this.player = player;
        this.title = title;
        this.size = size;
        this.menuRegistry = menuRegistry;
        this.inventory = Bukkit.createInventory(this, size, title);
        this.clickHandlers = new HashMap<>();
        this.animatedItems = new HashMap<>();
        
        // Load player preferences
        loadPlayerPreferences();
    }
    
    /**
     * Loads player preferences for sounds and animations.
     */
    private void loadPlayerPreferences() {
        // TODO: Load from player profile settings
        // For now, use default values
        this.soundsEnabled = true;
        this.animationsEnabled = true;
    }
    
    /**
     * Opens the menu for the player.
     */
    public void open() {
        setupMenu();
        player.openInventory(inventory);
        startAnimations();
        onOpen();
    }
    
    /**
     * Closes the menu.
     */
    public void close() {
        stopAnimations();
        player.closeInventory();
        onClose();
    }
    
    /**
     * Refreshes the menu content.
     */
    public void refresh() {
        inventory.clear();
        clickHandlers.clear();
        setupMenu();
        onRefresh();
    }
    
    /**
     * Sets up the menu content. Called when opening or refreshing.
     */
    protected abstract void setupMenu();
    
    /**
     * Called when the menu is opened.
     */
    protected void onOpen() {
        playSound(Sound.UI_BUTTON_CLICK, 0.5f, 1.0f);
    }
    
    /**
     * Called when the menu is closed.
     */
    protected void onClose() {
        // Override in subclasses if needed
    }
    
    /**
     * Called when the menu is refreshed.
     */
    protected void onRefresh() {
        // Override in subclasses if needed
    }
    
    /**
     * Handles inventory click events.
     * Note: Event is already cancelled by MenuRegistry, so we don't need to cancel it again.
     */
    public void handleClick(@NotNull InventoryClickEvent event) {
        int slot = event.getRawSlot();
        ClickType clickType = event.getClick();
        
        ClickHandler handler = clickHandlers.get(slot);
        if (handler != null) {
            try {
                handler.handle(clickType);
                playSound(Sound.UI_BUTTON_CLICK, 0.3f, 1.2f);
            } catch (Exception e) {
                menuRegistry.getPlugin().getLogger().severe("Error handling menu click: " + e.getMessage());
                e.printStackTrace();
                
                player.sendMessage(Component.text("An error occurred while processing your click.")
                    .color(NamedTextColor.RED));
            }
        }
    }
    
    /**
     * Sets an item in the menu with a click handler.
     */
    protected void setItem(int slot, @NotNull ItemStack item, @Nullable ClickHandler handler) {
        inventory.setItem(slot, item);
        if (handler != null) {
            clickHandlers.put(slot, handler);
        }
    }
    
    /**
     * Sets an item in the menu without a click handler.
     */
    protected void setItem(int slot, @NotNull ItemStack item) {
        setItem(slot, item, null);
    }
    
    /**
     * Creates an item stack with the given material and display name.
     */
    @NotNull
    protected ItemStack createItem(@NotNull Material material, @NotNull Component displayName) {
        return createItem(material, displayName, null);
    }
    
    /**
     * Creates an item stack with the given material, display name, and lore.
     */
    @NotNull
    protected ItemStack createItem(@NotNull Material material, @NotNull Component displayName, @Nullable List<Component> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.displayName(displayName);
            if (lore != null && !lore.isEmpty()) {
                meta.lore(lore);
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Creates a simple lore list from strings.
     */
    @NotNull
    protected List<Component> createLore(@NotNull String... lines) {
        List<Component> lore = new ArrayList<>();
        for (String line : lines) {
            lore.add(Component.text(line).color(NamedTextColor.GRAY));
        }
        return lore;
    }
    
    /**
     * Creates a back button that returns to the parent menu.
     */
    @NotNull
    protected ItemStack createBackButton() {
        return createItem(
            Material.ARROW,
            Component.text("← Back").color(NamedTextColor.YELLOW),
            createLore("Click to go back")
        );
    }
    
    /**
     * Creates a close button that closes the menu.
     */
    @NotNull
    protected ItemStack createCloseButton() {
        return createItem(
            Material.BARRIER,
            Component.text("✕ Close").color(NamedTextColor.RED),
            createLore("Click to close")
        );
    }
    
    /**
     * Creates a refresh button that refreshes the menu.
     */
    @NotNull
    protected ItemStack createRefreshButton() {
        return createItem(
            Material.LIME_DYE,
            Component.text("↻ Refresh").color(NamedTextColor.GREEN),
            createLore("Click to refresh")
        );
    }
    
    /**
     * Creates a filler item for empty slots.
     */
    @NotNull
    protected ItemStack createFiller() {
        ItemStack filler = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = filler.getItemMeta();
        if (meta != null) {
            meta.displayName(Component.empty());
            filler.setItemMeta(meta);
        }
        return filler;
    }
    
    /**
     * Fills empty slots with filler items.
     */
    protected void fillEmptySlots() {
        ItemStack filler = createFiller();
        for (int i = 0; i < size; i++) {
            if (inventory.getItem(i) == null) {
                setItem(i, filler);
            }
        }
    }
    
    /**
     * Sets up navigation buttons (back, close, refresh).
     */
    protected void setupNavigation() {
        // Back button (bottom left)
        if (parent != null) {
            setItem(size - 9, createBackButton(), clickType -> {
                if (clickType == ClickType.LEFT) {
                    parent.open();
                }
            });
        }
        
        // Close button (bottom right)
        setItem(size - 1, createCloseButton(), clickType -> {
            if (clickType == ClickType.LEFT) {
                close();
            }
        });
        
        // Refresh button (bottom center)
        setItem(size - 5, createRefreshButton(), clickType -> {
            if (clickType == ClickType.LEFT) {
                refresh();
            }
        });
    }
    
    /**
     * Plays a sound for the player if sounds are enabled.
     */
    protected void playSound(@NotNull Sound sound, float volume, float pitch) {
        if (soundsEnabled) {
            player.playSound(player.getLocation(), sound, volume, pitch);
        }
    }
    
    /**
     * Shows a confirmation dialog.
     */
    protected void showConfirmation(@NotNull Component message, @NotNull Runnable onConfirm) {
        ConfirmationMenu confirmation = new ConfirmationMenu(player, message, onConfirm, this, menuRegistry);
        confirmation.open();
    }
    
    /**
     * Opens a child menu with this menu as parent.
     */
    protected void openChild(@NotNull Menu child) {
        child.setParent(this);
        child.open();
    }
    
    // Getters and setters
    
    @NotNull
    public Player getPlayer() {
        return player;
    }
    
    @NotNull
    public Component getTitle() {
        return title;
    }
    
    public int getSize() {
        return size;
    }
    
    @Override
    @NotNull
    public Inventory getInventory() {
        return inventory;
    }
    
    @Nullable
    public Menu getParent() {
        return parent;
    }
    
    public void setParent(@Nullable Menu parent) {
        this.parent = parent;
    }
    
    public boolean isSoundsEnabled() {
        return soundsEnabled;
    }
    
    public void setSoundsEnabled(boolean soundsEnabled) {
        this.soundsEnabled = soundsEnabled;
    }
    
    public boolean isAnimationsEnabled() {
        return animationsEnabled;
    }
    
    public void setAnimationsEnabled(boolean animationsEnabled) {
        this.animationsEnabled = animationsEnabled;
    }
    
    @NotNull
    public MenuRegistry getMenuRegistry() {
        return menuRegistry;
    }
    
    /**
     * Sets an animated item that cycles through multiple ItemStacks.
     */
    protected void setAnimatedItem(int slot, @NotNull List<ItemStack> frames, int interval, @Nullable ClickHandler handler) {
        if (!animationsEnabled || frames.isEmpty()) {
            // If animations disabled or no frames, just set the first frame
            if (!frames.isEmpty()) {
                setItem(slot, frames.get(0), handler);
            }
            return;
        }
        
        AnimatedItem animatedItem = new AnimatedItem(frames, interval);
        animatedItems.put(slot, animatedItem);
        
        // Set initial frame
        inventory.setItem(slot, frames.get(0));
        
        if (handler != null) {
            clickHandlers.put(slot, handler);
        }
        
        // Start animation if menu is open
        if (player.getOpenInventory().getTopInventory().equals(inventory)) {
            animatedItem.start(slot);
        }
    }
    
    /**
     * Starts all animations for this menu.
     */
    protected void startAnimations() {
        if (!animationsEnabled) return;
        
        for (Map.Entry<Integer, AnimatedItem> entry : animatedItems.entrySet()) {
            int slot = entry.getKey();
            AnimatedItem animatedItem = entry.getValue();
            animatedItem.start(slot);
        }
    }
    
    /**
     * Stops all animations for this menu.
     */
    protected void stopAnimations() {
        for (AnimatedItem animatedItem : animatedItems.values()) {
            animatedItem.stop();
        }
    }
    
    /**
     * Functional interface for handling menu clicks.
     */
    @FunctionalInterface
    public interface ClickHandler {
        void handle(@NotNull ClickType clickType);
    }
    
    /**
     * Represents an animated item that cycles through frames.
     */
    private class AnimatedItem {
        private final List<ItemStack> frames;
        private final int interval;
        private int currentFrame = 0;
        private BukkitTask task;
        
        public AnimatedItem(@NotNull List<ItemStack> frames, int interval) {
            this.frames = new ArrayList<>(frames);
            this.interval = Math.max(1, interval); // Minimum 1 tick
        }
        
        public void start(int slot) {
            if (frames.isEmpty() || !animationsEnabled) return;
            
            // Stop existing animation
            stop();
            
            task = ((UltimateDungeonX) menuRegistry.getPlugin()).getSchedulerUtil().runTaskTimer(() -> {
                // Check if player still has this menu open
                if (!player.getOpenInventory().getTopInventory().equals(inventory)) {
                    stop();
                    return;
                }
                
                // Cycle to next frame
                currentFrame = (currentFrame + 1) % frames.size();
                inventory.setItem(slot, frames.get(currentFrame));
            }, interval, interval);
        }
        
        public void stop() {
            if (task != null && !task.isCancelled()) {
                task.cancel();
                task = null;
            }
        }
    }
}
