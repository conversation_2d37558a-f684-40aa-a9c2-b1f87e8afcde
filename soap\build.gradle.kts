plugins {
    java
    id("com.gradleup.shadow") version "8.3.0"
    id("xyz.jpenilla.run-paper") version "2.2.2"
}

group = "com.ultimatedungeon"
version = "1.0.0"
description = "UltimateDungeonX - No-Dependency, GUI-First Dungeon Plugin"

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
    maven("https://repo.papermc.io/repository/maven-public/")
    maven("https://oss.sonatype.org/content/groups/public/")
    maven("https://repo.extendedclip.com/content/repositories/placeholderapi/")
    maven("https://jitpack.io")
    maven("https://nexus.hc.to/content/repositories/pub_releases/") // Vault repository
}

dependencies {
    // Paper API
    compileOnly("io.papermc.paper:paper-api:1.21.3-R0.1-SNAPSHOT")
    
    // Adventure (included in Paper but explicit for clarity)
    compileOnly("net.kyori:adventure-api:4.14.0")
    compileOnly("net.kyori:adventure-text-minimessage:4.14.0")
    
    // Annotations
    compileOnly("org.jetbrains:annotations:24.1.0")
    
    // Optional soft dependencies (commented out for now - can be added later)
    // compileOnly("net.milkbowl.vault:VaultAPI:1.7.1") {
    //     exclude(group = "org.bukkit", module = "bukkit")
    // }
    compileOnly("me.clip:placeholderapi:2.11.5")
    
    // SQLite (embedded, no external dependency)
    implementation("org.xerial:sqlite-jdbc:********")
    
    // JSON processing (lightweight)
    implementation("com.google.code.gson:gson:2.10.1")
    
    // Testing
    testImplementation("org.junit.jupiter:junit-jupiter:5.10.1")
    testImplementation("org.mockito:mockito-core:5.8.0")
}

tasks {
    compileJava {
        options.encoding = Charsets.UTF_8.name()
        options.release.set(21)
        options.compilerArgs.addAll(listOf("-Xlint:all", "-Xlint:-processing"))
    }
    
    javadoc {
        options.encoding = Charsets.UTF_8.name()
    }
    
    processResources {
        filteringCharset = Charsets.UTF_8.name()
        val props = mapOf(
            "name" to project.name,
            "version" to project.version,
            "description" to project.description,
            "apiVersion" to "1.21"
        )
        inputs.properties(props)
        filesMatching("plugin.yml") {
            expand(props)
        }
    }
    
    shadowJar {
        archiveBaseName.set("UltimateDungeonX")
        archiveClassifier.set("")
        
        // Relocate dependencies to avoid conflicts
        relocate("org.xerial", "com.ultimatedungeon.udx.libs.xerial")
        relocate("com.google.gson", "com.ultimatedungeon.udx.libs.gson")
        
        // Copy to releases directory
        doLast {
            copy {
                from(archiveFile)
                into("${rootProject.projectDir}/releases")
            }
        }
    }
    
    build {
        dependsOn(shadowJar)
    }
    
    runServer {
        minecraftVersion("1.21.3")
        jvmArgs("-Xmx2G", "-Xms1G")
    }
    
    test {
        useJUnitPlatform()
    }
}

// Create releases directory
tasks.register("createReleaseDir") {
    doLast {
        file("${rootProject.projectDir}/releases").mkdirs()
    }
}

tasks.shadowJar {
    dependsOn("createReleaseDir")
}
