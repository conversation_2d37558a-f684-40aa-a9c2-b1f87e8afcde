package com.ultimatedungeon.udx.gen;

import com.ultimatedungeon.udx.room.Connector;
import com.ultimatedungeon.udx.room.RoomTemplate;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Represents a complete generated dungeon layout.
 * 
 * <p>This class contains all rooms and their connections in a generated
 * dungeon, providing methods to query the layout structure and properties.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class DungeonLayout {
    
    private final String dungeonId;
    private final GenerationConstraints constraints;
    private final Map<Integer, RoomLayout> rooms;
    private final RoomLayout entranceRoom;
    private final Set<RoomLayout> exitRooms;
    private final long generationTime;
    private final Map<String, Object> metadata;
    
    /**
     * Creates a new dungeon layout.
     * 
     * @param dungeonId the dungeon ID
     * @param constraints the generation constraints used
     * @param rooms map of room ID to room layout
     * @param entranceRoom the entrance room
     * @param exitRooms set of exit rooms
     * @param generationTime time taken to generate (milliseconds)
     */
    public DungeonLayout(
        @NotNull String dungeonId,
        @NotNull GenerationConstraints constraints,
        @NotNull Map<Integer, RoomLayout> rooms,
        @NotNull RoomLayout entranceRoom,
        @NotNull Set<RoomLayout> exitRooms,
        long generationTime
    ) {
        this.dungeonId = dungeonId;
        this.constraints = constraints;
        this.rooms = Map.copyOf(rooms);
        this.entranceRoom = entranceRoom;
        this.exitRooms = Set.copyOf(exitRooms);
        this.generationTime = generationTime;
        this.metadata = new HashMap<>();
    }
    
    /**
     * Gets the dungeon ID.
     * 
     * @return the dungeon ID
     */
    @NotNull
    public String getDungeonId() {
        return dungeonId;
    }
    
    /**
     * Gets the generation constraints.
     * 
     * @return the constraints
     */
    @NotNull
    public GenerationConstraints getConstraints() {
        return constraints;
    }
    
    /**
     * Gets all rooms in the layout.
     * 
     * @return map of room ID to room layout
     */
    @NotNull
    public Map<Integer, RoomLayout> getRooms() {
        return rooms;
    }
    
    /**
     * Gets a room by ID.
     * 
     * @param roomId the room ID
     * @return the room layout, or null if not found
     */
    @Nullable
    public RoomLayout getRoom(int roomId) {
        return rooms.get(roomId);
    }
    
    /**
     * Gets the entrance room.
     * 
     * @return the entrance room
     */
    @NotNull
    public RoomLayout getEntranceRoom() {
        return entranceRoom;
    }
    
    /**
     * Gets all exit rooms.
     * 
     * @return set of exit rooms
     */
    @NotNull
    public Set<RoomLayout> getExitRooms() {
        return exitRooms;
    }
    
    /**
     * Gets the generation time.
     * 
     * @return generation time in milliseconds
     */
    public long getGenerationTime() {
        return generationTime;
    }
    
    /**
     * Gets the total number of rooms.
     * 
     * @return room count
     */
    public int getRoomCount() {
        return rooms.size();
    }
    
    /**
     * Gets the maximum depth of the dungeon.
     * 
     * @return maximum depth
     */
    public int getMaxDepth() {
        return rooms.values().stream()
            .mapToInt(RoomLayout::getDepth)
            .max()
            .orElse(0);
    }
    
    /**
     * Gets rooms at a specific depth.
     * 
     * @param depth the depth
     * @return list of rooms at that depth
     */
    @NotNull
    public List<RoomLayout> getRoomsAtDepth(int depth) {
        return rooms.values().stream()
            .filter(room -> room.getDepth() == depth)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets all dead-end rooms.
     * 
     * @return list of dead-end rooms
     */
    @NotNull
    public List<RoomLayout> getDeadEndRooms() {
        return rooms.values().stream()
            .filter(RoomLayout::isDeadEnd)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets rooms by template type.
     * 
     * @param roomType the room type
     * @return list of rooms of that type
     */
    @NotNull
    public List<RoomLayout> getRoomsByType(@NotNull RoomTemplate.RoomType roomType) {
        return rooms.values().stream()
            .filter(room -> room.getTemplate().getRoomType() == roomType)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets rooms containing a specific tag.
     * 
     * @param tag the tag
     * @return list of rooms with that tag
     */
    @NotNull
    public List<RoomLayout> getRoomsWithTag(@NotNull String tag) {
        return rooms.values().stream()
            .filter(room -> room.getTemplate().getTags().contains(tag))
            .collect(Collectors.toList());
    }
    
    /**
     * Calculates the bounding box of the entire dungeon.
     * 
     * @return bounding box [minX, minY, minZ, maxX, maxY, maxZ]
     */
    @NotNull
    public int[] getBoundingBox() {
        if (rooms.isEmpty()) {
            return new int[]{0, 0, 0, 0, 0, 0};
        }
        
        int minX = Integer.MAX_VALUE;
        int minY = Integer.MAX_VALUE;
        int minZ = Integer.MAX_VALUE;
        int maxX = Integer.MIN_VALUE;
        int maxY = Integer.MIN_VALUE;
        int maxZ = Integer.MIN_VALUE;
        
        for (RoomLayout room : rooms.values()) {
            int[] bounds = room.getBoundingBox();
            minX = Math.min(minX, bounds[0]);
            minY = Math.min(minY, bounds[1]);
            minZ = Math.min(minZ, bounds[2]);
            maxX = Math.max(maxX, bounds[3]);
            maxY = Math.max(maxY, bounds[4]);
            maxZ = Math.max(maxZ, bounds[5]);
        }
        
        return new int[]{minX, minY, minZ, maxX, maxY, maxZ};
    }
    
    /**
     * Calculates the total volume of all rooms.
     * 
     * @return total volume in blocks
     */
    public int getTotalVolume() {
        return rooms.values().stream()
            .mapToInt(room -> room.getTemplate().getVolume())
            .sum();
    }
    
    /**
     * Gets the average branching factor of the layout.
     * 
     * @return average connections per room
     */
    public double getAverageBranchingFactor() {
        if (rooms.isEmpty()) {
            return 0.0;
        }
        
        int totalConnections = rooms.values().stream()
            .mapToInt(RoomLayout::getConnectionCount)
            .sum();
        
        return (double) totalConnections / rooms.size();
    }
    
    /**
     * Finds the shortest path between two rooms.
     * 
     * @param fromId source room ID
     * @param toId target room ID
     * @return list of room IDs forming the path, or empty if no path
     */
    @NotNull
    public List<Integer> findPath(int fromId, int toId) {
        RoomLayout from = rooms.get(fromId);
        RoomLayout to = rooms.get(toId);
        
        if (from == null || to == null) {
            return Collections.emptyList();
        }
        
        // BFS to find shortest path
        Queue<RoomLayout> queue = new LinkedList<>();
        Map<RoomLayout, RoomLayout> parent = new HashMap<>();
        Set<RoomLayout> visited = new HashSet<>();
        
        queue.offer(from);
        visited.add(from);
        parent.put(from, null);
        
        while (!queue.isEmpty()) {
            RoomLayout current = queue.poll();
            
            if (current.equals(to)) {
                // Reconstruct path
                List<Integer> path = new ArrayList<>();
                RoomLayout node = current;
                while (node != null) {
                    path.add(0, node.getId());
                    node = parent.get(node);
                }
                return path;
            }
            
            for (RoomLayout neighbor : current.getConnections().values()) {
                if (!visited.contains(neighbor)) {
                    visited.add(neighbor);
                    parent.put(neighbor, current);
                    queue.offer(neighbor);
                }
            }
        }
        
        return Collections.emptyList();
    }
    
    /**
     * Validates the layout structure.
     * 
     * @return list of validation errors (empty if valid)
     */
    @NotNull
    public List<String> validate() {
        List<String> errors = new ArrayList<>();
        
        // Check entrance room exists
        if (!rooms.containsValue(entranceRoom)) {
            errors.add("Entrance room not found in layout");
        }
        
        // Check exit rooms exist
        for (RoomLayout exitRoom : exitRooms) {
            if (!rooms.containsValue(exitRoom)) {
                errors.add("Exit room not found in layout: " + exitRoom.getId());
            }
        }
        
        // Check connectivity
        Set<RoomLayout> reachable = new HashSet<>();
        Queue<RoomLayout> queue = new LinkedList<>();
        queue.offer(entranceRoom);
        reachable.add(entranceRoom);
        
        while (!queue.isEmpty()) {
            RoomLayout current = queue.poll();
            for (RoomLayout neighbor : current.getConnections().values()) {
                if (!reachable.contains(neighbor)) {
                    reachable.add(neighbor);
                    queue.offer(neighbor);
                }
            }
        }
        
        if (reachable.size() != rooms.size()) {
            errors.add("Layout has unreachable rooms: " + (rooms.size() - reachable.size()) + " unreachable");
        }
        
        // Check for overlapping rooms
        List<RoomLayout> roomList = new ArrayList<>(rooms.values());
        for (int i = 0; i < roomList.size(); i++) {
            for (int j = i + 1; j < roomList.size(); j++) {
                if (roomList.get(i).overlaps(roomList.get(j))) {
                    errors.add("Rooms overlap: " + roomList.get(i).getId() + " and " + roomList.get(j).getId());
                }
            }
        }
        
        // Check connector consistency
        for (RoomLayout room : rooms.values()) {
            for (Map.Entry<Connector.Direction, RoomLayout> entry : room.getConnections().entrySet()) {
                Connector.Direction direction = entry.getKey();
                RoomLayout connected = entry.getValue();
                
                // Check reverse connection exists
                if (!connected.getConnections().containsKey(direction.getOpposite()) ||
                    !connected.getConnections().get(direction.getOpposite()).equals(room)) {
                    errors.add("Inconsistent connection between rooms " + room.getId() + " and " + connected.getId());
                }
            }
        }
        
        return errors;
    }
    
    /**
     * Gets layout statistics.
     * 
     * @return map of statistics
     */
    @NotNull
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("roomCount", getRoomCount());
        stats.put("maxDepth", getMaxDepth());
        stats.put("totalVolume", getTotalVolume());
        stats.put("averageBranchingFactor", getAverageBranchingFactor());
        stats.put("deadEndCount", getDeadEndRooms().size());
        stats.put("exitCount", exitRooms.size());
        stats.put("generationTime", generationTime);
        
        // Room type distribution
        Map<String, Integer> typeDistribution = new HashMap<>();
        for (RoomTemplate.RoomType type : RoomTemplate.RoomType.values()) {
            typeDistribution.put(type.name(), getRoomsByType(type).size());
        }
        stats.put("roomTypeDistribution", typeDistribution);
        
        // Depth distribution
        Map<Integer, Integer> depthDistribution = new HashMap<>();
        for (int depth = 0; depth <= getMaxDepth(); depth++) {
            depthDistribution.put(depth, getRoomsAtDepth(depth).size());
        }
        stats.put("depthDistribution", depthDistribution);
        
        return stats;
    }
    
    /**
     * Gets metadata value.
     * 
     * @param key the metadata key
     * @return the value, or null if not found
     */
    @Nullable
    public Object getMetadata(@NotNull String key) {
        return metadata.get(key);
    }
    
    /**
     * Sets metadata value.
     * 
     * @param key the metadata key
     * @param value the value
     */
    public void setMetadata(@NotNull String key, @Nullable Object value) {
        if (value == null) {
            metadata.remove(key);
        } else {
            metadata.put(key, value);
        }
    }
    
    /**
     * Gets all metadata.
     * 
     * @return copy of metadata map
     */
    @NotNull
    public Map<String, Object> getAllMetadata() {
        return new HashMap<>(metadata);
    }
    
    @Override
    public String toString() {
        return "DungeonLayout{" +
               "dungeonId='" + dungeonId + '\'' +
               ", rooms=" + rooms.size() +
               ", maxDepth=" + getMaxDepth() +
               ", generationTime=" + generationTime + "ms" +
               '}';
    }
}
