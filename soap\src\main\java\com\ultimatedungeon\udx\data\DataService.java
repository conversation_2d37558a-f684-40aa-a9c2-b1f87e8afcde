package com.ultimatedungeon.udx.data;

import com.ultimatedungeon.udx.config.ConfigService;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.sql.*;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.logging.Level;

/**
 * Service for managing persistent data storage using SQLite.
 * 
 * <p>This service handles all database operations including player profiles,
 * progression data, statistics, and achievements. It uses connection pooling
 * and async operations to maintain performance.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class DataService {
    
    private static final String SCHEMA_VERSION_TABLE = "schema_version";
    private static final int CURRENT_SCHEMA_VERSION = 1;
    
    private final Plugin plugin;
    private final ConfigService configService;
    private final File databaseFile;
    private final ConcurrentMap<UUID, PlayerProfile> profileCache;
    
    private Connection connection;
    private boolean initialized = false;
    
    public DataService(@NotNull Plugin plugin, @NotNull ConfigService configService) {
        this.plugin = plugin;
        this.configService = configService;
        this.databaseFile = new File(plugin.getDataFolder(), configService.getSqliteFile());
        this.profileCache = new ConcurrentHashMap<>();
    }
    
    /**
     * Initializes the database connection and creates tables.
     */
    public void initialize() {
        try {
            // Ensure data folder exists
            if (!plugin.getDataFolder().exists()) {
                plugin.getDataFolder().mkdirs();
            }
            
            // Create database connection
            String url = "jdbc:sqlite:" + databaseFile.getAbsolutePath();
            connection = DriverManager.getConnection(url);
            
            // Enable foreign keys and WAL mode for better performance
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("PRAGMA foreign_keys = ON");
                stmt.execute("PRAGMA journal_mode = WAL");
                stmt.execute("PRAGMA synchronous = NORMAL");
                stmt.execute("PRAGMA cache_size = 10000");
                stmt.execute("PRAGMA temp_store = MEMORY");
            }
            
            // Check schema version and migrate if necessary
            int currentVersion = getSchemaVersion();
            if (currentVersion < CURRENT_SCHEMA_VERSION) {
                migrateSchema(currentVersion);
            }
            
            // Create tables if they don't exist
            createTables();
            
            initialized = true;
            plugin.getLogger().info("Database initialized successfully");
            
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to initialize database", e);
            throw new RuntimeException("Database initialization failed", e);
        }
    }
    
    /**
     * Creates all necessary database tables.
     */
    private void createTables() throws SQLException {
        // Schema version table
        executeUpdate("""
            CREATE TABLE IF NOT EXISTS schema_version (
                version INTEGER PRIMARY KEY
            )
        """);
        
        // Player profiles table
        executeUpdate("""
            CREATE TABLE IF NOT EXISTS player_profiles (
                uuid TEXT PRIMARY KEY,
                username TEXT NOT NULL,
                first_join INTEGER NOT NULL,
                last_seen INTEGER NOT NULL,
                total_playtime INTEGER DEFAULT 0,
                dungeons_completed INTEGER DEFAULT 0,
                deaths INTEGER DEFAULT 0,
                season_score INTEGER DEFAULT 0,
                settings TEXT DEFAULT '{}',
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL
            )
        """);
        
        // Dungeon progress table
        executeUpdate("""
            CREATE TABLE IF NOT EXISTS dungeon_progress (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                dungeon_id TEXT NOT NULL,
                difficulty TEXT NOT NULL,
                completed BOOLEAN DEFAULT FALSE,
                best_time INTEGER,
                completions INTEGER DEFAULT 0,
                deaths INTEGER DEFAULT 0,
                last_attempt INTEGER,
                unlocked_at INTEGER NOT NULL,
                FOREIGN KEY (player_uuid) REFERENCES player_profiles(uuid) ON DELETE CASCADE,
                UNIQUE(player_uuid, dungeon_id, difficulty)
            )
        """);
        
        // Achievements table
        executeUpdate("""
            CREATE TABLE IF NOT EXISTS achievements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                achievement_id TEXT NOT NULL,
                unlocked_at INTEGER NOT NULL,
                progress INTEGER DEFAULT 0,
                FOREIGN KEY (player_uuid) REFERENCES player_profiles(uuid) ON DELETE CASCADE,
                UNIQUE(player_uuid, achievement_id)
            )
        """);
        
        // Statistics table
        executeUpdate("""
            CREATE TABLE IF NOT EXISTS statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                stat_name TEXT NOT NULL,
                stat_value INTEGER DEFAULT 0,
                last_updated INTEGER NOT NULL,
                FOREIGN KEY (player_uuid) REFERENCES player_profiles(uuid) ON DELETE CASCADE,
                UNIQUE(player_uuid, stat_name)
            )
        """);
        
        // Party history table
        executeUpdate("""
            CREATE TABLE IF NOT EXISTS party_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                party_id TEXT NOT NULL,
                player_uuid TEXT NOT NULL,
                dungeon_id TEXT NOT NULL,
                difficulty TEXT NOT NULL,
                completed BOOLEAN DEFAULT FALSE,
                completion_time INTEGER,
                started_at INTEGER NOT NULL,
                ended_at INTEGER,
                FOREIGN KEY (player_uuid) REFERENCES player_profiles(uuid) ON DELETE CASCADE
            )
        """);
        
        // Leaderboards table
        executeUpdate("""
            CREATE TABLE IF NOT EXISTS leaderboards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                dungeon_id TEXT NOT NULL,
                difficulty TEXT NOT NULL,
                completion_time INTEGER NOT NULL,
                party_size INTEGER NOT NULL,
                season INTEGER NOT NULL,
                completed_at INTEGER NOT NULL,
                FOREIGN KEY (player_uuid) REFERENCES player_profiles(uuid) ON DELETE CASCADE
            )
        """);
        
        // Create indexes for better performance
        createIndexes();
        
        // Set schema version
        setSchemaVersion(CURRENT_SCHEMA_VERSION);
    }
    
    /**
     * Creates database indexes for better query performance.
     */
    private void createIndexes() throws SQLException {
        executeUpdate("CREATE INDEX IF NOT EXISTS idx_player_profiles_username ON player_profiles(username)");
        executeUpdate("CREATE INDEX IF NOT EXISTS idx_dungeon_progress_player ON dungeon_progress(player_uuid)");
        executeUpdate("CREATE INDEX IF NOT EXISTS idx_dungeon_progress_dungeon ON dungeon_progress(dungeon_id, difficulty)");
        executeUpdate("CREATE INDEX IF NOT EXISTS idx_achievements_player ON achievements(player_uuid)");
        executeUpdate("CREATE INDEX IF NOT EXISTS idx_statistics_player ON statistics(player_uuid)");
        executeUpdate("CREATE INDEX IF NOT EXISTS idx_party_history_player ON party_history(player_uuid)");
        executeUpdate("CREATE INDEX IF NOT EXISTS idx_leaderboards_dungeon ON leaderboards(dungeon_id, difficulty, completion_time)");
        executeUpdate("CREATE INDEX IF NOT EXISTS idx_leaderboards_season ON leaderboards(season, completion_time)");
    }
    
    /**
     * Gets the current schema version.
     */
    private int getSchemaVersion() throws SQLException {
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='" + SCHEMA_VERSION_TABLE + "'")) {
            
            if (!rs.next()) {
                return 0; // No schema version table exists
            }
        }
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT version FROM " + SCHEMA_VERSION_TABLE + " ORDER BY version DESC LIMIT 1")) {
            
            if (rs.next()) {
                return rs.getInt("version");
            }
            return 0;
        }
    }
    
    /**
     * Sets the schema version.
     */
    private void setSchemaVersion(int version) throws SQLException {
        executeUpdate("DELETE FROM " + SCHEMA_VERSION_TABLE);
        executeUpdate("INSERT INTO " + SCHEMA_VERSION_TABLE + " (version) VALUES (?)", version);
    }
    
    /**
     * Migrates the database schema from an older version.
     */
    private void migrateSchema(int fromVersion) throws SQLException {
        plugin.getLogger().info("Migrating database schema from version " + fromVersion + " to " + CURRENT_SCHEMA_VERSION);
        
        // Perform migrations based on version
        switch (fromVersion) {
            case 0:
                // Initial schema creation - no migration needed
                break;
            // Add more migration cases as needed
        }
        
        plugin.getLogger().info("Database schema migration completed");
    }
    
    /**
     * Loads a player profile from the database or creates a new one.
     */
    @NotNull
    public CompletableFuture<PlayerProfile> loadPlayerProfile(@NotNull UUID uuid, @NotNull String username) {
        return CompletableFuture.supplyAsync(() -> {
            // Check cache first
            PlayerProfile cached = profileCache.get(uuid);
            if (cached != null) {
                return cached;
            }
            
            try {
                // Try to load from database
                String sql = "SELECT * FROM player_profiles WHERE uuid = ?";
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setString(1, uuid.toString());
                    
                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            PlayerProfile profile = PlayerProfile.fromResultSet(rs);
                            profileCache.put(uuid, profile);
                            return profile;
                        }
                    }
                }
                
                // Create new profile if not found
                PlayerProfile newProfile = createNewPlayerProfile(uuid, username);
                profileCache.put(uuid, newProfile);
                return newProfile;
                
            } catch (SQLException e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to load player profile for " + uuid, e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * Creates a new player profile in the database.
     */
    @NotNull
    private PlayerProfile createNewPlayerProfile(@NotNull UUID uuid, @NotNull String username) throws SQLException {
        long now = System.currentTimeMillis();
        
        String sql = """
            INSERT INTO player_profiles (uuid, username, first_join, last_seen, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """;
        
        executeUpdate(sql, uuid.toString(), username, now, now, now, now);
        
        return new PlayerProfile(
            uuid,
            username,
            now,
            now,
            0L,
            0,
            0,
            0,
            "{}",
            now,
            now
        );
    }
    
    /**
     * Saves a player profile to the database.
     */
    @NotNull
    public CompletableFuture<Void> savePlayerProfile(@NotNull PlayerProfile profile) {
        return CompletableFuture.runAsync(() -> {
            try {
                String sql = """
                    UPDATE player_profiles SET
                        username = ?, last_seen = ?, total_playtime = ?,
                        dungeons_completed = ?, deaths = ?, season_score = ?,
                        settings = ?, updated_at = ?
                    WHERE uuid = ?
                """;
                
                long now = System.currentTimeMillis();
                executeUpdate(sql,
                    profile.username(),
                    profile.lastSeen(),
                    profile.totalPlaytime(),
                    profile.dungeonsCompleted(),
                    profile.deaths(),
                    profile.seasonScore(),
                    profile.settings(),
                    now,
                    profile.uuid().toString()
                );
                
                // Update cache
                profileCache.put(profile.uuid(), profile);
                
            } catch (SQLException e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to save player profile for " + profile.uuid(), e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * Gets dungeon progress for a player.
     */
    @NotNull
    public CompletableFuture<DungeonProgress> getDungeonProgress(@NotNull UUID playerUuid, @NotNull String dungeonId, @NotNull String difficulty) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String sql = "SELECT * FROM dungeon_progress WHERE player_uuid = ? AND dungeon_id = ? AND difficulty = ?";
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setString(1, playerUuid.toString());
                    stmt.setString(2, dungeonId);
                    stmt.setString(3, difficulty);
                    
                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            return DungeonProgress.fromResultSet(rs);
                        }
                    }
                }
                
                // Create new progress entry if not found
                return createNewDungeonProgress(playerUuid, dungeonId, difficulty);
                
            } catch (SQLException e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to get dungeon progress", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * Creates a new dungeon progress entry.
     */
    @NotNull
    private DungeonProgress createNewDungeonProgress(@NotNull UUID playerUuid, @NotNull String dungeonId, @NotNull String difficulty) throws SQLException {
        long now = System.currentTimeMillis();
        
        String sql = """
            INSERT INTO dungeon_progress (player_uuid, dungeon_id, difficulty, unlocked_at)
            VALUES (?, ?, ?, ?)
        """;
        
        executeUpdate(sql, playerUuid.toString(), dungeonId, difficulty, now);
        
        return new DungeonProgress(
            playerUuid,
            dungeonId,
            difficulty,
            false,
            null,
            0,
            0,
            null,
            now
        );
    }
    
    /**
     * Saves dungeon progress.
     */
    @NotNull
    public CompletableFuture<Void> saveDungeonProgress(@NotNull DungeonProgress progress) {
        return CompletableFuture.runAsync(() -> {
            try {
                String sql = """
                    UPDATE dungeon_progress SET
                        completed = ?, best_time = ?, completions = ?,
                        deaths = ?, last_attempt = ?
                    WHERE player_uuid = ? AND dungeon_id = ? AND difficulty = ?
                """;
                
                executeUpdate(sql,
                    progress.completed(),
                    progress.bestTime(),
                    progress.completions(),
                    progress.deaths(),
                    progress.lastAttempt(),
                    progress.playerUuid().toString(),
                    progress.dungeonId(),
                    progress.difficulty()
                );
                
            } catch (SQLException e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to save dungeon progress", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * Executes an update query with parameters.
     */
    private void executeUpdate(@NotNull String sql, @Nullable Object... params) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    stmt.setObject(i + 1, params[i]);
                }
            }
            stmt.executeUpdate();
        }
    }
    
    /**
     * Performs periodic maintenance tasks.
     */
    public void performMaintenance() {
        if (!initialized) return;
        
        try {
            // Clean up old cache entries
            long cutoff = System.currentTimeMillis() - (30 * 60 * 1000); // 30 minutes
            profileCache.entrySet().removeIf(entry -> 
                entry.getValue().lastSeen() < cutoff
            );
            
            // Optimize database
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("PRAGMA optimize");
            }
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Error during data service maintenance", e);
        }
    }
    
    /**
     * Shuts down the data service and closes connections.
     */
    public void shutdown() {
        try {
            if (connection != null && !connection.isClosed()) {
                // Save any cached profiles
                for (PlayerProfile profile : profileCache.values()) {
                    savePlayerProfile(profile).join();
                }
                
                connection.close();
                plugin.getLogger().info("Database connection closed");
            }
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Error closing database connection", e);
        }
        
        profileCache.clear();
        initialized = false;
    }
    
    /**
     * Checks if the service is initialized.
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Gets a cached player profile.
     */
    @Nullable
    public PlayerProfile getCachedProfile(@NotNull UUID uuid) {
        return profileCache.get(uuid);
    }
    
    /**
     * Removes a profile from cache.
     */
    public void removeCachedProfile(@NotNull UUID uuid) {
        profileCache.remove(uuid);
    }
}
