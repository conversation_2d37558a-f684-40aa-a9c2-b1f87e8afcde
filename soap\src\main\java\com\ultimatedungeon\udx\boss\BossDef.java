package com.ultimatedungeon.udx.boss;

import org.bukkit.entity.EntityType;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;

/**
 * Defines a boss with phases, abilities, and mechanics.
 * 
 * <p>Boss definitions include multiple phases triggered by health thresholds
 * or timers, each with their own abilities, mechanics, and environmental changes.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record BossDef(
    @NotNull String id,
    @NotNull String displayName,
    @NotNull EntityType entityType,
    @NotNull BossAttributes attributes,
    @NotNull List<BossPhase> phases,
    @NotNull Map<String, Object> equipment,
    @NotNull List<String> immunities,
    @NotNull BossArena arena,
    @NotNull Map<String, Object> metadata
) {
    
    /**
     * Boss attributes including health, damage, and resistances.
     */
    public record BossAttributes(
        double maxHealth,
        double attackDamage,
        double movementSpeed,
        double armor,
        double armorToughness,
        double knockbackResistance,
        @NotNull Map<String, Double> resistances
    ) {}
    
    /**
     * Represents a boss phase with triggers and abilities.
     */
    public record BossPhase(
        @NotNull String id,
        @NotNull String name,
        @NotNull PhaseTrigger trigger,
        @NotNull List<BossAbility> abilities,
        @NotNull List<String> mechanics,
        @NotNull Map<String, Object> environment,
        @Nullable String announcement
    ) {}
    
    /**
     * Phase trigger conditions.
     */
    public record PhaseTrigger(
        @NotNull TriggerType type,
        double value,
        @Nullable String condition
    ) {
        
        public enum TriggerType {
            HEALTH_PERCENT,
            TIMER,
            PLAYER_COUNT,
            DAMAGE_TAKEN,
            CUSTOM
        }
    }
    
    /**
     * Boss ability definition.
     */
    public record BossAbility(
        @NotNull String id,
        @NotNull String name,
        @NotNull AbilityType type,
        @NotNull AbilityTiming timing,
        @NotNull Map<String, Object> parameters,
        @Nullable Telegraph telegraph
    ) {
        
        public enum AbilityType {
            PROJECTILE,
            AOE_DAMAGE,
            SUMMON_MINIONS,
            TELEPORT,
            CHARGE,
            HEAL,
            BUFF_SELF,
            DEBUFF_PLAYERS,
            ENVIRONMENTAL,
            CUSTOM
        }
    }
    
    /**
     * Ability timing configuration.
     */
    public record AbilityTiming(
        @NotNull TimingType type,
        long interval,
        long delay,
        int maxUses,
        @NotNull List<String> conditions
    ) {
        
        public enum TimingType {
            INTERVAL,
            ON_PHASE_START,
            ON_PHASE_END,
            ON_DAMAGE,
            ON_PLAYER_NEAR,
            RANDOM,
            CUSTOM
        }
    }
    
    /**
     * Telegraph configuration for ability warnings.
     */
    public record Telegraph(
        long duration,
        @NotNull String type,
        @NotNull Map<String, Object> visual,
        @Nullable String sound,
        @Nullable String message
    ) {}
    
    /**
     * Boss arena configuration.
     */
    public record BossArena(
        @NotNull String type,
        @NotNull Map<String, Object> bounds,
        @NotNull List<String> mechanics,
        @NotNull Map<String, Object> environment
    ) {}
    
    /**
     * Gets a phase by ID.
     * 
     * @param phaseId the phase ID
     * @return the phase, or null if not found
     */
    @Nullable
    public BossPhase getPhase(@NotNull String phaseId) {
        return phases.stream()
            .filter(phase -> phase.id().equals(phaseId))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Gets the first phase (usually the starting phase).
     * 
     * @return the first phase
     * @throws IllegalStateException if no phases are defined
     */
    @NotNull
    public BossPhase getFirstPhase() {
        if (phases.isEmpty()) {
            throw new IllegalStateException("Boss " + id + " has no phases defined");
        }
        return phases.get(0);
    }
    
    /**
     * Gets the next phase after the given phase.
     * 
     * @param currentPhase the current phase
     * @return the next phase, or null if this is the last phase
     */
    @Nullable
    public BossPhase getNextPhase(@NotNull BossPhase currentPhase) {
        int currentIndex = phases.indexOf(currentPhase);
        if (currentIndex >= 0 && currentIndex < phases.size() - 1) {
            return phases.get(currentIndex + 1);
        }
        return null;
    }
    
    /**
     * Checks if this boss has a specific immunity.
     * 
     * @param immunity the immunity to check
     * @return true if the boss is immune
     */
    public boolean hasImmunity(@NotNull String immunity) {
        return immunities.contains(immunity);
    }
    
    /**
     * Gets resistance value for a damage type.
     * 
     * @param damageType the damage type
     * @return resistance value (0.0 = no resistance, 1.0 = full immunity)
     */
    public double getResistance(@NotNull String damageType) {
        return attributes.resistances().getOrDefault(damageType, 0.0);
    }
    
    /**
     * Validates the boss definition.
     * 
     * @throws IllegalArgumentException if the definition is invalid
     */
    public void validate() {
        if (id.isBlank()) {
            throw new IllegalArgumentException("Boss ID cannot be blank");
        }
        
        if (displayName.isBlank()) {
            throw new IllegalArgumentException("Boss display name cannot be blank");
        }
        
        if (phases.isEmpty()) {
            throw new IllegalArgumentException("Boss must have at least one phase");
        }
        
        if (attributes.maxHealth() <= 0) {
            throw new IllegalArgumentException("Boss max health must be positive");
        }
        
        // Validate phases
        for (BossPhase phase : phases) {
            validatePhase(phase);
        }
    }
    
    private void validatePhase(@NotNull BossPhase phase) {
        if (phase.id().isBlank()) {
            throw new IllegalArgumentException("Phase ID cannot be blank");
        }
        
        if (phase.name().isBlank()) {
            throw new IllegalArgumentException("Phase name cannot be blank");
        }
        
        // Validate trigger
        PhaseTrigger trigger = phase.trigger();
        if (trigger.type() == PhaseTrigger.TriggerType.HEALTH_PERCENT) {
            if (trigger.value() < 0 || trigger.value() > 100) {
                throw new IllegalArgumentException("Health percent trigger must be between 0 and 100");
            }
        }
        
        // Validate abilities
        for (BossAbility ability : phase.abilities()) {
            validateAbility(ability);
        }
    }
    
    private void validateAbility(@NotNull BossAbility ability) {
        if (ability.id().isBlank()) {
            throw new IllegalArgumentException("Ability ID cannot be blank");
        }
        
        if (ability.name().isBlank()) {
            throw new IllegalArgumentException("Ability name cannot be blank");
        }
        
        // Validate timing
        AbilityTiming timing = ability.timing();
        if (timing.interval() < 0) {
            throw new IllegalArgumentException("Ability interval cannot be negative");
        }
        
        if (timing.delay() < 0) {
            throw new IllegalArgumentException("Ability delay cannot be negative");
        }
    }
}
