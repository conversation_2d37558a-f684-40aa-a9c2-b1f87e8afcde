package com.ultimatedungeon.udx.api.progression;

import org.jetbrains.annotations.NotNull;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

/**
 * Immutable representation of dungeon progress data for API consumers.
 */
public record DungeonProgressData(
    @NotNull UUID playerId,
    @NotNull String dungeonId,
    @NotNull Map<String, DifficultyProgress> difficultyProgress,
    int totalCompletions,
    long bestTimeMs,
    @NotNull Instant firstCompleted,
    @NotNull Instant lastCompleted
) {
    
    public record DifficultyProgress(
        @NotNull String difficulty,
        int completions,
        long bestTimeMs,
        int bestDeaths,
        boolean unlocked
    ) {}
    
    public DungeonProgressData {
        if (playerId == null) throw new IllegalArgumentException("Player ID cannot be null");
        if (dungeonId == null || dungeonId.isBlank()) throw new IllegalArgumentException("Dungeon ID cannot be null or blank");
        if (difficultyProgress == null) throw new IllegalArgumentException("Difficulty progress cannot be null");
        if (totalCompletions < 0) throw new IllegalArgumentException("Total completions cannot be negative");
        if (bestTimeMs < 0) throw new IllegalArgumentException("Best time cannot be negative");
        if (firstCompleted == null) throw new IllegalArgumentException("First completed cannot be null");
        if (lastCompleted == null) throw new IllegalArgumentException("Last completed cannot be null");
    }
    
    public boolean isDifficultyUnlocked(@NotNull String difficulty) {
        DifficultyProgress progress = difficultyProgress.get(difficulty);
        return progress != null && progress.unlocked();
    }
}
