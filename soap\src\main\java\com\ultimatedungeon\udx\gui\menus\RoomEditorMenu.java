package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Room editor for creating and editing dungeon room templates.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class RoomEditorMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    private String selectedRoomType = "NORMAL";
    private String selectedTheme = "STONE";
    private boolean hasSelection = false;
    
    public RoomEditorMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Room Editor").color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Header
        ItemStack headerItem = new ItemBuilder(Material.BRICKS)
            .name(Component.text("Room Template Editor").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Create and edit room templates").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Build rooms in the world, then use").color(NamedTextColor.YELLOW),
                Component.text("these tools to capture them as templates").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(4, headerItem, clickType -> {
            // No action for header
        });
        
        // Selection Tools
        setupSelectionTools();
        
        // Room Configuration
        setupRoomConfiguration();
        
        // Connector Management
        setupConnectorManagement();
        
        // Marker Placement
        setupMarkerPlacement();
        
        // Actions
        setupActions();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Editor Hub").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(49, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openEditorHub(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupSelectionTools() {
        // Selection Wand
        ItemStack wandItem = new ItemBuilder(Material.GOLDEN_AXE)
            .name(Component.text("Selection Wand").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("WorldEdit-style selection tool").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Left-click: Set position 1").color(NamedTextColor.YELLOW),
                Component.text("Right-click: Set position 2").color(NamedTextColor.YELLOW),
                Component.empty(),
                hasSelection ? 
                    Component.text("✓ Selection active").color(NamedTextColor.GREEN) :
                    Component.text("✗ No selection").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("Click to get wand").color(NamedTextColor.AQUA)
            )
            .glow(hasSelection)
            .build();
        
        setItem(10, wandItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // Give selection wand
                ItemStack wand = new ItemBuilder(Material.GOLDEN_AXE)
                    .name(Component.text("UDX Selection Wand").color(NamedTextColor.GOLD))
                    .lore(
                        Component.text("Left-click: Set position 1").color(NamedTextColor.YELLOW),
                        Component.text("Right-click: Set position 2").color(NamedTextColor.YELLOW)
                    )
                    .build();
                
                player.getInventory().addItem(wand);
                player.sendMessage(Component.text("Selection wand given! Use it to select your room area.")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
            }
        });
        
        // Clear Selection
        ItemStack clearItem = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Clear Selection").color(NamedTextColor.RED))
            .lore(
                Component.text("Remove current selection").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to clear").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(11, clearItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                hasSelection = false;
                player.sendMessage(Component.text("Selection cleared!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
                refresh();
            }
        });
        
        // Expand Selection
        ItemStack expandItem = new ItemBuilder(Material.PISTON)
            .name(Component.text("Expand Selection").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Expand selection in all directions").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Left-click: Expand by 1").color(NamedTextColor.YELLOW),
                Component.text("Right-click: Expand by 5").color(NamedTextColor.YELLOW),
                Component.text("Shift-click: Contract by 1").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(12, expandItem, clickType -> {
            if (hasSelection) {
                int amount = switch (clickType) {
                    case RIGHT -> 5;
                    case SHIFT_LEFT -> -1;
                    default -> 1;
                };
                
                player.sendMessage(Component.text("Selection " + 
                    (amount > 0 ? "expanded" : "contracted") + " by " + Math.abs(amount))
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            } else {
                player.sendMessage(Component.text("No selection to expand!")
                    .color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
        });
    }
    
    private void setupRoomConfiguration() {
        // Room Type
        Material typeMaterial = switch (selectedRoomType) {
            case "START" -> Material.EMERALD_BLOCK;
            case "BOSS" -> Material.REDSTONE_BLOCK;
            case "TREASURE" -> Material.GOLD_BLOCK;
            case "PUZZLE" -> Material.LAPIS_BLOCK;
            default -> Material.STONE_BRICKS;
        };
        
        ItemStack typeItem = new ItemBuilder(typeMaterial)
            .name(Component.text("Room Type: " + selectedRoomType).color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Configure the room's purpose").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available types:").color(NamedTextColor.YELLOW),
                Component.text("• NORMAL - Standard room").color(selectedRoomType.equals("NORMAL") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• START - Entry point").color(selectedRoomType.equals("START") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• BOSS - Boss encounter").color(selectedRoomType.equals("BOSS") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• TREASURE - Loot room").color(selectedRoomType.equals("TREASURE") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• PUZZLE - Puzzle room").color(selectedRoomType.equals("PUZZLE") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to cycle types").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(19, typeItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                selectedRoomType = switch (selectedRoomType) {
                    case "NORMAL" -> "START";
                    case "START" -> "BOSS";
                    case "BOSS" -> "TREASURE";
                    case "TREASURE" -> "PUZZLE";
                    default -> "NORMAL";
                };
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Room Theme
        Material themeMaterial = switch (selectedTheme) {
            case "STONE" -> Material.STONE_BRICKS;
            case "NETHER" -> Material.NETHER_BRICKS;
            case "END" -> Material.END_STONE_BRICKS;
            case "OCEAN" -> Material.PRISMARINE_BRICKS;
            case "JUNGLE" -> Material.MOSSY_STONE_BRICKS;
            default -> Material.COBBLESTONE;
        };
        
        ItemStack themeItem = new ItemBuilder(themeMaterial)
            .name(Component.text("Theme: " + selectedTheme).color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Visual theme for the room").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available themes:").color(NamedTextColor.YELLOW),
                Component.text("• STONE - Classic dungeon").color(selectedTheme.equals("STONE") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• NETHER - Fiery depths").color(selectedTheme.equals("NETHER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• END - Void realm").color(selectedTheme.equals("END") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• OCEAN - Underwater ruins").color(selectedTheme.equals("OCEAN") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• JUNGLE - Overgrown temple").color(selectedTheme.equals("JUNGLE") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to cycle themes").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(20, themeItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                selectedTheme = switch (selectedTheme) {
                    case "STONE" -> "NETHER";
                    case "NETHER" -> "END";
                    case "END" -> "OCEAN";
                    case "OCEAN" -> "JUNGLE";
                    default -> "STONE";
                };
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Room Size Info
        ItemStack sizeItem = new ItemBuilder(Material.STICK)
            .name(Component.text("Room Dimensions").color(NamedTextColor.YELLOW))
            .lore(
                Component.text("Current selection size").color(NamedTextColor.GRAY),
                Component.empty(),
                hasSelection ?
                    Component.text("Size: 16x8x16 blocks").color(NamedTextColor.GREEN) :
                    Component.text("No selection made").color(NamedTextColor.RED),
                hasSelection ?
                    Component.text("Volume: 2,048 blocks").color(NamedTextColor.GRAY) :
                    Component.text("Select an area first").color(NamedTextColor.GRAY)
            )
            .build();
        
        setItem(21, sizeItem, clickType -> {
            // No action - info only
        });
    }
    
    private void setupConnectorManagement() {
        // North Connector
        ItemStack northItem = new ItemBuilder(Material.IRON_DOOR)
            .name(Component.text("North Connector").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Configure north-facing connection").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: Not set").color(NamedTextColor.RED),
                Component.text("Size: Standard (3x3)").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to place connector").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(28, northItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Look at the north wall and click to place connector")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                close();
            }
        });
        
        // East Connector
        ItemStack eastItem = new ItemBuilder(Material.IRON_DOOR)
            .name(Component.text("East Connector").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Configure east-facing connection").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: Set").color(NamedTextColor.GREEN),
                Component.text("Size: Large (5x5)").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to modify connector").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(29, eastItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("East connector configured!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // South Connector
        ItemStack southItem = new ItemBuilder(Material.IRON_DOOR)
            .name(Component.text("South Connector").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Configure south-facing connection").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: Not set").color(NamedTextColor.RED),
                Component.text("Size: Standard (3x3)").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to place connector").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(30, southItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Look at the south wall and click to place connector")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                close();
            }
        });
        
        // West Connector
        ItemStack westItem = new ItemBuilder(Material.IRON_DOOR)
            .name(Component.text("West Connector").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Configure west-facing connection").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: Not set").color(NamedTextColor.RED),
                Component.text("Size: Standard (3x3)").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to place connector").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(31, westItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Look at the west wall and click to place connector")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                close();
            }
        });
    }
    
    private void setupMarkerPlacement() {
        // Spawn Marker
        ItemStack spawnItem = new ItemBuilder(Material.ZOMBIE_HEAD)
            .name(Component.text("Spawn Markers").color(NamedTextColor.RED))
            .lore(
                Component.text("Place mob spawn points").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current spawns: 3").color(NamedTextColor.YELLOW),
                Component.text("• 2x Skeleton spawns").color(NamedTextColor.WHITE),
                Component.text("• 1x Zombie spawn").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to add spawn marker").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(37, spawnItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Right-click a block to place spawn marker")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                close();
            }
        });
        
        // Chest Marker
        ItemStack chestItem = new ItemBuilder(Material.CHEST)
            .name(Component.text("Chest Markers").color(NamedTextColor.GOLD))
            .lore(
                Component.text("Place loot chest locations").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current chests: 1").color(NamedTextColor.YELLOW),
                Component.text("• 1x Treasure chest").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to add chest marker").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(38, chestItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Right-click a block to place chest marker")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                close();
            }
        });
        
        // Trigger Marker
        ItemStack triggerItem = new ItemBuilder(Material.REDSTONE)
            .name(Component.text("Trigger Markers").color(NamedTextColor.RED))
            .lore(
                Component.text("Place event triggers").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current triggers: 0").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to add trigger marker").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(39, triggerItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Right-click a block to place trigger marker")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                close();
            }
        });
        
        // Gate Marker
        ItemStack gateItem = new ItemBuilder(Material.IRON_BARS)
            .name(Component.text("Gate Markers").color(NamedTextColor.GRAY))
            .lore(
                Component.text("Place gates and barriers").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current gates: 1").color(NamedTextColor.YELLOW),
                Component.text("• 1x Exit gate").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to add gate marker").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(40, gateItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Right-click a block to place gate marker")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                close();
            }
        });
        
        // Clear Markers
        ItemStack clearMarkersItem = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Clear All Markers").color(NamedTextColor.RED))
            .lore(
                Component.text("Remove all placed markers").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will clear:").color(NamedTextColor.YELLOW),
                Component.text("• All spawn points").color(NamedTextColor.WHITE),
                Component.text("• All chest locations").color(NamedTextColor.WHITE),
                Component.text("• All triggers and gates").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to clear all").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(41, clearMarkersItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("All markers cleared!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
                refresh();
            }
        });
    }
    
    private void setupActions() {
        // Save Room
        ItemStack saveItem = new ItemBuilder(Material.WRITABLE_BOOK)
            .name(Component.text("Save Room Template").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Export room to UDX format").color(NamedTextColor.GRAY),
                Component.empty(),
                hasSelection ?
                    Component.text("✓ Ready to save").color(NamedTextColor.GREEN) :
                    Component.text("✗ Need selection first").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("This will create a .udxroom file").color(NamedTextColor.YELLOW),
                Component.text("with all blocks, markers, and metadata").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to save").color(NamedTextColor.AQUA)
            )
            .glow(hasSelection)
            .build();
        
        setItem(46, saveItem, clickType -> {
            if (clickType == ClickType.LEFT && hasSelection) {
                player.sendMessage(Component.text("Saving room template...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("✓ Room saved as 'custom_room_1.udxroom'")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            } else {
                player.sendMessage(Component.text("Make a selection first!")
                    .color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
        });
        
        // Load Room
        ItemStack loadItem = new ItemBuilder(Material.BOOK)
            .name(Component.text("Load Room Template").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Import existing room template").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available templates: 12").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to browse templates").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(47, loadItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening template browser...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open template browser menu
            }
        });
        
        // Test Room
        ItemStack testItem = new ItemBuilder(Material.ENDER_PEARL)
            .name(Component.text("Test Room").color(NamedTextColor.LIGHT_PURPLE))
            .lore(
                Component.text("Create test instance of this room").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will:").color(NamedTextColor.YELLOW),
                Component.text("• Create a temporary world").color(NamedTextColor.WHITE),
                Component.text("• Paste the room template").color(NamedTextColor.WHITE),
                Component.text("• Spawn configured mobs").color(NamedTextColor.WHITE),
                Component.text("• Allow testing gameplay").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to test").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(48, testItem, clickType -> {
            if (clickType == ClickType.LEFT && hasSelection) {
                player.sendMessage(Component.text("Creating test instance...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("Teleporting to test world...")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                close();
            } else {
                player.sendMessage(Component.text("Save the room first!")
                    .color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
        });
    }
}
