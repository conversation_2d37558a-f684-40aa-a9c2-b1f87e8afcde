# UltimateDungeonX Server Setup Guide

This guide provides step-by-step instructions for setting up a Paper server with UltimateDungeonX for testing and production use.

## Quick Setup (5 Minutes)

### Prerequisites

- **Java 21** or higher installed
- **4GB RAM** minimum (8GB recommended)
- **10GB disk space** for server and worlds
- **Internet connection** for downloading Paper

### Step 1: Download Paper Server

```bash
# Create server directory
mkdir udx-server
cd udx-server

# Download Paper 1.21.3 (latest)
wget https://api.papermc.io/v2/projects/paper/versions/1.21.3/builds/latest/downloads/paper-1.21.3-latest.jar

# Or use curl if wget is not available
curl -o paper-1.21.3-latest.jar https://api.papermc.io/v2/projects/paper/versions/1.21.3/builds/latest/downloads/paper-1.21.3-latest.jar
```

### Step 2: Create Start Script

**Linux/Mac (`start.sh`)**:
```bash
#!/bin/bash
java -Xmx4G -Xms2G -XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+DisableExplicitGC -XX:+AlwaysPreTouch -XX:G1NewSizePercent=30 -XX:G1MaxNewSizePercent=40 -XX:G1HeapRegionSize=8M -XX:G1ReservePercent=20 -XX:G1HeapWastePercent=5 -XX:G1MixedGCCountTarget=4 -XX:InitiatingHeapOccupancyPercent=15 -XX:G1MixedGCLiveThresholdPercent=90 -XX:G1RSetUpdatingPauseTimePercent=5 -XX:SurvivorRatio=32 -XX:+PerfDisableSharedMem -XX:MaxTenuringThreshold=1 -Dusing.aikars.flags=https://mcflags.emc.gs -Daikars.new.flags=true -jar paper-1.21.3-latest.jar --nogui

echo "Server stopped. Press any key to exit..."
read -n 1
```

**Windows (`start.bat`)**:
```batch
@echo off
java -Xmx4G -Xms2G -XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+DisableExplicitGC -XX:+AlwaysPreTouch -XX:G1NewSizePercent=30 -XX:G1MaxNewSizePercent=40 -XX:G1HeapRegionSize=8M -XX:G1ReservePercent=20 -XX:G1HeapWastePercent=5 -XX:G1MixedGCCountTarget=4 -XX:InitiatingHeapOccupancyPercent=15 -XX:G1MixedGCLiveThresholdPercent=90 -XX:G1RSetUpdatingPauseTimePercent=5 -XX:SurvivorRatio=32 -XX:+PerfDisableSharedMem -XX:MaxTenuringThreshold=1 -Dusing.aikars.flags=https://mcflags.emc.gs -Daikars.new.flags=true -jar paper-1.21.3-latest.jar --nogui

echo Server stopped. Press any key to exit...
pause >nul
```

Make the script executable (Linux/Mac):
```bash
chmod +x start.sh
```

### Step 3: Initial Server Setup

1. **Start the server** to generate files:
   ```bash
   ./start.sh  # Linux/Mac
   start.bat   # Windows
   ```

2. **Accept EULA** when prompted:
   - Edit `eula.txt` and change `eula=false` to `eula=true`

3. **Restart the server** to complete setup

4. **Stop the server** (type `stop` in console) to install plugins

### Step 4: Install UltimateDungeonX

1. **Download the plugin**:
   - Place `UltimateDungeonX-<version>.jar` in the `plugins/` folder

2. **Start the server** again:
   ```bash
   ./start.sh
   ```

3. **Verify installation**:
   - Look for `[UltimateDungeonX] Enabled successfully` in console
   - Check that `plugins/UltimateDungeonX/` folder was created

### Step 5: Basic Configuration

1. **Stop the server** and edit `plugins/UltimateDungeonX/config.yml`:

```yaml
# Basic configuration for testing
build_world: "udx_build"
max_concurrent_instances: 5
debug: true

performance:
  tick_budget_per_frame: 5000
  max_entities_per_instance: 50

gui:
  sound_effects_enabled: true
  particle_effects_enabled: true
```

2. **Create build world** (optional, for room creation):
   - Install Multiverse-Core plugin for world management
   - Or manually create world folder structure

3. **Set permissions** (if using a permission plugin):
   ```
   /lp group default permission set udx.use true
   /lp group default permission set udx.join true
   /lp group default permission set udx.party true
   /lp user <admin> permission set udx.admin true
   ```

### Step 6: Test the Plugin

1. **Join the server** and run:
   ```
   /udx
   ```

2. **Verify the main menu opens** with dungeon options

3. **Test admin features** (if you have admin permissions):
   ```
   /udx admin
   ```

## Production Setup

### Recommended Server Specifications

**Minimum**:
- 4GB RAM
- 2 CPU cores
- 20GB SSD storage
- 10 Mbps upload

**Recommended**:
- 8GB RAM
- 4 CPU cores
- 50GB NVMe SSD
- 25 Mbps upload

**High Performance**:
- 16GB RAM
- 8 CPU cores
- 100GB NVMe SSD
- 100 Mbps upload

### Production Configuration

#### Server Properties (`server.properties`)

```properties
# Basic settings
server-name=UltimateDungeonX Server
motd=§6Ultimate Dungeon Adventures Await!
server-port=25565
max-players=50
difficulty=normal
gamemode=survival
hardcore=false
pvp=false

# Performance settings
view-distance=8
simulation-distance=6
entity-broadcast-range-percentage=100
network-compression-threshold=256

# World settings
spawn-protection=0
allow-flight=true
max-world-size=29999984

# Security
online-mode=true
enforce-whitelist=false
white-list=false
```

#### Paper Configuration (`config/paper-global.yml`)

```yaml
# Performance optimizations
chunk-loading:
  min-load-radius: 2
  max-concurrent-sends: 2
  autoconfig-send-distance: true
  target-player-chunk-send-rate: 100.0
  global-max-chunk-send-rate: -1.0
  enable-frustum-priority: false
  global-max-chunk-load-rate: -1.0
  player-max-concurrent-loads: 20.0
  global-max-concurrent-loads: 500.0

entities:
  armor-stands:
    do-collision-entity-lookups: false
    tick: false
  markers:
    tick: false
  mob-effects:
    undead-immune-to-certain-effects: true
  sniffer:
    boosted-dig-chance: 0.5
    boosted-dig-time-reduction: 10
  spawning:
    all-chunks-are-slime-chunks: false
    alt-item-despawn-rate:
      enabled: false
      items:
        cobblestone: 300
    count-all-mobs-for-spawning: false
    creative-arrow-despawn-rate: 300
    despawn-ranges:
      ambient:
        hard: 128
        soft: 32
      axolotls:
        hard: 128
        soft: 32
      creature:
        hard: 128
        soft: 32
      misc:
        hard: 128
        soft: 32
      monster:
        hard: 128
        soft: 32
      underground_water_creature:
        hard: 128
        soft: 32
      water_ambient:
        hard: 64
        soft: 32
      water_creature:
        hard: 128
        soft: 32
    disable-mob-spawner-spawn-egg-transformation: false
    duplicate-uuid:
      mode: SAFE_REGEN
      safe-regen-delete-range: 32
    filter-bad-tile-entity-nbt-from-falling-blocks: true
    filtered-entity-tag-nbt-paths:
    - Pos
    - Motion
    - SleepingX
    - SleepingY
    - SleepingZ
    iron-golems-can-spawn-in-air: false
    monster-spawn-max-light-level: -1
    non-player-arrow-despawn-rate: 60
    per-player-mob-spawns: true
    scan-for-legacy-ender-dragon: true
    skeleton-horse-thunder-spawn-chance: 0.01
    slime-spawn-height:
      slime-chunk:
        maximum: 40.0
      surface-biome:
        maximum: 70.0
        minimum: 50.0
    spawn-limits:
      ambient: -1
      axolotls: -1
      creature: -1
      monster: -1
      underground_water_creature: -1
      water_ambient: -1
      water_creature: -1
    wandering-trader:
      spawn-chance-failure-increment: 25
      spawn-chance-max: 75
      spawn-chance-min: 25
      spawn-day-length: 24000
      spawn-minute-length: 1200
    wateranimal-spawn-height:
      maximum: 58.0
      minimum: 45.0
```

#### UltimateDungeonX Production Config

```yaml
# Production configuration
config-version: 1

# General settings
build_world: "udx_build"
max_concurrent_instances: 20
instance_timeout_minutes: 90
default_language: "en_US"

# Debug and logging (disabled for production)
debug: false
log_level: "INFO"
performance_logging: true

# Performance settings
performance:
  tick_budget_per_frame: 4000
  max_entities_per_instance: 100
  entity_cleanup_interval: 200
  async_world_generation: true
  world_generation_threads: 4
  chunk_preload_radius: 2
  memory_cleanup_interval: 6000
  gc_threshold_mb: 1024
  object_pool_enabled: true
  blocks_per_tick: 2048
  lighting_update_delay: 3

# Database configuration
database:
  type: "sqlite"
  file: "players.db"
  connection_pool_size: 10
  backup:
    enabled: true
    interval_hours: 6
    keep_backups: 14

# GUI settings
gui:
  menu_update_interval: 40
  animation_speed: 15
  sound_effects_enabled: true
  particle_effects_enabled: true
  click_sound: "UI_BUTTON_CLICK"
  auto_close_delay: 600
  confirm_destructive_actions: true
  items_per_page: 45

# Integration settings
integrations:
  vault:
    enabled: true
    currency_symbol: "$"
  placeholderapi:
    enabled: true
  discord:
    enabled: false

# Affix system
affixes:
  rotation:
    daily_count: 3
    weekly_count: 5
    monthly_count: 8
    rotation_time: "00:00"
  rarity_weights:
    COMMON: 45.0
    UNCOMMON: 30.0
    RARE: 18.0
    EPIC: 6.0
    LEGENDARY: 1.0
  seasonal_events:
    enabled: true
```

### Security Considerations

#### Firewall Configuration

```bash
# Ubuntu/Debian
sudo ufw allow 25565/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=25565/tcp
sudo firewall-cmd --reload
```

#### Backup Strategy

**Automated Backup Script** (`backup.sh`):
```bash
#!/bin/bash

# Configuration
SERVER_DIR="/path/to/server"
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="udx_backup_$DATE"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Stop server gracefully
screen -S minecraft -p 0 -X stuff "say Server backup starting in 30 seconds...^M"
sleep 30
screen -S minecraft -p 0 -X stuff "save-all^M"
sleep 5
screen -S minecraft -p 0 -X stuff "save-off^M"
sleep 5

# Create backup
cd "$SERVER_DIR"
tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" \
    world/ \
    world_nether/ \
    world_the_end/ \
    plugins/UltimateDungeonX/ \
    server.properties \
    --exclude="*.tmp" \
    --exclude="session.lock"

# Resume server
screen -S minecraft -p 0 -X stuff "save-on^M"
screen -S minecraft -p 0 -X stuff "say Backup completed!^M"

# Clean old backups (keep last 7 days)
find "$BACKUP_DIR" -name "udx_backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_NAME.tar.gz"
```

**Cron Job** (daily at 3 AM):
```bash
crontab -e
# Add this line:
0 3 * * * /path/to/backup.sh >> /var/log/minecraft_backup.log 2>&1
```

### Monitoring and Maintenance

#### Performance Monitoring Script

```bash
#!/bin/bash

# Monitor server performance
LOG_FILE="/var/log/minecraft_performance.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Get server stats
CPU_USAGE=$(top -bn1 | grep "java" | awk '{print $9}')
MEMORY_USAGE=$(ps -o pid,vsz,rss,comm -p $(pgrep java) | tail -1 | awk '{print $3}')
DISK_USAGE=$(df -h /path/to/server | tail -1 | awk '{print $5}')

# Log stats
echo "$DATE - CPU: ${CPU_USAGE}%, Memory: ${MEMORY_USAGE}KB, Disk: $DISK_USAGE" >> "$LOG_FILE"

# Alert if usage is high
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "HIGH CPU USAGE: $CPU_USAGE%" | mail -s "Server Alert" <EMAIL>
fi
```

#### Health Check Script

```bash
#!/bin/bash

# Check if server is responding
SERVER_HOST="localhost"
SERVER_PORT="25565"

# Test connection
if timeout 5 bash -c "</dev/tcp/$SERVER_HOST/$SERVER_PORT"; then
    echo "$(date): Server is responding"
else
    echo "$(date): Server is not responding - attempting restart"
    
    # Restart server
    screen -S minecraft -X quit
    sleep 10
    cd /path/to/server
    screen -dmS minecraft ./start.sh
    
    # Send alert
    echo "Server was restarted due to unresponsiveness" | mail -s "Server Restart" <EMAIL>
fi
```

### Troubleshooting Common Issues

#### Server Won't Start

1. **Check Java version**:
   ```bash
   java -version
   ```

2. **Check available memory**:
   ```bash
   free -h
   ```

3. **Check server logs**:
   ```bash
   tail -f logs/latest.log
   ```

#### Plugin Errors

1. **Check plugin compatibility**:
   - Ensure Paper 1.21.x
   - Verify Java 21+
   - Check for conflicting plugins

2. **Enable debug mode**:
   ```yaml
   # config.yml
   debug: true
   log_level: "DEBUG"
   ```

3. **Check permissions**:
   ```
   /lp user <player> permission check udx.use
   ```

#### Performance Issues

1. **Monitor tick time**:
   ```
   /tps
   /spark tps
   ```

2. **Check entity counts**:
   ```
   /udx admin
   # Navigate to Debug → Performance Monitor
   ```

3. **Adjust performance settings**:
   ```yaml
   performance:
     tick_budget_per_frame: 3000  # Reduce for better performance
     max_entities_per_instance: 50  # Lower limit
   ```

### Support and Resources

- **Documentation**: Check `docs/` folder for detailed guides
- **Issues**: Report bugs with full server logs and configuration
- **Community**: Join Discord server for real-time support
- **Updates**: Check releases page for latest versions

---

*This setup guide should get you running with UltimateDungeonX quickly and efficiently. For production deployments, always test thoroughly in a staging environment first.*
