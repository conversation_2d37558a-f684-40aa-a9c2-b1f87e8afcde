# UltimateDungeonX Baseline Mob Library
# A collection of 20+ mob definitions for use across all dungeons

mobs:
  # ===== UNDEAD CATEGORY =====
  
  skeleton_archer:
    id: "skeleton_archer"
    display_name: "§fSkeleton Archer"
    entity_type: "SKELETON"
    category: "undead"
    difficulty: 2
    attributes:
      max_health: 20.0
      attack_damage: 5.0
      movement_speed: 0.25
      armor: 1.0
    equipment:
      main_hand:
        material: "BOW"
        enchantments:
          POWER: 1
    behaviors: ["RANGED_ATTACK", "AVOID_ENTITY:WOLF", "LOOK_AT_PLAYER"]
    
  zombie_brute:
    id: "zombie_brute"
    display_name: "§2Zombie Brute"
    entity_type: "ZOMBIE"
    category: "undead"
    difficulty: 3
    attributes:
      max_health: 35.0
      attack_damage: 8.0
      movement_speed: 0.2
      armor: 3.0
      knockback_resistance: 0.5
    equipment:
      main_hand:
        material: "IRON_AXE"
      helmet:
        material: "IRON_HELMET"
    behaviors: ["MELEE_ATTACK", "BREAK_DOORS", "CALL_REINFORCEMENTS"]
    
  wraith:
    id: "wraith"
    display_name: "§8Wraith"
    entity_type: "VEX"
    category: "undead"
    difficulty: 4
    attributes:
      max_health: 15.0
      attack_damage: 6.0
      movement_speed: 0.4
    abilities: ["PHASE_THROUGH_WALLS", "LIFE_DRAIN"]
    behaviors: ["FLY_AROUND", "SWOOP_ATTACK", "TELEPORT_TO_TARGET"]
    
  # ===== FIRE CATEGORY =====
  
  fire_elemental:
    id: "fire_elemental"
    display_name: "§cFire Elemental"
    entity_type: "BLAZE"
    category: "fire"
    difficulty: 5
    attributes:
      max_health: 40.0
      attack_damage: 10.0
      movement_speed: 0.3
    abilities: ["FIREBALL_BARRAGE", "FLAME_AURA", "IGNITE_GROUND"]
    behaviors: ["FLY_AROUND", "RANGED_ATTACK", "AVOID_WATER"]
    flags: ["FIRE_IMMUNE"]
    
  molten_golem:
    id: "molten_golem"
    display_name: "§6Molten Golem"
    entity_type: "IRON_GOLEM"
    category: "fire"
    difficulty: 6
    attributes:
      max_health: 80.0
      attack_damage: 15.0
      movement_speed: 0.15
      armor: 5.0
      knockback_resistance: 0.8
    abilities: ["LAVA_SLAM", "MOLTEN_CHARGE", "HEAT_WAVE"]
    behaviors: ["GUARD_AREA", "CHARGE_ATTACK", "GROUND_SLAM"]
    flags: ["FIRE_IMMUNE", "KNOCKBACK_RESISTANT"]
    
  ember_sprite:
    id: "ember_sprite"
    display_name: "§eEmber Sprite"
    entity_type: "BAT"
    category: "fire"
    difficulty: 2
    attributes:
      max_health: 8.0
      attack_damage: 3.0
      movement_speed: 0.6
    abilities: ["EMBER_BURST", "FIRE_TRAIL"]
    behaviors: ["FLY_ERRATIC", "SWARM_ATTACK", "EXPLODE_ON_DEATH"]
    
  # ===== AIR/WIND CATEGORY =====
  
  wind_elemental:
    id: "wind_elemental"
    display_name: "§bWind Elemental"
    entity_type: "PHANTOM"
    category: "air"
    difficulty: 4
    attributes:
      max_health: 25.0
      attack_damage: 7.0
      movement_speed: 0.5
    abilities: ["WIND_BLAST", "TORNADO", "LEVITATE_ENEMIES"]
    behaviors: ["FLY_CIRCLES", "DIVE_ATTACK", "KNOCKBACK_ATTACK"]
    
  sky_pirate:
    id: "sky_pirate"
    display_name: "§9Sky Pirate"
    entity_type: "PILLAGER"
    category: "humanoid"
    difficulty: 5
    attributes:
      max_health: 30.0
      attack_damage: 8.0
      movement_speed: 0.35
    equipment:
      main_hand:
        material: "CROSSBOW"
        enchantments:
          QUICK_CHARGE: 2
      helmet:
        material: "LEATHER_HELMET"
        color: "#4169E1"
    abilities: ["EXPLOSIVE_SHOT", "GRAPPLING_HOOK", "ELYTRA_DIVE"]
    behaviors: ["RANGED_ATTACK", "STRAFE", "USE_COVER"]
    
  storm_hawk:
    id: "storm_hawk"
    display_name: "§3Storm Hawk"
    entity_type: "PARROT"
    category: "air"
    difficulty: 3
    attributes:
      max_health: 18.0
      attack_damage: 5.0
      movement_speed: 0.7
    abilities: ["LIGHTNING_STRIKE", "WIND_SHEAR", "STORM_CALL"]
    behaviors: ["FLY_HIGH", "DIVE_BOMB", "CIRCLE_PREY"]
    
  # ===== EARTH/STONE CATEGORY =====
  
  stone_guardian:
    id: "stone_guardian"
    display_name: "§7Stone Guardian"
    entity_type: "IRON_GOLEM"
    category: "earth"
    difficulty: 7
    attributes:
      max_health: 100.0
      attack_damage: 18.0
      movement_speed: 0.1
      armor: 8.0
      knockback_resistance: 1.0
    abilities: ["EARTHQUAKE", "STONE_SPIKES", "BOULDER_THROW"]
    behaviors: ["GUARD_AREA", "SLOW_APPROACH", "AREA_DENIAL"]
    flags: ["KNOCKBACK_IMMUNE", "EXPLOSION_RESISTANT"]
    
  crystal_spider:
    id: "crystal_spider"
    display_name: "§dCrystal Spider"
    entity_type: "SPIDER"
    category: "earth"
    difficulty: 4
    attributes:
      max_health: 22.0
      attack_damage: 6.0
      movement_speed: 0.4
    abilities: ["CRYSTAL_WEB", "SHARD_SHOT", "REFLECT_DAMAGE"]
    behaviors: ["WALL_CLIMB", "WEB_TRAP", "AMBUSH"]
    
  # ===== WATER/ICE CATEGORY =====
  
  frost_wraith:
    id: "frost_wraith"
    display_name: "§bFrost Wraith"
    entity_type: "STRAY"
    category: "ice"
    difficulty: 4
    attributes:
      max_health: 28.0
      attack_damage: 7.0
      movement_speed: 0.3
    abilities: ["ICE_SHARD", "FREEZE_AURA", "BLIZZARD"]
    behaviors: ["RANGED_ATTACK", "SLOW_ENEMIES", "ICE_TERRAIN"]
    
  ice_elemental:
    id: "ice_elemental"
    display_name: "§fIce Elemental"
    entity_type: "SNOW_GOLEM"
    category: "ice"
    difficulty: 5
    attributes:
      max_health: 35.0
      attack_damage: 8.0
      movement_speed: 0.25
      armor: 2.0
    abilities: ["ICE_WALL", "FROZEN_GROUND", "ICICLE_RAIN"]
    behaviors: ["AREA_CONTROL", "RANGED_ATTACK", "TERRAIN_MODIFICATION"]
    
  # ===== NATURE/PLANT CATEGORY =====
  
  thorn_beast:
    id: "thorn_beast"
    display_name: "§2Thorn Beast"
    entity_type: "WOLF"
    category: "nature"
    difficulty: 4
    attributes:
      max_health: 32.0
      attack_damage: 9.0
      movement_speed: 0.4
    abilities: ["THORN_BARRAGE", "ENTANGLE", "POISON_BITE"]
    behaviors: ["PACK_HUNT", "LEAP_ATTACK", "POISON_TRAIL"]
    
  vine_horror:
    id: "vine_horror"
    display_name: "§aVine Horror"
    entity_type: "ENDERMAN"
    category: "nature"
    difficulty: 6
    attributes:
      max_health: 45.0
      attack_damage: 12.0
      movement_speed: 0.25
      reach: 4.0
    abilities: ["VINE_WHIP", "ROOT_PRISON", "REGENERATION"]
    behaviors: ["TELEPORT_ATTACK", "GRAB_ENEMIES", "TERRAIN_CONTROL"]
    
  # ===== SHADOW/VOID CATEGORY =====
  
  shadow_stalker:
    id: "shadow_stalker"
    display_name: "§8Shadow Stalker"
    entity_type: "ENDERMAN"
    category: "shadow"
    difficulty: 5
    attributes:
      max_health: 30.0
      attack_damage: 10.0
      movement_speed: 0.35
    abilities: ["SHADOW_STEP", "DARKNESS_AURA", "VOID_STRIKE"]
    behaviors: ["STEALTH", "TELEPORT_BEHIND", "HIT_AND_RUN"]
    
  void_spawn:
    id: "void_spawn"
    display_name: "§5Void Spawn"
    entity_type: "SHULKER"
    category: "void"
    difficulty: 6
    attributes:
      max_health: 40.0
      attack_damage: 8.0
      movement_speed: 0.0
      armor: 6.0
    abilities: ["VOID_BULLET", "LEVITATION_CURSE", "DIMENSIONAL_RIFT"]
    behaviors: ["STATIONARY_ATTACK", "TELEPORT_WHEN_HIT", "AREA_DENIAL"]
    
  # ===== MECHANICAL/CONSTRUCT CATEGORY =====
  
  clockwork_soldier:
    id: "clockwork_soldier"
    display_name: "§6Clockwork Soldier"
    entity_type: "IRON_GOLEM"
    category: "construct"
    difficulty: 5
    attributes:
      max_health: 50.0
      attack_damage: 12.0
      movement_speed: 0.2
      armor: 4.0
    equipment:
      main_hand:
        material: "IRON_SWORD"
        enchantments:
          SHARPNESS: 2
    abilities: ["GEAR_SPIN", "STEAM_BLAST", "REPAIR_SELF"]
    behaviors: ["FORMATION_FIGHT", "COORDINATED_ATTACK", "SELF_REPAIR"]
    
  arcane_turret:
    id: "arcane_turret"
    display_name: "§dArcane Turret"
    entity_type: "SHULKER"
    category: "construct"
    difficulty: 4
    attributes:
      max_health: 25.0
      attack_damage: 9.0
      movement_speed: 0.0
      armor: 3.0
    abilities: ["MAGIC_MISSILE", "ARCANE_BARRIER", "OVERCHARGE"]
    behaviors: ["STATIONARY_ATTACK", "ROTATING_FIRE", "CHARGE_ATTACK"]
    
  # ===== BEAST CATEGORY =====
  
  dire_wolf:
    id: "dire_wolf"
    display_name: "§8Dire Wolf"
    entity_type: "WOLF"
    category: "beast"
    difficulty: 4
    attributes:
      max_health: 35.0
      attack_damage: 10.0
      movement_speed: 0.45
    abilities: ["HOWL_BUFF", "PACK_COORDINATION", "BLEEDING_BITE"]
    behaviors: ["PACK_HUNT", "FLANK_ATTACK", "COORDINATED_STRIKE"]
    
  giant_spider:
    id: "giant_spider"
    display_name: "§4Giant Spider"
    entity_type: "SPIDER"
    category: "beast"
    difficulty: 5
    attributes:
      max_health: 40.0
      attack_damage: 8.0
      movement_speed: 0.35
    abilities: ["WEB_SHOT", "POISON_BITE", "WALL_CRAWL"]
    behaviors: ["AMBUSH", "WEB_TRAP", "CEILING_DROP"]

# Mob Categories for easy filtering
categories:
  undead: ["skeleton_archer", "zombie_brute", "wraith"]
  fire: ["fire_elemental", "molten_golem", "ember_sprite"]
  air: ["wind_elemental", "sky_pirate", "storm_hawk"]
  earth: ["stone_guardian", "crystal_spider"]
  ice: ["frost_wraith", "ice_elemental"]
  nature: ["thorn_beast", "vine_horror"]
  shadow: ["shadow_stalker", "void_spawn"]
  construct: ["clockwork_soldier", "arcane_turret"]
  beast: ["dire_wolf", "giant_spider"]

# Difficulty Tiers
difficulty_tiers:
  easy: [1, 2]      # skeleton_archer, ember_sprite
  medium: [3, 4, 5] # zombie_brute, wraith, wind_elemental, etc.
  hard: [6, 7]      # molten_golem, stone_guardian
  extreme: [8, 9, 10] # Reserved for boss-tier mobs
