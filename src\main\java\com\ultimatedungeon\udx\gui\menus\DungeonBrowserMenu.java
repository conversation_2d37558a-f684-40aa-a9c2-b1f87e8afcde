package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.PagedMenu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Menu for browsing and selecting dungeons to play.
 * 
 * <p>This menu displays available dungeons including both built-in
 * and custom dungeons, allowing players to queue for them.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class DungeonBrowserMenu extends PagedMenu<DungeonInfo> {
    
    private final UltimateDungeonX plugin;
    
    public DungeonBrowserMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, 
              Component.text("Browse Dungeons").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true), 
              54,
              plugin.getMenuRegistry(), 
              loadAvailableDungeons(plugin));
        this.plugin = plugin;
    }
    
    private static List<DungeonInfo> loadAvailableDungeons(@NotNull UltimateDungeonX plugin) {
        List<DungeonInfo> dungeons = new ArrayList<>();
        
        // Add built-in sample dungeons
        dungeons.add(new DungeonInfo("Crypt of Echoes", "A haunted crypt filled with undead", 
                                   Material.SKELETON_SKULL, DungeonInfo.Difficulty.NORMAL, 3, 5));
        dungeons.add(new DungeonInfo("Ember Foundry", "A blazing forge deep underground", 
                                   Material.MAGMA_BLOCK, DungeonInfo.Difficulty.HARD, 4, 6));
        dungeons.add(new DungeonInfo("Skyfane Spire", "A towering spire reaching the clouds", 
                                   Material.END_STONE, DungeonInfo.Difficulty.EXPERT, 5, 8));
        
        // Add custom dungeons
        for (String customDungeon : plugin.getDungeonService().getCustomDungeonNames()) {
            dungeons.add(new DungeonInfo(customDungeon, "A custom dungeon created by admins", 
                                       Material.STRUCTURE_BLOCK, DungeonInfo.Difficulty.NORMAL, 1, 4));
        }
        
        return dungeons;
    }
    
    @Override
    @NotNull
    protected ItemStack createDisplayItem(@NotNull DungeonInfo dungeon, int index) {
        return new ItemBuilder(dungeon.getIcon())
            .name(Component.text(dungeon.getName()).color(dungeon.getDifficulty().getColor()).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text(dungeon.getDescription()).color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Difficulty: ").color(NamedTextColor.YELLOW)
                    .append(Component.text(dungeon.getDifficulty().getDisplayName()).color(dungeon.getDifficulty().getColor())),
                Component.text("Players: " + dungeon.getMinPlayers() + "-" + dungeon.getMaxPlayers()).color(NamedTextColor.AQUA),
                Component.empty(),
                Component.text("Left-click: Queue for dungeon").color(NamedTextColor.GREEN),
                Component.text("Right-click: View details").color(NamedTextColor.YELLOW)
            )
            .glow(dungeon.getDifficulty() == DungeonInfo.Difficulty.EXPERT)
            .build();
    }
    
    @Override
    @NotNull
    protected ClickHandler createClickHandler(@NotNull DungeonInfo dungeon, int index) {
        return clickType -> {
            if (clickType == ClickType.LEFT) {
                // Queue for dungeon
                queueForDungeon(dungeon);
            } else if (clickType == ClickType.RIGHT) {
                // Show dungeon details
                showDungeonDetails(dungeon);
            }
        };
    }
    
    private void queueForDungeon(@NotNull DungeonInfo dungeon) {
        close();
        
        player.sendMessage(Component.text("Queueing for " + dungeon.getName() + "...").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
        
        // TODO: Implement actual queueing system
        plugin.getSchedulerUtil().runTaskLater(() -> {
            player.sendMessage(Component.text("Queue system is under development!").color(NamedTextColor.YELLOW));
            player.sendMessage(Component.text("For now, use '/udx tp " + dungeon.getName() + "' to visit custom dungeons").color(NamedTextColor.AQUA));
        }, 20L);
    }
    
    private void showDungeonDetails(@NotNull DungeonInfo dungeon) {
        player.sendMessage(Component.text("=== " + dungeon.getName() + " ===").color(NamedTextColor.GOLD));
        player.sendMessage(Component.text(dungeon.getDescription()).color(NamedTextColor.GRAY));
        player.sendMessage(Component.text("Difficulty: ").color(NamedTextColor.YELLOW)
            .append(Component.text(dungeon.getDifficulty().getDisplayName()).color(dungeon.getDifficulty().getColor())));
        player.sendMessage(Component.text("Players: " + dungeon.getMinPlayers() + "-" + dungeon.getMaxPlayers()).color(NamedTextColor.AQUA));
        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }
    
    @Override
    protected void setupNavigation() {
        super.setupNavigation();
        
        // Add back button
        ItemStack backButton = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Back to Main Menu").color(NamedTextColor.RED))
            .lore(Component.text("Return to the main menu").color(NamedTextColor.GRAY))
            .build();
        
        setItem(45, backButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                new PlayerMainMenu(plugin, player).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
            }
        });
    }
}
