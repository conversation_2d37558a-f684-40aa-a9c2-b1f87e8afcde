package com.ultimatedungeon.udx.progression;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Objects;

/**
 * Represents an achievement that players can unlock.
 * 
 * <p>This class defines an achievement with its metadata including
 * name, description, requirements, and visual representation.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class Achievement {
    
    private final String id;
    private final String name;
    private final String description;
    private final int targetValue;
    private final String iconMaterial;
    private final List<String> requirements;
    private final boolean hidden;
    private final String category;
    
    public Achievement(@NotNull String id, @NotNull String name, @NotNull String description,
                      int targetValue, @NotNull String iconMaterial, @NotNull List<String> requirements) {
        this(id, name, description, targetValue, iconMaterial, requirements, false, "GENERAL");
    }
    
    public Achievement(@NotNull String id, @NotNull String name, @NotNull String description,
                      int targetValue, @NotNull String iconMaterial, @NotNull List<String> requirements,
                      boolean hidden, @NotNull String category) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.targetValue = targetValue;
        this.iconMaterial = iconMaterial;
        this.requirements = List.copyOf(requirements);
        this.hidden = hidden;
        this.category = category;
    }
    
    // Getters
    
    @NotNull
    public String getId() {
        return id;
    }
    
    @NotNull
    public String getName() {
        return name;
    }
    
    @NotNull
    public String getDescription() {
        return description;
    }
    
    public int getTargetValue() {
        return targetValue;
    }
    
    @NotNull
    public String getIconMaterial() {
        return iconMaterial;
    }
    
    @NotNull
    public List<String> getRequirements() {
        return requirements;
    }
    
    public boolean isHidden() {
        return hidden;
    }
    
    @NotNull
    public String getCategory() {
        return category;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Achievement that = (Achievement) obj;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "Achievement{" +
            "id='" + id + '\'' +
            ", name='" + name + '\'' +
            ", targetValue=" + targetValue +
            ", category='" + category + '\'' +
            '}';
    }
}
