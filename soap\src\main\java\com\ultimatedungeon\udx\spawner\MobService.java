package com.ultimatedungeon.udx.spawner;

import com.ultimatedungeon.udx.config.ConfigService;
import com.ultimatedungeon.udx.spawner.behavior.BasicBehaviors;
import com.ultimatedungeon.udx.spawner.behavior.MobBehavior;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import net.kyori.adventure.text.Component;
import org.bukkit.Location;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Mob;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.inventory.EntityEquipment;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.logging.Level;

/**
 * Service for managing custom mobs and spawners.
 * 
 * <p>This service handles the first-party mob system including
 * custom behaviors, AI, abilities, and spawning mechanics.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class MobService implements Listener {
    
    private final Plugin plugin;
    private final ConfigService configService;
    private final SchedulerUtil schedulerUtil;
    
    // Registries
    private final Map<String, MobDef> mobDefinitions = new ConcurrentHashMap<>();
    private final Map<String, MobBehavior> behaviors = new ConcurrentHashMap<>();
    private final Map<String, Spawner> spawners = new ConcurrentHashMap<>();
    
    // Runtime tracking
    private final Map<UUID, MobDef> spawnedMobs = new ConcurrentHashMap<>();
    private final Map<UUID, List<MobBehavior>> mobBehaviors = new ConcurrentHashMap<>();
    private final Map<String, Set<UUID>> spawnerMobs = new ConcurrentHashMap<>();
    
    // Statistics
    private int totalMobsSpawned = 0;
    private int totalMobsKilled = 0;
    private long totalSpawnTime = 0;
    
    public MobService(@NotNull Plugin plugin, @NotNull ConfigService configService, @NotNull SchedulerUtil schedulerUtil) {
        this.plugin = plugin;
        this.configService = configService;
        this.schedulerUtil = schedulerUtil;
    }
    
    /**
     * Initializes the mob service.
     */
    public void initialize() {
        // Register event listeners
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        
        // Register default behaviors
        registerDefaultBehaviors();
        
        // Load mob definitions from config
        loadMobDefinitions();
        
        // Start behavior update task
        startBehaviorUpdateTask();
        
        plugin.getLogger().info("Mob service initialized with " + mobDefinitions.size() + " mob definitions and " + behaviors.size() + " behaviors");
    }
    
    /**
     * Registers a mob definition.
     * 
     * @param mobDef the mob definition
     */
    public void registerMobDefinition(@NotNull MobDef mobDef) {
        mobDefinitions.put(mobDef.getId(), mobDef);
        plugin.getLogger().fine("Registered mob definition: " + mobDef.getId());
    }
    
    /**
     * Gets a mob definition by ID.
     * 
     * @param id the mob ID
     * @return the mob definition, or null if not found
     */
    @Nullable
    public MobDef getMobDefinition(@NotNull String id) {
        return mobDefinitions.get(id);
    }
    
    /**
     * Gets all mob definitions.
     * 
     * @return collection of all mob definitions
     */
    @NotNull
    public Collection<MobDef> getAllMobDefinitions() {
        return new ArrayList<>(mobDefinitions.values());
    }
    
    /**
     * Registers a behavior.
     * 
     * @param behavior the behavior
     */
    public void registerBehavior(@NotNull MobBehavior behavior) {
        behaviors.put(behavior.getId(), behavior);
        plugin.getLogger().fine("Registered behavior: " + behavior.getId());
    }
    
    /**
     * Gets a behavior by ID.
     * 
     * @param id the behavior ID
     * @return the behavior, or null if not found
     */
    @Nullable
    public MobBehavior getBehavior(@NotNull String id) {
        return behaviors.get(id);
    }
    
    /**
     * Spawns a mob from definition.
     * 
     * @param mobId the mob definition ID
     * @param location the spawn location
     * @return the spawned entity, or null if failed
     */
    @Nullable
    public LivingEntity spawnMob(@NotNull String mobId, @NotNull Location location) {
        return spawnMob(mobId, location, null);
    }
    
    /**
     * Spawns a mob from definition with parameters.
     * 
     * @param mobId the mob definition ID
     * @param location the spawn location
     * @param parameters additional spawn parameters
     * @return the spawned entity, or null if failed
     */
    @Nullable
    public LivingEntity spawnMob(@NotNull String mobId, @NotNull Location location, @Nullable Map<String, Object> parameters) {
        MobDef mobDef = mobDefinitions.get(mobId);
        if (mobDef == null) {
            plugin.getLogger().warning("Unknown mob definition: " + mobId);
            return null;
        }
        
        return spawnMob(mobDef, location, parameters);
    }
    
    /**
     * Spawns a mob from definition.
     * 
     * @param mobDef the mob definition
     * @param location the spawn location
     * @param parameters additional spawn parameters
     * @return the spawned entity, or null if failed
     */
    @Nullable
    public LivingEntity spawnMob(@NotNull MobDef mobDef, @NotNull Location location, @Nullable Map<String, Object> parameters) {
        long startTime = System.currentTimeMillis();
        
        try {
            // Spawn the entity
            Entity entity = location.getWorld().spawnEntity(location, mobDef.getEntityType());
            if (!(entity instanceof LivingEntity livingEntity)) {
                entity.remove();
                plugin.getLogger().warning("Mob definition " + mobDef.getId() + " does not spawn a living entity");
                return null;
            }
            
            // Apply mob definition
            applyMobDefinition(livingEntity, mobDef, parameters);
            
            // Track the mob
            spawnedMobs.put(livingEntity.getUniqueId(), mobDef);
            totalMobsSpawned++;
            totalSpawnTime += System.currentTimeMillis() - startTime;
            
            plugin.getLogger().fine("Spawned mob " + mobDef.getId() + " at " + location);
            return livingEntity;
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to spawn mob " + mobDef.getId(), e);
            return null;
        }
    }
    
    /**
     * Applies a mob definition to an entity.
     */
    private void applyMobDefinition(@NotNull LivingEntity entity, @NotNull MobDef mobDef, @Nullable Map<String, Object> parameters) {
        // Set display name using Adventure API
        entity.customName(Component.text(mobDef.getDisplayName()));
        entity.setCustomNameVisible(mobDef.hasFlag(MobDef.MobFlag.CUSTOM_NAME_VISIBLE));
        
        // Apply attributes
        for (Map.Entry<String, Object> entry : mobDef.getAttributes().entrySet()) {
            try {
                // Handle modern attribute names for Paper 1.21.x
                String attrName = entry.getKey();
                if (attrName.equals("GENERIC_MAX_HEALTH")) attrName = "MAX_HEALTH";
                if (attrName.equals("GENERIC_ATTACK_DAMAGE")) attrName = "ATTACK_DAMAGE";
                if (attrName.equals("GENERIC_MOVEMENT_SPEED")) attrName = "MOVEMENT_SPEED";
                
                Attribute attribute = Attribute.valueOf(attrName);
                if (entity.getAttribute(attribute) != null) {
                    double value = ((Number) entry.getValue()).doubleValue();
                    entity.getAttribute(attribute).setBaseValue(value);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to apply attribute " + entry.getKey() + " to mob " + mobDef.getId());
            }
        }
        
        // Apply equipment
        applyEquipment(entity, mobDef.getEquipment());
        
        // Apply potion effects
        for (PotionEffect effect : mobDef.getPotionEffects()) {
            entity.addPotionEffect(effect);
        }
        
        // Apply flags
        applyFlags(entity, mobDef.getFlags());
        
        // Apply behaviors
        applyBehaviors(entity, mobDef, parameters);
        
        // Mark as dungeon mob
        entity.setMetadata("udx_mob", new FixedMetadataValue(plugin, mobDef.getId()));
        entity.setMetadata("udx_spawn_time", new FixedMetadataValue(plugin, System.currentTimeMillis()));
    }
    
    /**
     * Applies equipment to an entity.
     */
    private void applyEquipment(@NotNull LivingEntity entity, @NotNull MobDef.Equipment equipment) {
        EntityEquipment entityEquipment = entity.getEquipment();
        if (entityEquipment == null) return;
        
        if (equipment.getHelmet() != null) {
            entityEquipment.setHelmet(equipment.getHelmet());
            entityEquipment.setHelmetDropChance(equipment.getDropChance("helmet"));
        }
        
        if (equipment.getChestplate() != null) {
            entityEquipment.setChestplate(equipment.getChestplate());
            entityEquipment.setChestplateDropChance(equipment.getDropChance("chestplate"));
        }
        
        if (equipment.getLeggings() != null) {
            entityEquipment.setLeggings(equipment.getLeggings());
            entityEquipment.setLeggingsDropChance(equipment.getDropChance("leggings"));
        }
        
        if (equipment.getBoots() != null) {
            entityEquipment.setBoots(equipment.getBoots());
            entityEquipment.setBootsDropChance(equipment.getDropChance("boots"));
        }
        
        if (equipment.getMainHand() != null) {
            entityEquipment.setItemInMainHand(equipment.getMainHand());
            entityEquipment.setItemInMainHandDropChance(equipment.getDropChance("mainHand"));
        }
        
        if (equipment.getOffHand() != null) {
            entityEquipment.setItemInOffHand(equipment.getOffHand());
            entityEquipment.setItemInOffHandDropChance(equipment.getDropChance("offHand"));
        }
    }
    
    /**
     * Applies flags to an entity.
     */
    private void applyFlags(@NotNull LivingEntity entity, @NotNull Set<MobDef.MobFlag> flags) {
        for (MobDef.MobFlag flag : flags) {
            switch (flag) {
                case FIRE_IMMUNE -> entity.setFireTicks(0);
                case SILENT -> entity.setSilent(true);
                case PERSISTENT -> entity.setPersistent(true);
                case NO_AI -> entity.setAI(false);
                case INVULNERABLE -> entity.setInvulnerable(true);
                case GLOWING -> entity.setGlowing(true);
                case INVISIBLE -> entity.setInvisible(true);
                case NO_GRAVITY -> entity.setGravity(false);
            }
        }
    }
    
    /**
     * Applies behaviors to an entity.
     */
    private void applyBehaviors(@NotNull LivingEntity entity, @NotNull MobDef mobDef, @Nullable Map<String, Object> parameters) {
        List<MobBehavior> entityBehaviors = new ArrayList<>();
        
        for (String behaviorId : mobDef.getBehaviors()) {
            MobBehavior behavior = behaviors.get(behaviorId);
            if (behavior != null) {
                Map<String, Object> behaviorParams = parameters != null ? parameters : Map.of();
                if (behavior.canApply(entity, behaviorParams)) {
                    behavior.apply(entity, behaviorParams);
                    entityBehaviors.add(behavior);
                }
            } else {
                plugin.getLogger().warning("Unknown behavior: " + behaviorId + " for mob " + mobDef.getId());
            }
        }
        
        // Sort by priority
        entityBehaviors.sort((a, b) -> Integer.compare(b.getPriority(), a.getPriority()));
        mobBehaviors.put(entity.getUniqueId(), entityBehaviors);
    }
    
    /**
     * Registers a spawner.
     * 
     * @param spawner the spawner
     */
    public void registerSpawner(@NotNull Spawner spawner) {
        spawners.put(spawner.getId(), spawner);
        spawnerMobs.put(spawner.getId(), new HashSet<>());
        plugin.getLogger().fine("Registered spawner: " + spawner.getId());
    }
    
    /**
     * Gets a spawner by ID.
     * 
     * @param id the spawner ID
     * @return the spawner, or null if not found
     */
    @Nullable
    public Spawner getSpawner(@NotNull String id) {
        return spawners.get(id);
    }
    
    /**
     * Activates a spawner.
     * 
     * @param spawnerId the spawner ID
     * @return true if activated successfully
     */
    public boolean activateSpawner(@NotNull String spawnerId) {
        Spawner spawner = spawners.get(spawnerId);
        if (spawner == null) {
            return false;
        }
        
        spawner.activate();
        processSpawner(spawner);
        return true;
    }
    
    /**
     * Processes a spawner's current wave.
     */
    private void processSpawner(@NotNull Spawner spawner) {
        if (!spawner.isActive() || spawner.isCompleted()) {
            return;
        }
        
        Spawner.Wave currentWave = spawner.getCurrentWave();
        if (currentWave == null) {
            spawner.complete();
            return;
        }
        
        // Check if we can spawn (concurrent mob limit)
        int aliveMobs = spawner.getAliveMobCount();
        if (aliveMobs >= spawner.getMaxConcurrentMobs()) {
            return; // Wait for mobs to die
        }
        
        // Spawn mobs from current wave
        for (Spawner.MobSpawn mobSpawn : currentWave.getMobSpawns()) {
            spawnFromMobSpawn(spawner, mobSpawn);
        }
        
        // Check if wave is complete and advance
        if (aliveMobs == 0 && spawner.getLastSpawnTime() > 0) {
            if (!spawner.nextWave()) {
                plugin.getLogger().fine("Spawner " + spawner.getId() + " completed all waves");
            }
        }
    }
    
    /**
     * Spawns mobs from a mob spawn definition.
     */
    private void spawnFromMobSpawn(@NotNull Spawner spawner, @NotNull Spawner.MobSpawn mobSpawn) {
        for (int i = 0; i < mobSpawn.getCount(); i++) {
            // Calculate spawn location with spread
            Location spawnLoc = calculateSpawnLocation(spawner.getLocation(), mobSpawn.getSpreadRadius());
            
            // Spawn the mob
            LivingEntity mob = spawnMob(mobSpawn.getMobId(), spawnLoc, mobSpawn.getParameters());
            if (mob != null) {
                // Track with spawner
                spawner.addSpawnedMob(mob.getUniqueId());
                spawnerMobs.get(spawner.getId()).add(mob.getUniqueId());
                
                // Apply elite status
                if (mobSpawn.isElite()) {
                    applyEliteStatus(mob);
                }
            }
        }
    }
    
    /**
     * Calculates a spawn location with spread.
     */
    @NotNull
    private Location calculateSpawnLocation(@NotNull Location center, double spreadRadius) {
        if (spreadRadius <= 0) {
            return center.clone();
        }
        
        double angle = ThreadLocalRandom.current().nextDouble() * 2 * Math.PI;
        double distance = ThreadLocalRandom.current().nextDouble() * spreadRadius;
        
        double x = center.getX() + Math.cos(angle) * distance;
        double z = center.getZ() + Math.sin(angle) * distance;
        
        return new Location(center.getWorld(), x, center.getY(), z);
    }
    
    /**
     * Applies elite status to a mob.
     */
    private void applyEliteStatus(@NotNull LivingEntity mob) {
        // Increase health and damage using modern attribute names
        if (mob.getAttribute(Attribute.MAX_HEALTH) != null) {
            double health = mob.getAttribute(Attribute.MAX_HEALTH).getBaseValue();
            mob.getAttribute(Attribute.MAX_HEALTH).setBaseValue(health * 1.5);
            mob.setHealth(health * 1.5);
        }
        
        if (mob.getAttribute(Attribute.ATTACK_DAMAGE) != null) {
            double damage = mob.getAttribute(Attribute.ATTACK_DAMAGE).getBaseValue();
            mob.getAttribute(Attribute.ATTACK_DAMAGE).setBaseValue(damage * 1.3);
        }
        
        // Add glowing effect
        mob.setGlowing(true);
        
        // Mark as elite
        mob.setMetadata("udx_elite", new FixedMetadataValue(plugin, true));
    }
    
    /**
     * Registers default behaviors.
     */
    private void registerDefaultBehaviors() {
        registerBehavior(new BasicBehaviors.RoamBehavior());
        registerBehavior(new BasicBehaviors.GuardBehavior());
        registerBehavior(new BasicBehaviors.LeapBehavior());
        registerBehavior(new BasicBehaviors.ChargeBehavior());
        registerBehavior(new BasicBehaviors.RangedBehavior());
    }
    
    /**
     * Loads mob definitions from configuration.
     */
    private void loadMobDefinitions() {
        // TODO: Load from config files
        // For now, create some basic definitions
        createDefaultMobDefinitions();
    }
    
    /**
     * Creates default mob definitions.
     */
    private void createDefaultMobDefinitions() {
        // Skeleton Archer
        MobDef skeletonArcher = MobDef.builder("skeleton_archer", EntityType.SKELETON)
            .displayName("Skeleton Archer")
            .attribute(Attribute.MAX_HEALTH, 25.0)
            .attribute(Attribute.MOVEMENT_SPEED, 0.3)
            .behavior("ranged")
            .behavior("guard")
            .targeting(MobDef.TargetingType.NEAREST)
            .leashRadius(12.0)
            .build();
        registerMobDefinition(skeletonArcher);
        
        // Zombie Brute
        MobDef zombieBrute = MobDef.builder("zombie_brute", EntityType.ZOMBIE)
            .displayName("Zombie Brute")
            .attribute(Attribute.MAX_HEALTH, 40.0)
            .attribute(Attribute.ATTACK_DAMAGE, 8.0)
            .attribute(Attribute.MOVEMENT_SPEED, 0.2)
            .behavior("charge")
            .behavior("roam")
            .targeting(MobDef.TargetingType.NEAREST)
            .flag(MobDef.MobFlag.KNOCKBACK_RESISTANT)
            .build();
        registerMobDefinition(zombieBrute);
        
        // Spider Leaper
        MobDef spiderLeaper = MobDef.builder("spider_leaper", EntityType.SPIDER)
            .displayName("Spider Leaper")
            .attribute(Attribute.MAX_HEALTH, 20.0)
            .attribute(Attribute.MOVEMENT_SPEED, 0.4)
            .behavior("leap")
            .behavior("roam")
            .targeting(MobDef.TargetingType.NEAREST)
            .build();
        registerMobDefinition(spiderLeaper);
    }
    
    /**
     * Starts the behavior update task.
     */
    private void startBehaviorUpdateTask() {
        schedulerUtil.runTaskTimer(() -> {
            for (Map.Entry<UUID, List<MobBehavior>> entry : mobBehaviors.entrySet()) {
                Entity entity = plugin.getServer().getEntity(entry.getKey());
                if (entity instanceof LivingEntity livingEntity && !livingEntity.isDead()) {
                    updateMobBehaviors(livingEntity, entry.getValue());
                } else {
                    // Clean up dead mob
                    mobBehaviors.remove(entry.getKey());
                    spawnedMobs.remove(entry.getKey());
                }
            }
            
            // Process active spawners
            for (Spawner spawner : spawners.values()) {
                if (spawner.isActive()) {
                    processSpawner(spawner);
                }
            }
        }, 20L, 20L); // Run every second
    }
    
    /**
     * Updates behaviors for a mob.
     */
    private void updateMobBehaviors(@NotNull LivingEntity entity, @NotNull List<MobBehavior> behaviors) {
        for (MobBehavior behavior : behaviors) {
            if (behavior.isActive(entity)) {
                behavior.update(entity, Map.of());
            }
        }
    }
    
    /**
     * Handles entity death events.
     */
    @EventHandler
    public void onEntityDeath(@NotNull EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        UUID entityId = entity.getUniqueId();
        
        // Check if it's a dungeon mob
        if (spawnedMobs.containsKey(entityId)) {
            totalMobsKilled++;
            
            // Clean up behaviors
            List<MobBehavior> entityBehaviors = mobBehaviors.remove(entityId);
            if (entityBehaviors != null) {
                for (MobBehavior behavior : entityBehaviors) {
                    behavior.remove(entity);
                }
            }
            
            // Remove from spawner tracking
            for (Set<UUID> spawnerMobSet : spawnerMobs.values()) {
                spawnerMobSet.remove(entityId);
            }
            
            // Remove from spawned mobs
            spawnedMobs.remove(entityId);
            
            // Use Adventure API for custom name
            Component customName = entity.customName();
            String nameStr = customName != null ? customName.toString() : "Unknown";
            plugin.getLogger().fine("Dungeon mob died: " + nameStr);
        }
    }
    
    /**
     * Gets service statistics.
     * 
     * @return map of statistics
     */
    @NotNull
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalMobsSpawned", totalMobsSpawned);
        stats.put("totalMobsKilled", totalMobsKilled);
        stats.put("aliveMobs", spawnedMobs.size());
        stats.put("averageSpawnTime", totalMobsSpawned > 0 ? (double) totalSpawnTime / totalMobsSpawned : 0.0);
        stats.put("mobDefinitions", mobDefinitions.size());
        stats.put("behaviors", behaviors.size());
        stats.put("activeSpawners", spawners.values().stream().mapToLong(s -> s.isActive() ? 1 : 0).sum());
        
        return stats;
    }
    
    /**
     * Shuts down the mob service.
     */
    public void shutdown() {
        // Despawn all dungeon mobs
        for (UUID mobId : new HashSet<>(spawnedMobs.keySet())) {
            Entity entity = plugin.getServer().getEntity(mobId);
            if (entity != null) {
                entity.remove();
            }
        }
        
        // Clear all data
        spawnedMobs.clear();
        mobBehaviors.clear();
        spawnerMobs.clear();
        
        plugin.getLogger().info("Mob service shut down (spawned " + totalMobsSpawned + " mobs, " + totalMobsKilled + " killed)");
    }
}
