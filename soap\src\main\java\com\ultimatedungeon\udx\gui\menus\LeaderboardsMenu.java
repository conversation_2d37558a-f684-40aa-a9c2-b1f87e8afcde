package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Leaderboards menu showing rankings and seasonal statistics.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class LeaderboardsMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    private String currentCategory = "overall";
    private String currentPeriod = "season";
    
    public LeaderboardsMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Leaderboards").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Category selection
        setupCategorySelection();
        
        // Period selection
        setupPeriodSelection();
        
        // Player's rank info
        setupPlayerRankInfo();
        
        // Top rankings
        setupTopRankings();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Main Menu").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(49, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMainMenu(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupCategorySelection() {
        // Overall Score
        ItemBuilder overallBuilder = new ItemBuilder(currentCategory.equals("overall") ? Material.GOLDEN_APPLE : Material.APPLE)
            .name(Component.text("Overall Score").color(currentCategory.equals("overall") ? NamedTextColor.GOLD : NamedTextColor.YELLOW)
                .decoration(TextDecoration.BOLD, currentCategory.equals("overall")))
            .lore(
                Component.text("Combined performance across all dungeons").color(NamedTextColor.GRAY),
                Component.empty(),
                currentCategory.equals("overall") ? 
                    Component.text("Currently Selected").color(NamedTextColor.GREEN) :
                    Component.text("Click to select").color(NamedTextColor.AQUA)
            );
        
        if (currentCategory.equals("overall")) {
            overallBuilder = overallBuilder.glow();
        }
        
        ItemStack overallItem = overallBuilder.build();
        
        setItem(10, overallItem, clickType -> {
            if (clickType == ClickType.LEFT && !currentCategory.equals("overall")) {
                currentCategory = "overall";
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Speed Runs
        ItemBuilder speedBuilder = new ItemBuilder(currentCategory.equals("speed") ? Material.GOLDEN_BOOTS : Material.LEATHER_BOOTS)
            .name(Component.text("Speed Runs").color(currentCategory.equals("speed") ? NamedTextColor.GOLD : NamedTextColor.YELLOW)
                .decoration(TextDecoration.BOLD, currentCategory.equals("speed")))
            .lore(
                Component.text("Fastest completion times").color(NamedTextColor.GRAY),
                Component.empty(),
                currentCategory.equals("speed") ? 
                    Component.text("Currently Selected").color(NamedTextColor.GREEN) :
                    Component.text("Click to select").color(NamedTextColor.AQUA)
            );
        
        if (currentCategory.equals("speed")) {
            speedBuilder = speedBuilder.glow();
        }
        
        ItemStack speedItem = speedBuilder.build();
        
        setItem(11, speedItem, clickType -> {
            if (clickType == ClickType.LEFT && !currentCategory.equals("speed")) {
                currentCategory = "speed";
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Completions
        ItemBuilder completionsBuilder = new ItemBuilder(currentCategory.equals("completions") ? Material.GOLDEN_SWORD : Material.IRON_SWORD)
            .name(Component.text("Completions").color(currentCategory.equals("completions") ? NamedTextColor.GOLD : NamedTextColor.YELLOW)
                .decoration(TextDecoration.BOLD, currentCategory.equals("completions")))
            .lore(
                Component.text("Total dungeon completions").color(NamedTextColor.GRAY),
                Component.empty(),
                currentCategory.equals("completions") ? 
                    Component.text("Currently Selected").color(NamedTextColor.GREEN) :
                    Component.text("Click to select").color(NamedTextColor.AQUA)
            );
        
        if (currentCategory.equals("completions")) {
            completionsBuilder = completionsBuilder.glow();
        }
        
        ItemStack completionsItem = completionsBuilder.build();
        
        setItem(12, completionsItem, clickType -> {
            if (clickType == ClickType.LEFT && !currentCategory.equals("completions")) {
                currentCategory = "completions";
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Boss Kills
        ItemBuilder bossKillsBuilder = new ItemBuilder(currentCategory.equals("boss_kills") ? Material.NETHERITE_SWORD : Material.DIAMOND_SWORD)
            .name(Component.text("Boss Kills").color(currentCategory.equals("boss_kills") ? NamedTextColor.GOLD : NamedTextColor.YELLOW)
                .decoration(TextDecoration.BOLD, currentCategory.equals("boss_kills")))
            .lore(
                Component.text("Total boss defeats").color(NamedTextColor.GRAY),
                Component.empty(),
                currentCategory.equals("boss_kills") ? 
                    Component.text("Currently Selected").color(NamedTextColor.GREEN) :
                    Component.text("Click to select").color(NamedTextColor.AQUA)
            );
        
        if (currentCategory.equals("boss_kills")) {
            bossKillsBuilder = bossKillsBuilder.glow();
        }
        
        ItemStack bossKillsItem = bossKillsBuilder.build();
        
        setItem(13, bossKillsItem, clickType -> {
            if (clickType == ClickType.LEFT && !currentCategory.equals("boss_kills")) {
                currentCategory = "boss_kills";
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
    }
    
    private void setupPeriodSelection() {
        // Season
        ItemBuilder seasonBuilder = new ItemBuilder(currentPeriod.equals("season") ? Material.CLOCK : Material.COMPASS)
            .name(Component.text("This Season").color(currentPeriod.equals("season") ? NamedTextColor.AQUA : NamedTextColor.GRAY)
                .decoration(TextDecoration.BOLD, currentPeriod.equals("season")))
            .lore(
                Component.text("Current season rankings").color(NamedTextColor.GRAY),
                Component.text("Season 3: Winter Trials").color(NamedTextColor.YELLOW),
                Component.text("Ends in: 23 days").color(NamedTextColor.GRAY),
                Component.empty(),
                currentPeriod.equals("season") ? 
                    Component.text("Currently Selected").color(NamedTextColor.GREEN) :
                    Component.text("Click to select").color(NamedTextColor.AQUA)
            );
        
        if (currentPeriod.equals("season")) {
            seasonBuilder = seasonBuilder.glow();
        }
        
        ItemStack seasonItem = seasonBuilder.build();
        
        setItem(15, seasonItem, clickType -> {
            if (clickType == ClickType.LEFT && !currentPeriod.equals("season")) {
                currentPeriod = "season";
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // All Time
        ItemBuilder allTimeBuilder = new ItemBuilder(currentPeriod.equals("all_time") ? Material.NETHERITE_INGOT : Material.IRON_INGOT)
            .name(Component.text("All Time").color(currentPeriod.equals("all_time") ? NamedTextColor.AQUA : NamedTextColor.GRAY)
                .decoration(TextDecoration.BOLD, currentPeriod.equals("all_time")))
            .lore(
                Component.text("Historical rankings").color(NamedTextColor.GRAY),
                Component.text("Since server launch").color(NamedTextColor.YELLOW),
                Component.empty(),
                currentPeriod.equals("all_time") ? 
                    Component.text("Currently Selected").color(NamedTextColor.GREEN) :
                    Component.text("Click to select").color(NamedTextColor.AQUA)
            );
        
        if (currentPeriod.equals("all_time")) {
            allTimeBuilder = allTimeBuilder.glow();
        }
        
        ItemStack allTimeItem = allTimeBuilder.build();
        
        setItem(16, allTimeItem, clickType -> {
            if (clickType == ClickType.LEFT && !currentPeriod.equals("all_time")) {
                currentPeriod = "all_time";
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
    }
    
    private void setupPlayerRankInfo() {
        // Player's current rank
        ItemStack playerRankItem = new ItemBuilder(Material.PLAYER_HEAD)
            .name(Component.text("Your Rank").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Player: " + player.getName()).color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Current Rank: #156").color(NamedTextColor.AQUA),
                Component.text("Score: 2,847").color(NamedTextColor.GRAY),
                Component.text("Percentile: Top 15%").color(NamedTextColor.GREEN),
                Component.empty(),
                Component.text("Recent Change: ↑12").color(NamedTextColor.GREEN),
                Component.text("Peak Rank: #89").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(22, playerRankItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Keep playing to improve your rank!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
    }
    
    private void setupTopRankings() {
        // Generate sample rankings based on current category and period
        List<RankingEntry> rankings = generateSampleRankings();
        
        // Display top 10 rankings
        for (int i = 0; i < Math.min(rankings.size(), 10); i++) {
            RankingEntry entry = rankings.get(i);
            int slot = 28 + (i % 7) + (i / 7) * 9; // Arrange in rows
            
            Material material = switch (i) {
                case 0 -> Material.GOLD_INGOT;
                case 1 -> Material.IRON_INGOT;
                case 2 -> Material.COPPER_INGOT;
                default -> Material.COAL;
            };
            
            NamedTextColor nameColor = switch (i) {
                case 0 -> NamedTextColor.GOLD;
                case 1 -> NamedTextColor.GRAY;
                case 2 -> NamedTextColor.DARK_AQUA;
                default -> NamedTextColor.WHITE;
            };
            
            List<Component> lore = new ArrayList<>();
            lore.add(Component.text("Rank: #" + (i + 1)).color(NamedTextColor.YELLOW));
            lore.add(Component.text("Player: " + entry.playerName()).color(nameColor));
            lore.add(Component.empty());
            
            switch (currentCategory) {
                case "overall" -> {
                    lore.add(Component.text("Score: " + entry.score()).color(NamedTextColor.AQUA));
                    lore.add(Component.text("Completions: " + entry.completions()).color(NamedTextColor.GRAY));
                }
                case "speed" -> {
                    lore.add(Component.text("Best Time: " + entry.bestTime()).color(NamedTextColor.AQUA));
                    lore.add(Component.text("Dungeon: " + entry.dungeon()).color(NamedTextColor.GRAY));
                }
                case "completions" -> {
                    lore.add(Component.text("Completions: " + entry.completions()).color(NamedTextColor.AQUA));
                    lore.add(Component.text("Success Rate: " + entry.successRate() + "%").color(NamedTextColor.GRAY));
                }
                case "boss_kills" -> {
                    lore.add(Component.text("Boss Kills: " + entry.bossKills()).color(NamedTextColor.AQUA));
                    lore.add(Component.text("Favorite: " + entry.favoriteBoss()).color(NamedTextColor.GRAY));
                }
            }
            
            if (entry.playerName().equals(player.getName())) {
                lore.add(Component.empty());
                lore.add(Component.text("← This is you!").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true));
            }
            
            ItemBuilder rankingBuilder = new ItemBuilder(material)
                .name(Component.text("#" + (i + 1) + " " + entry.playerName()).color(nameColor).decoration(TextDecoration.BOLD, true))
                .lore(lore);
            
            if (entry.playerName().equals(player.getName())) {
                rankingBuilder = rankingBuilder.glow();
            }
            
            ItemStack rankingItem = rankingBuilder.build();
            
            setItem(slot, rankingItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    player.sendMessage(Component.text("Viewing " + entry.playerName() + "'s stats")
                        .color(NamedTextColor.YELLOW));
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                }
            });
        }
    }
    
    private List<RankingEntry> generateSampleRankings() {
        // Generate sample data based on current category and period
        List<RankingEntry> rankings = new ArrayList<>();
        
        String[] sampleNames = {
            "DragonSlayer99", "MysticMage", "ShadowHunter", "IronWill", "StormBreaker",
            "CrystalKnight", "VoidWalker", "FlameGuard", "FrostBite", "ThunderStrike",
            player.getName() // Include the current player
        };
        
        for (int i = 0; i < sampleNames.length; i++) {
            String name = sampleNames[i];
            int baseScore = 5000 - (i * 200) + (int)(Math.random() * 100);
            
            rankings.add(new RankingEntry(
                name,
                baseScore,
                baseScore / 50 + (int)(Math.random() * 20),
                formatTime(600 + (i * 30) + (int)(Math.random() * 60)),
                "Crypt of Echoes",
                85 + (int)(Math.random() * 15),
                baseScore / 10 + (int)(Math.random() * 50),
                "Ancient Lich"
            ));
        }
        
        // Sort by score (descending)
        rankings.sort((a, b) -> Integer.compare(b.score(), a.score()));
        
        return rankings;
    }
    
    private String formatTime(int seconds) {
        int minutes = seconds / 60;
        int secs = seconds % 60;
        return String.format("%d:%02d", minutes, secs);
    }
    
    /**
     * Record representing a leaderboard entry.
     */
    private record RankingEntry(
        String playerName,
        int score,
        int completions,
        String bestTime,
        String dungeon,
        int successRate,
        int bossKills,
        String favoriteBoss
    ) {}
}
