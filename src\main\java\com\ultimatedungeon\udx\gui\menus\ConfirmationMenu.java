package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.gui.MenuRegistry;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * A confirmation dialog menu for confirming dangerous or important actions.
 * 
 * <p>This menu presents the user with a confirmation message and Yes/No options.
 * It's used throughout the plugin for actions that need user confirmation.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class ConfirmationMenu extends Menu {
    
    private final Component message;
    private final Runnable onConfirm;
    private final Menu parentMenu;
    
    /**
     * Creates a new confirmation menu.
     * 
     * @param player the player viewing the menu
     * @param message the confirmation message to display
     * @param onConfirm the action to run when confirmed
     * @param parentMenu the parent menu to return to
     * @param menuRegistry the menu registry
     */
    public ConfirmationMenu(@NotNull Player player, @NotNull Component message, @NotNull Runnable onConfirm, 
                           @NotNull Menu parentMenu, @NotNull MenuRegistry menuRegistry) {
        super(player, Component.text("Confirm Action").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true), 
              27, menuRegistry);
        this.message = message;
        this.onConfirm = onConfirm;
        this.parentMenu = parentMenu;
        setParent(parentMenu);
    }
    
    @Override
    protected void setupMenu() {
        // Clear inventory
        inventory.clear();
        clickHandlers.clear();
        
        // Display message item
        ItemStack messageItem = new ItemBuilder(Material.PAPER)
            .name(Component.text("Confirmation Required").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.empty(),
                message,
                Component.empty(),
                Component.text("Please confirm your action below").color(NamedTextColor.GRAY)
            )
            .build();
        
        setItem(13, messageItem);
        
        // Confirm button (Yes)
        ItemStack confirmItem = new ItemBuilder(Material.LIME_CONCRETE)
            .name(Component.text("✓ YES").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Click to confirm the action").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This action cannot be undone!").color(NamedTextColor.RED)
            )
            .glow()
            .build();
        
        setItem(11, confirmItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                try {
                    onConfirm.run();
                    player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
                    
                    // Return to parent menu
                    if (parentMenu != null) {
                        parentMenu.open();
                    } else {
                        close();
                    }
                } catch (Exception e) {
                    menuRegistry.getPlugin().getLogger().severe("Error executing confirmation action: " + e.getMessage());
                    e.printStackTrace();
                    
                    player.sendMessage(Component.text("An error occurred while executing the action.")
                        .color(NamedTextColor.RED));
                    
                    // Still return to parent menu
                    if (parentMenu != null) {
                        parentMenu.open();
                    } else {
                        close();
                    }
                }
            }
        });
        
        // Cancel button (No)
        ItemStack cancelItem = new ItemBuilder(Material.RED_CONCRETE)
            .name(Component.text("✗ NO").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Click to cancel the action").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Return to the previous menu").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(15, cancelItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
                
                // Return to parent menu
                if (parentMenu != null) {
                    parentMenu.open();
                } else {
                    close();
                }
            }
        });
        
        // Fill empty slots with glass panes
        fillEmptySlots();
        
        // Override the default navigation since we have custom behavior
        // Back button (same as cancel)
        setItem(size - 9, createBackButton(), clickType -> {
            if (clickType == ClickType.LEFT) {
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
                
                if (parentMenu != null) {
                    parentMenu.open();
                } else {
                    close();
                }
            }
        });
        
        // Close button
        setItem(size - 1, createCloseButton(), clickType -> {
            if (clickType == ClickType.LEFT) {
                close();
            }
        });
    }
    
    @Override
    protected void onOpen() {
        super.onOpen();
        
        // Play a warning sound to indicate this is an important action
        player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_BELL, 1.0f, 1.0f);
    }
}
