package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Spectate menu for viewing active dungeon runs.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class SpectateMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public SpectateMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Spectate Dungeons").color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true), 
              45, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Active runs section
        setupActiveRuns();
        
        // Spectate options
        setupSpectateOptions();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Main Menu").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(40, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMainMenu(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupActiveRuns() {
        // Header
        ItemStack headerItem = new ItemBuilder(Material.SPYGLASS)
            .name(Component.text("Active Dungeon Runs").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Choose a dungeon run to spectate").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("You can watch other players' adventures").color(NamedTextColor.YELLOW),
                Component.text("without interfering with their gameplay").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(4, headerItem, clickType -> {
            // No action for header
        });
        
        // Sample active runs
        List<ActiveRun> activeRuns = generateSampleActiveRuns();
        
        int[] runSlots = {10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25};
        
        for (int i = 0; i < Math.min(activeRuns.size(), runSlots.length); i++) {
            ActiveRun run = activeRuns.get(i);
            int slot = runSlots[i];
            
            Material material = switch (run.dungeon()) {
                case "Crypt of Echoes" -> Material.SKELETON_SKULL;
                case "Ember Foundry" -> Material.BLAZE_POWDER;
                case "Skyfane Spire" -> Material.FEATHER;
                default -> Material.COMPASS;
            };
            
            NamedTextColor difficultyColor = switch (run.difficulty()) {
                case "Normal" -> NamedTextColor.GREEN;
                case "Hard" -> NamedTextColor.YELLOW;
                case "Mythic" -> NamedTextColor.RED;
                case "Nightmare" -> NamedTextColor.DARK_PURPLE;
                default -> NamedTextColor.WHITE;
            };
            
            List<Component> lore = new ArrayList<>();
            lore.add(Component.text("Dungeon: " + run.dungeon()).color(NamedTextColor.AQUA));
            lore.add(Component.text("Difficulty: " + run.difficulty()).color(difficultyColor));
            lore.add(Component.text("Progress: " + run.progress()).color(NamedTextColor.GRAY));
            lore.add(Component.text("Runtime: " + run.runtime()).color(NamedTextColor.GRAY));
            lore.add(Component.empty());
            lore.add(Component.text("Party Members:").color(NamedTextColor.YELLOW));
            
            for (String member : run.members()) {
                lore.add(Component.text("  • " + member).color(NamedTextColor.WHITE));
            }
            
            lore.add(Component.empty());
            
            if (run.allowSpectators()) {
                lore.add(Component.text("✓ Spectators Allowed").color(NamedTextColor.GREEN));
                lore.add(Component.text("Click to spectate").color(NamedTextColor.AQUA));
            } else {
                lore.add(Component.text("✗ Private Run").color(NamedTextColor.RED));
                lore.add(Component.text("Spectating not allowed").color(NamedTextColor.GRAY));
            }
            
            ItemBuilder runBuilder = new ItemBuilder(material)
                .name(Component.text(run.partyLeader() + "'s Party").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
                .lore(lore);
            
            if (run.allowSpectators()) {
                runBuilder = runBuilder.glow();
            }
            
            ItemStack runItem = runBuilder.build();
            
            setItem(slot, runItem, clickType -> {
                if (clickType == ClickType.LEFT && run.allowSpectators()) {
                    // Start spectating
                    player.sendMessage(Component.text("Starting spectate mode for " + run.partyLeader() + "'s party...")
                        .color(NamedTextColor.GREEN));
                    player.sendMessage(Component.text("Use /udx to exit spectate mode")
                        .color(NamedTextColor.YELLOW));
                    player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.2f);
                    close();
                } else if (clickType == ClickType.LEFT) {
                    player.sendMessage(Component.text("This run is private and doesn't allow spectators")
                        .color(NamedTextColor.RED));
                    player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                }
            });
        }
        
        // No active runs message if empty
        if (activeRuns.isEmpty()) {
            ItemStack noRunsItem = new ItemBuilder(Material.BARRIER)
                .name(Component.text("No Active Runs").color(NamedTextColor.RED))
                .lore(
                    Component.text("There are currently no dungeon runs").color(NamedTextColor.GRAY),
                    Component.text("available for spectating.").color(NamedTextColor.GRAY),
                    Component.empty(),
                    Component.text("Check back later!").color(NamedTextColor.YELLOW)
                )
                .build();
            
            setItem(22, noRunsItem, clickType -> {
                // No action
            });
        }
    }
    
    private void setupSpectateOptions() {
        // Spectate settings
        ItemStack settingsItem = new ItemBuilder(Material.REDSTONE)
            .name(Component.text("Spectate Settings").color(NamedTextColor.YELLOW))
            .lore(
                Component.text("Configure your spectate preferences").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("• Spectate notifications: ON").color(NamedTextColor.GREEN),
                Component.text("• Auto-follow players: OFF").color(NamedTextColor.RED),
                Component.text("• Show player names: ON").color(NamedTextColor.GREEN),
                Component.text("• Hide spectate UI: OFF").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("Click to toggle settings").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(37, settingsItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Spectate settings updated!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                refresh();
            }
        });
        
        // Refresh runs
        ItemStack refreshItem = new ItemBuilder(Material.CLOCK)
            .name(Component.text("Refresh Runs").color(NamedTextColor.AQUA))
            .lore(
                Component.text("Update the list of active runs").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to refresh").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(43, refreshItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Refreshing active runs...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
    }
    
    private List<ActiveRun> generateSampleActiveRuns() {
        List<ActiveRun> runs = new ArrayList<>();
        
        // Sample active runs
        runs.add(new ActiveRun(
            "DragonSlayer99",
            "Crypt of Echoes",
            "Hard",
            "Room 3/7",
            "12:34",
            List.of("DragonSlayer99", "MysticMage", "ShadowHunter"),
            true
        ));
        
        runs.add(new ActiveRun(
            "IronWill",
            "Ember Foundry",
            "Mythic",
            "Boss Fight",
            "23:45",
            List.of("IronWill", "StormBreaker"),
            true
        ));
        
        runs.add(new ActiveRun(
            "CrystalKnight",
            "Skyfane Spire",
            "Normal",
            "Room 1/5",
            "05:12",
            List.of("CrystalKnight", "VoidWalker", "FlameGuard", "FrostBite"),
            false
        ));
        
        runs.add(new ActiveRun(
            "ThunderStrike",
            "Crypt of Echoes",
            "Nightmare",
            "Room 5/7",
            "34:56",
            List.of("ThunderStrike"),
            true
        ));
        
        return runs;
    }
    
    /**
     * Record representing an active dungeon run.
     */
    private record ActiveRun(
        String partyLeader,
        String dungeon,
        String difficulty,
        String progress,
        String runtime,
        List<String> members,
        boolean allowSpectators
    ) {}
}
