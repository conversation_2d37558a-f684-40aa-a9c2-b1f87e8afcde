package com.ultimatedungeon.udx.gen;

import com.ultimatedungeon.udx.room.RoomTemplate;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * ApexDungeons-style world generation system.
 * 
 * <p>This generator creates dungeons using a room-based approach similar to ApexDungeons,
 * with procedural layout generation and room template placement.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class ApexStyleGenerator {
    
    private final Plugin plugin;
    private final Random random;
    
    // Generation settings
    private int maxRooms = 25;
    private double branchChance = 0.3;
    private int roomSpacing = 5;
    private int blocksPerTick = 8000;
    
    public ApexStyleGenerator(@NotNull Plugin plugin) {
        this.plugin = plugin;
        this.random = new Random();
    }
    
    /**
     * Generates a dungeon in the specified world.
     * 
     * @param world the target world
     * @param startX starting X coordinate
     * @param startY starting Y coordinate
     * @param startZ starting Z coordinate
     * @param seed generation seed
     * @return future that completes when generation is done
     */
    @NotNull
    public CompletableFuture<DungeonGenerationResult> generateDungeon(
            @NotNull World world, int startX, int startY, int startZ, long seed) {
        
        CompletableFuture<DungeonGenerationResult> future = new CompletableFuture<>();
        
        // Generate layout off-thread
        CompletableFuture.supplyAsync(() -> {
            random.setSeed(seed);
            return generateLayout(startX, startY, startZ);
        }).thenAccept(layout -> {
            // Execute block placement on main thread
            org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
                try {
                    // Step 2: Place rooms (on main thread)
                    placeRooms(world, layout);
                    
                    // Step 3: Connect rooms (on main thread)
                    connectRooms(world, layout);
                    
                    // Step 4: Add details (on main thread)
                    addDungeonDetails(world, layout);
                    
                    future.complete(new DungeonGenerationResult(true, layout, "Generation completed successfully"));
                    
                } catch (Exception e) {
                    plugin.getLogger().severe("Error generating dungeon: " + e.getMessage());
                    e.printStackTrace();
                    future.complete(new DungeonGenerationResult(false, null, "Generation failed: " + e.getMessage()));
                }
            });
        }).exceptionally(throwable -> {
            plugin.getLogger().severe("Error in dungeon generation: " + throwable.getMessage());
            throwable.printStackTrace();
            future.complete(new DungeonGenerationResult(false, null, "Generation failed: " + throwable.getMessage()));
            return null;
        });
        
        return future;
    }
    
    /**
     * Generates the dungeon layout plan.
     */
    @NotNull
    private DungeonLayoutPlan generateLayout(int startX, int startY, int startZ) {
        DungeonLayoutPlan layout = new DungeonLayoutPlan();
        
        // Create entrance room
        RoomPlacement entrance = new RoomPlacement(
            0, "entrance", startX, startY, startZ, 15, 7, 15
        );
        layout.addRoom(entrance);
        
        // Generate main path
        generateMainPath(layout, entrance);
        
        // Add branches
        addBranches(layout);
        
        // Add boss room at the end
        addBossRoom(layout);
        
        plugin.getLogger().info("Generated layout with " + layout.getRooms().size() + " rooms");
        return layout;
    }
    
    /**
     * Generates the main path through the dungeon.
     */
    private void generateMainPath(@NotNull DungeonLayoutPlan layout, @NotNull RoomPlacement start) {
        RoomPlacement current = start;
        int roomCount = 1;
        
        while (roomCount < maxRooms && roomCount < 8) { // Main path limit
            // Choose direction for next room
            Direction direction = chooseDirection(layout, current);
            if (direction == null) break;
            
            // Calculate position for next room
            int[] nextPos = calculateNextPosition(current, direction);
            
            // Create next room
            RoomPlacement nextRoom = new RoomPlacement(
                roomCount, 
                getRandomRoomType(),
                nextPos[0], nextPos[1], nextPos[2],
                getRoomWidth(), getRoomHeight(), getRoomDepth()
            );
            
            layout.addRoom(nextRoom);
            layout.addConnection(current.getId(), nextRoom.getId(), direction);
            
            current = nextRoom;
            roomCount++;
        }
    }
    
    /**
     * Adds branch rooms to the layout.
     */
    private void addBranches(@NotNull DungeonLayoutPlan layout) {
        List<RoomPlacement> existingRooms = new ArrayList<>(layout.getRooms());
        
        for (RoomPlacement room : existingRooms) {
            if (layout.getRooms().size() >= maxRooms) break;
            
            if (random.nextDouble() < branchChance) {
                // Try to add a branch
                Direction direction = chooseDirection(layout, room);
                if (direction != null) {
                    int[] branchPos = calculateNextPosition(room, direction);
                    
                    RoomPlacement branchRoom = new RoomPlacement(
                        layout.getRooms().size(),
                        getRandomRoomType(),
                        branchPos[0], branchPos[1], branchPos[2],
                        getRoomWidth(), getRoomHeight(), getRoomDepth()
                    );
                    
                    layout.addRoom(branchRoom);
                    layout.addConnection(room.getId(), branchRoom.getId(), direction);
                }
            }
        }
    }
    
    /**
     * Adds a boss room to the layout.
     */
    private void addBossRoom(@NotNull DungeonLayoutPlan layout) {
        // Find the room furthest from entrance
        RoomPlacement furthest = layout.getRooms().stream()
            .max(Comparator.comparingDouble(room -> 
                Math.sqrt(Math.pow(room.getX(), 2) + Math.pow(room.getZ(), 2))))
            .orElse(null);
        
        if (furthest != null) {
            Direction direction = chooseDirection(layout, furthest);
            if (direction != null) {
                int[] bossPos = calculateNextPosition(furthest, direction);
                
                RoomPlacement bossRoom = new RoomPlacement(
                    layout.getRooms().size(),
                    "boss_room",
                    bossPos[0], bossPos[1], bossPos[2],
                    21, 9, 21 // Larger boss room
                );
                
                layout.addRoom(bossRoom);
                layout.addConnection(furthest.getId(), bossRoom.getId(), direction);
            }
        }
    }
    
    /**
     * Places rooms in the world.
     */
    private void placeRooms(@NotNull World world, @NotNull DungeonLayoutPlan layout) {
        for (RoomPlacement room : layout.getRooms()) {
            placeRoom(world, room);
        }
    }
    
    /**
     * Places a single room in the world.
     */
    private void placeRoom(@NotNull World world, @NotNull RoomPlacement room) {
        int startX = room.getX();
        int startY = room.getY();
        int startZ = room.getZ();
        int width = room.getWidth();
        int height = room.getHeight();
        int depth = room.getDepth();
        
        // Clear the area
        for (int x = startX; x < startX + width; x++) {
            for (int y = startY; y < startY + height; y++) {
                for (int z = startZ; z < startZ + depth; z++) {
                    world.getBlockAt(x, y, z).setType(Material.AIR);
                }
            }
        }
        
        // Create floor
        for (int x = startX; x < startX + width; x++) {
            for (int z = startZ; z < startZ + depth; z++) {
                world.getBlockAt(x, startY, z).setType(getFloorMaterial(room.getType()));
            }
        }
        
        // Create walls
        createWalls(world, room);
        
        // Create ceiling
        for (int x = startX; x < startX + width; x++) {
            for (int z = startZ; z < startZ + depth; z++) {
                world.getBlockAt(x, startY + height - 1, z).setType(getCeilingMaterial(room.getType()));
            }
        }
        
        // Add room-specific features
        addRoomFeatures(world, room);
    }
    
    /**
     * Creates walls for a room.
     */
    private void createWalls(@NotNull World world, @NotNull RoomPlacement room) {
        int startX = room.getX();
        int startY = room.getY();
        int startZ = room.getZ();
        int width = room.getWidth();
        int height = room.getHeight();
        int depth = room.getDepth();
        
        Material wallMaterial = getWallMaterial(room.getType());
        
        // North and South walls
        for (int x = startX; x < startX + width; x++) {
            for (int y = startY + 1; y < startY + height - 1; y++) {
                world.getBlockAt(x, y, startZ).setType(wallMaterial);
                world.getBlockAt(x, y, startZ + depth - 1).setType(wallMaterial);
            }
        }
        
        // East and West walls
        for (int z = startZ; z < startZ + depth; z++) {
            for (int y = startY + 1; y < startY + height - 1; y++) {
                world.getBlockAt(startX, y, z).setType(wallMaterial);
                world.getBlockAt(startX + width - 1, y, z).setType(wallMaterial);
            }
        }
    }
    
    /**
     * Connects rooms with corridors.
     */
    private void connectRooms(@NotNull World world, @NotNull DungeonLayoutPlan layout) {
        for (RoomConnection connection : layout.getConnections()) {
            RoomPlacement room1 = layout.getRoom(connection.getRoom1Id());
            RoomPlacement room2 = layout.getRoom(connection.getRoom2Id());
            
            if (room1 != null && room2 != null) {
                createCorridor(world, room1, room2, connection.getDirection());
            }
        }
    }
    
    /**
     * Creates a corridor between two rooms.
     */
    private void createCorridor(@NotNull World world, @NotNull RoomPlacement room1, 
                               @NotNull RoomPlacement room2, @NotNull Direction direction) {
        // Calculate corridor path
        int[] start = getConnectionPoint(room1, direction);
        int[] end = getConnectionPoint(room2, direction.opposite());
        
        // Create simple straight corridor
        createStraightCorridor(world, start, end);
    }
    
    /**
     * Creates a straight corridor between two points.
     */
    private void createStraightCorridor(@NotNull World world, int[] start, int[] end) {
        int startX = start[0], startY = start[1], startZ = start[2];
        int endX = end[0], endY = end[1], endZ = end[2];
        
        // Simple corridor - just clear blocks and add floor
        int minX = Math.min(startX, endX);
        int maxX = Math.max(startX, endX);
        int minZ = Math.min(startZ, endZ);
        int maxZ = Math.max(startZ, endZ);
        
        for (int x = minX; x <= maxX; x++) {
            for (int z = minZ; z <= maxZ; z++) {
                // Clear corridor
                for (int y = startY; y < startY + 3; y++) {
                    world.getBlockAt(x, y, z).setType(Material.AIR);
                }
                // Add floor
                world.getBlockAt(x, startY, z).setType(Material.STONE_BRICKS);
            }
        }
    }
    
    /**
     * Adds dungeon-specific details like lighting and decorations.
     */
    private void addDungeonDetails(@NotNull World world, @NotNull DungeonLayoutPlan layout) {
        for (RoomPlacement room : layout.getRooms()) {
            addLighting(world, room);
            addDecorations(world, room);
        }
    }
    
    /**
     * Adds lighting to a room.
     */
    private void addLighting(@NotNull World world, @NotNull RoomPlacement room) {
        int centerX = room.getX() + room.getWidth() / 2;
        int centerY = room.getY() + 2;
        int centerZ = room.getZ() + room.getDepth() / 2;
        
        // Add central light source
        world.getBlockAt(centerX, centerY, centerZ).setType(Material.GLOWSTONE);
        
        // Add corner torches
        int x1 = room.getX() + 2;
        int x2 = room.getX() + room.getWidth() - 3;
        int z1 = room.getZ() + 2;
        int z2 = room.getZ() + room.getDepth() - 3;
        int y = room.getY() + 2;
        
        world.getBlockAt(x1, y, z1).setType(Material.TORCH);
        world.getBlockAt(x1, y, z2).setType(Material.TORCH);
        world.getBlockAt(x2, y, z1).setType(Material.TORCH);
        world.getBlockAt(x2, y, z2).setType(Material.TORCH);
    }
    
    // Helper methods
    
    private Direction chooseDirection(@NotNull DungeonLayoutPlan layout, @NotNull RoomPlacement room) {
        List<Direction> available = new ArrayList<>(Arrays.asList(Direction.values()));
        available.removeIf(dir -> wouldOverlap(layout, room, dir));
        
        return available.isEmpty() ? null : available.get(random.nextInt(available.size()));
    }
    
    private boolean wouldOverlap(@NotNull DungeonLayoutPlan layout, @NotNull RoomPlacement room, @NotNull Direction direction) {
        int[] nextPos = calculateNextPosition(room, direction);
        int width = getRoomWidth();
        int depth = getRoomDepth();
        
        for (RoomPlacement existing : layout.getRooms()) {
            if (roomsOverlap(nextPos[0], nextPos[2], width, depth,
                           existing.getX(), existing.getZ(), existing.getWidth(), existing.getDepth())) {
                return true;
            }
        }
        return false;
    }
    
    private boolean roomsOverlap(int x1, int z1, int w1, int d1, int x2, int z2, int w2, int d2) {
        return !(x1 + w1 <= x2 || x2 + w2 <= x1 || z1 + d1 <= z2 || z2 + d2 <= z1);
    }
    
    private int[] calculateNextPosition(@NotNull RoomPlacement room, @NotNull Direction direction) {
        int x = room.getX();
        int y = room.getY();
        int z = room.getZ();
        
        switch (direction) {
            case NORTH -> z -= (getRoomDepth() + roomSpacing);
            case SOUTH -> z += (room.getDepth() + roomSpacing);
            case EAST -> x += (room.getWidth() + roomSpacing);
            case WEST -> x -= (getRoomWidth() + roomSpacing);
        }
        
        return new int[]{x, y, z};
    }
    
    private int[] getConnectionPoint(@NotNull RoomPlacement room, @NotNull Direction direction) {
        int x = room.getX();
        int y = room.getY() + 1;
        int z = room.getZ();
        
        switch (direction) {
            case NORTH -> {
                x += room.getWidth() / 2;
                // z stays at room.getZ()
            }
            case SOUTH -> {
                x += room.getWidth() / 2;
                z += room.getDepth() - 1;
            }
            case EAST -> {
                x += room.getWidth() - 1;
                z += room.getDepth() / 2;
            }
            case WEST -> {
                // x stays at room.getX()
                z += room.getDepth() / 2;
            }
        }
        
        return new int[]{x, y, z};
    }
    
    private String getRandomRoomType() {
        String[] types = {"chamber", "corridor", "treasure_room", "mini_boss_room"};
        return types[random.nextInt(types.length)];
    }
    
    private int getRoomWidth() { return 11 + random.nextInt(8); }
    private int getRoomHeight() { return 5 + random.nextInt(3); }
    private int getRoomDepth() { return 11 + random.nextInt(8); }
    
    private Material getFloorMaterial(String roomType) {
        return switch (roomType) {
            case "boss_room" -> Material.OBSIDIAN;
            case "treasure_room" -> Material.GOLD_BLOCK;
            default -> Material.STONE_BRICKS;
        };
    }
    
    private Material getWallMaterial(String roomType) {
        return switch (roomType) {
            case "boss_room" -> Material.COBBLESTONE;
            case "treasure_room" -> Material.CHISELED_STONE_BRICKS;
            default -> Material.STONE_BRICKS;
        };
    }
    
    private Material getCeilingMaterial(String roomType) {
        return switch (roomType) {
            case "boss_room" -> Material.COBBLESTONE;
            default -> Material.STONE_BRICKS;
        };
    }
    
    private void addRoomFeatures(@NotNull World world, @NotNull RoomPlacement room) {
        // Add room-specific features based on type
        switch (room.getType()) {
            case "treasure_room" -> addTreasureChests(world, room);
            case "boss_room" -> addBossArena(world, room);
        }
    }
    
    private void addTreasureChests(@NotNull World world, @NotNull RoomPlacement room) {
        int centerX = room.getX() + room.getWidth() / 2;
        int centerZ = room.getZ() + room.getDepth() / 2;
        int y = room.getY() + 1;
        
        world.getBlockAt(centerX, y, centerZ).setType(Material.CHEST);
    }
    
    private void addBossArena(@NotNull World world, @NotNull RoomPlacement room) {
        // Add pillars around the room
        int x1 = room.getX() + 3;
        int x2 = room.getX() + room.getWidth() - 4;
        int z1 = room.getZ() + 3;
        int z2 = room.getZ() + room.getDepth() - 4;
        
        for (int y = room.getY() + 1; y < room.getY() + room.getHeight() - 1; y++) {
            world.getBlockAt(x1, y, z1).setType(Material.COBBLESTONE);
            world.getBlockAt(x1, y, z2).setType(Material.COBBLESTONE);
            world.getBlockAt(x2, y, z1).setType(Material.COBBLESTONE);
            world.getBlockAt(x2, y, z2).setType(Material.COBBLESTONE);
        }
    }
    
    private void addDecorations(@NotNull World world, @NotNull RoomPlacement room) {
        // Add random decorative elements
        if (random.nextDouble() < 0.3) {
            int x = room.getX() + 1 + random.nextInt(room.getWidth() - 2);
            int z = room.getZ() + 1 + random.nextInt(room.getDepth() - 2);
            int y = room.getY() + 1;
            
            Material[] decorations = {Material.COBWEB, Material.SKELETON_SKULL, Material.FLOWER_POT};
            world.getBlockAt(x, y, z).setType(decorations[random.nextInt(decorations.length)]);
        }
    }
    
    // Enums and inner classes
    
    public enum Direction {
        NORTH, SOUTH, EAST, WEST;
        
        public Direction opposite() {
            return switch (this) {
                case NORTH -> SOUTH;
                case SOUTH -> NORTH;
                case EAST -> WEST;
                case WEST -> EAST;
            };
        }
    }
    
    public static class DungeonGenerationResult {
        private final boolean success;
        private final DungeonLayoutPlan layout;
        private final String message;
        
        public DungeonGenerationResult(boolean success, DungeonLayoutPlan layout, String message) {
            this.success = success;
            this.layout = layout;
            this.message = message;
        }
        
        public boolean isSuccess() { return success; }
        public DungeonLayoutPlan getLayout() { return layout; }
        public String getMessage() { return message; }
    }
    
    public static class DungeonLayoutPlan {
        private final List<RoomPlacement> rooms = new ArrayList<>();
        private final List<RoomConnection> connections = new ArrayList<>();
        
        public void addRoom(RoomPlacement room) { rooms.add(room); }
        public void addConnection(int room1Id, int room2Id, Direction direction) {
            connections.add(new RoomConnection(room1Id, room2Id, direction));
        }
        
        public List<RoomPlacement> getRooms() { return rooms; }
        public List<RoomConnection> getConnections() { return connections; }
        public RoomPlacement getRoom(int id) {
            return rooms.stream().filter(r -> r.getId() == id).findFirst().orElse(null);
        }
    }
    
    public static class RoomPlacement {
        private final int id;
        private final String type;
        private final int x, y, z;
        private final int width, height, depth;
        
        public RoomPlacement(int id, String type, int x, int y, int z, int width, int height, int depth) {
            this.id = id;
            this.type = type;
            this.x = x;
            this.y = y;
            this.z = z;
            this.width = width;
            this.height = height;
            this.depth = depth;
        }
        
        public int getId() { return id; }
        public String getType() { return type; }
        public int getX() { return x; }
        public int getY() { return y; }
        public int getZ() { return z; }
        public int getWidth() { return width; }
        public int getHeight() { return height; }
        public int getDepth() { return depth; }
    }
    
    public static class RoomConnection {
        private final int room1Id, room2Id;
        private final Direction direction;
        
        public RoomConnection(int room1Id, int room2Id, Direction direction) {
            this.room1Id = room1Id;
            this.room2Id = room2Id;
            this.direction = direction;
        }
        
        public int getRoom1Id() { return room1Id; }
        public int getRoom2Id() { return room2Id; }
        public Direction getDirection() { return direction; }
    }
}
