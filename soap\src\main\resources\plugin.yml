name: ${name}
version: ${version}
description: ${description}
main: com.ultimatedungeon.udx.bootstrap.UltimateDungeonX
api-version: ${apiVersion}
author: UltimateDungeon
website: https://github.com/ultimatedungeon/UltimateDungeonX

softdepend:
  - Vault
  - PlaceholderAPI

commands:
  udx:
    description: Opens the UltimateDungeonX main menu
    usage: /udx
    permission: udx.use
    aliases: [ultimatedungeon, dungeonx]
  
  udxadmin:
    description: Opens the UltimateDungeonX admin hub
    usage: /udxadmin
    permission: udx.admin
    aliases: [udx admin]
  
  udxeditor:
    description: Opens the UltimateDungeonX room/spawner/loot editors
    usage: /udxeditor
    permission: udx.editor
    aliases: [udx editor]
  
  udxportal:
    description: Gives a Portal Keystone item to place join portals
    usage: /udxportal
    permission: udx.admin.portal
    aliases: [udx portal]

permissions:
  udx.*:
    description: All UltimateDungeonX permissions
    children:
      udx.use: true
      udx.join: true
      udx.party: true
      udx.admin.*: true
      udx.editor.*: true
    default: op
  
  udx.use:
    description: Access to basic UltimateDungeonX features
    default: true
  
  udx.join:
    description: Join dungeon runs
    default: true
  
  udx.party:
    description: Create and manage parties
    default: true
  
  udx.admin.*:
    description: All administrative permissions
    children:
      udx.admin: true
      udx.admin.create: true
      udx.admin.delete: true
      udx.admin.manage: true
      udx.admin.teleport: true
      udx.admin.force: true
      udx.admin.portal: true
      udx.admin.debug: true
    default: op
  
  udx.admin:
    description: Access to admin hub
    default: op
  
  udx.admin.create:
    description: Create new dungeons and instances
    default: op
  
  udx.admin.delete:
    description: Delete dungeons and stop instances
    default: op
  
  udx.admin.manage:
    description: Manage existing dungeons and settings
    default: op
  
  udx.admin.teleport:
    description: Teleport to instances and build worlds
    default: op
  
  udx.admin.force:
    description: Force start/stop dungeon runs
    default: op
  
  udx.admin.portal:
    description: Create and manage portal keystones
    default: op
  
  udx.admin.debug:
    description: Access debug tools and performance monitoring
    default: op
  
  udx.editor.*:
    description: All editor permissions
    children:
      udx.editor.room: true
      udx.editor.spawner: true
      udx.editor.boss: true
      udx.editor.loot: true
    default: op
  
  udx.editor.room:
    description: Access to room editor
    default: op
  
  udx.editor.spawner:
    description: Access to spawner editor
    default: op
  
  udx.editor.boss:
    description: Access to boss editor
    default: op
  
  udx.editor.loot:
    description: Access to loot editor
    default: op
