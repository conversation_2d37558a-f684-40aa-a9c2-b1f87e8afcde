# Castle Tower - Tall defensive tower with spiral stairs
name: "castle_tower"
theme: "castle"
width: 9
height: 12
depth: 9

# Material palette
palette:
  0: "AIR"
  1: "STONE_BRICKS"
  2: "COBBLESTONE"
  3: "CHISELED_STONE_BRICKS"
  4: "STONE_BRICK_STAIRS"
  5: "TORCH"
  6: "IRON_BARS"
  7: "DARK_OAK_PLANKS"
  8: "DARK_OAK_LOG"
  9: "STONE_BRICK_SLAB"

# Layout by Y level (bottom to top)
layout:
  0: # Foundation
    - "111111111"
    - "111111111"
    - "111111111"
    - "111111111"
    - "111111111"
    - "111111111"
    - "111111111"
    - "111111111"
    - "111111111"
  1: # Ground floor
    - "111111111"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "111111111"
  2: # Floor 1 walls
    - "111111111"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "111111111"
  3: # Floor 1 ceiling
    - "111111111"
    - "177777771"
    - "177777771"
    - "177777771"
    - "177777771"
    - "177777771"
    - "177777771"
    - "177777771"
    - "111111111"
  4: # Floor 2
    - "111111111"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "111111111"
  5: # Floor 2 walls
    - "111111111"
    - "150000051"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "150000051"
    - "111111111"
  6: # Floor 2 ceiling
    - "111111111"
    - "177777771"
    - "177777771"
    - "177777771"
    - "177777771"
    - "177777771"
    - "177777771"
    - "177777771"
    - "111111111"
  7: # Floor 3
    - "111111111"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "111111111"
  8: # Floor 3 walls
    - "111111111"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "111111111"
  9: # Top floor
    - "111111111"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "111111111"
  10: # Battlements base
    - "111111111"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "100000001"
    - "111111111"
  11: # Battlements
    - "101010101"
    - "000000000"
    - "000000000"
    - "000000000"
    - "000000000"
    - "000000000"
    - "000000000"
    - "000000000"
    - "101010101"
