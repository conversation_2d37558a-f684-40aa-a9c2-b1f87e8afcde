package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * Builder tools menu for dungeon creation and configuration.
 * 
 * <p>This menu provides access to placement wands, spawner tools,
 * and other utilities for building dungeons.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class BuilderToolsMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public BuilderToolsMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Builder Tools").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Header
        ItemStack headerItem = new ItemBuilder(Material.GOLDEN_PICKAXE)
            .name(Component.text("Dungeon Builder Tools").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Essential tools for dungeon creation").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Place spawners, configure mobs,").color(NamedTextColor.YELLOW),
                Component.text("and set up gameplay mechanics").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(4, headerItem, clickType -> {
            // No action for header
        });
        
        // Placement Tools
        setupPlacementTools();
        
        // Configuration Tools
        setupConfigurationTools();
        
        // Utility Tools
        setupUtilityTools();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Close Tools").color(NamedTextColor.RED))
            .lore(Component.text("Close the builder tools menu").color(NamedTextColor.GRAY))
            .build();
        
        setItem(49, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                close();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupPlacementTools() {
        // Spawner Wand
        ItemStack spawnerWand = new ItemBuilder(Material.BLAZE_ROD)
            .name(Component.text("Spawner Wand").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Place and configure mob spawners").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Left-click: Place spawner").color(NamedTextColor.GREEN),
                Component.text("Right-click: Configure spawner").color(NamedTextColor.YELLOW),
                Component.text("Shift-click: Remove spawner").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("Click to get spawner wand").color(NamedTextColor.AQUA)
            )
            .glow(true)
            .build();
        
        setItem(10, spawnerWand, clickType -> {
            if (clickType == ClickType.LEFT) {
                giveSpawnerWand();
            }
        });
        
        // Mob Selection Wand
        ItemStack mobWand = new ItemBuilder(Material.ZOMBIE_HEAD)
            .name(Component.text("Mob Selection Wand").color(NamedTextColor.DARK_RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Select mob types for spawners").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Right-click: Open mob selector").color(NamedTextColor.YELLOW),
                Component.text("Left-click spawner: Set mob type").color(NamedTextColor.GREEN),
                Component.empty(),
                Component.text("Current mob: ZOMBIE").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to get mob wand").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(12, mobWand, clickType -> {
            if (clickType == ClickType.LEFT) {
                giveMobSelectionWand();
            }
        });
        
        // Chest Placement Wand
        ItemStack chestWand = new ItemBuilder(Material.CHEST)
            .name(Component.text("Chest Placement Wand").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Place loot chests and configure rewards").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Left-click: Place chest").color(NamedTextColor.GREEN),
                Component.text("Right-click: Configure loot table").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to get chest wand").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(14, chestWand, clickType -> {
            if (clickType == ClickType.LEFT) {
                giveChestWand();
            }
        });
        
        // Gate/Trigger Wand
        ItemStack gateWand = new ItemBuilder(Material.IRON_BARS)
            .name(Component.text("Gate & Trigger Wand").color(NamedTextColor.GRAY).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Place gates, doors, and triggers").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Left-click: Place gate/door").color(NamedTextColor.GREEN),
                Component.text("Right-click: Place trigger zone").color(NamedTextColor.YELLOW),
                Component.text("Shift-click: Configure behavior").color(NamedTextColor.AQUA),
                Component.empty(),
                Component.text("Click to get gate wand").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(16, gateWand, clickType -> {
            if (clickType == ClickType.LEFT) {
                giveGateWand();
            }
        });
    }
    
    private void setupConfigurationTools() {
        // Spawner Editor
        ItemStack spawnerEditor = new ItemBuilder(Material.SPAWNER)
            .name(Component.text("Spawner Editor").color(NamedTextColor.RED))
            .lore(
                Component.text("Advanced spawner configuration").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Configure waves, behaviors,").color(NamedTextColor.YELLOW),
                Component.text("and mob abilities").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to open editor").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(28, spawnerEditor, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMenu(player, new SpawnerEditorMenu(plugin, player));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Boss Editor
        ItemStack bossEditor = new ItemBuilder(Material.WITHER_SKELETON_SKULL)
            .name(Component.text("Boss Editor").color(NamedTextColor.DARK_PURPLE))
            .lore(
                Component.text("Create and configure boss encounters").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Design multi-phase bosses").color(NamedTextColor.YELLOW),
                Component.text("with special abilities").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to open editor").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(30, bossEditor, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMenu(player, new BossEditorMenu(plugin, player));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Loot Editor
        ItemStack lootEditor = new ItemBuilder(Material.DIAMOND)
            .name(Component.text("Loot Editor").color(NamedTextColor.AQUA))
            .lore(
                Component.text("Configure loot tables and rewards").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Create balanced reward systems").color(NamedTextColor.YELLOW),
                Component.text("with rare and common items").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to open editor").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(32, lootEditor, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMenu(player, new LootEditorMenu(plugin, player));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
    }
    
    private void setupUtilityTools() {
        // Selection Wand
        ItemStack selectionWand = new ItemBuilder(Material.GOLDEN_AXE)
            .name(Component.text("Selection Wand").color(NamedTextColor.GOLD))
            .lore(
                Component.text("WorldEdit-style area selection").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Left-click: Set position 1").color(NamedTextColor.GREEN),
                Component.text("Right-click: Set position 2").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to get selection wand").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(37, selectionWand, clickType -> {
            if (clickType == ClickType.LEFT) {
                giveSelectionWand();
            }
        });
        
        // Info Tool
        ItemStack infoTool = new ItemBuilder(Material.COMPASS)
            .name(Component.text("Info Tool").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Get information about blocks and entities").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Right-click: Get block/entity info").color(NamedTextColor.YELLOW),
                Component.text("Shows spawner configs, loot tables, etc.").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to get info tool").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(39, infoTool, clickType -> {
            if (clickType == ClickType.LEFT) {
                giveInfoTool();
            }
        });
        
        // Clear Tools
        ItemStack clearTools = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Clear All Tools").color(NamedTextColor.RED))
            .lore(
                Component.text("Remove all builder tools from inventory").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will clear all UDX tools").color(NamedTextColor.YELLOW),
                Component.text("from your inventory").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to clear tools").color(NamedTextColor.RED)
            )
            .build();
        
        setItem(41, clearTools, clickType -> {
            if (clickType == ClickType.LEFT) {
                clearAllTools();
            }
        });
    }
    
    private void giveSpawnerWand() {
        ItemStack wand = new ItemBuilder(Material.BLAZE_ROD)
            .name(Component.text("UDX Spawner Wand").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(List.of(
                Component.text("Left-click: Place spawner").color(NamedTextColor.GREEN),
                Component.text("Right-click: Configure spawner").color(NamedTextColor.YELLOW),
                Component.text("Shift-click: Remove spawner").color(NamedTextColor.RED)
            ))
            .glow(true)
            .build();
        
        player.getInventory().addItem(wand);
        player.sendMessage(Component.text("Spawner wand given! Use it to place and configure mob spawners.").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
    }
    
    private void giveMobSelectionWand() {
        ItemStack wand = new ItemBuilder(Material.ZOMBIE_HEAD)
            .name(Component.text("UDX Mob Selection Wand").color(NamedTextColor.DARK_RED).decoration(TextDecoration.BOLD, true))
            .lore(List.of(
                Component.text("Right-click: Open mob selector").color(NamedTextColor.YELLOW),
                Component.text("Left-click spawner: Set mob type").color(NamedTextColor.GREEN),
                Component.empty(),
                Component.text("Current mob: ZOMBIE").color(NamedTextColor.YELLOW)
            ))
            .build();
        
        player.getInventory().addItem(wand);
        player.sendMessage(Component.text("Mob selection wand given! Right-click to select mob types.").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
    }
    
    private void giveChestWand() {
        ItemStack wand = new ItemBuilder(Material.CHEST)
            .name(Component.text("UDX Chest Wand").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(List.of(
                Component.text("Left-click: Place chest").color(NamedTextColor.GREEN),
                Component.text("Right-click: Configure loot table").color(NamedTextColor.YELLOW)
            ))
            .build();
        
        player.getInventory().addItem(wand);
        player.sendMessage(Component.text("Chest wand given! Use it to place loot chests.").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
    }
    
    private void giveGateWand() {
        ItemStack wand = new ItemBuilder(Material.IRON_BARS)
            .name(Component.text("UDX Gate & Trigger Wand").color(NamedTextColor.GRAY).decoration(TextDecoration.BOLD, true))
            .lore(List.of(
                Component.text("Left-click: Place gate/door").color(NamedTextColor.GREEN),
                Component.text("Right-click: Place trigger zone").color(NamedTextColor.YELLOW),
                Component.text("Shift-click: Configure behavior").color(NamedTextColor.AQUA)
            ))
            .build();
        
        player.getInventory().addItem(wand);
        player.sendMessage(Component.text("Gate & trigger wand given! Use it to place interactive elements.").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
    }
    
    private void giveSelectionWand() {
        ItemStack wand = new ItemBuilder(Material.GOLDEN_AXE)
            .name(Component.text("UDX Selection Wand").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(List.of(
                Component.text("Left-click: Set position 1").color(NamedTextColor.GREEN),
                Component.text("Right-click: Set position 2").color(NamedTextColor.YELLOW)
            ))
            .build();
        
        player.getInventory().addItem(wand);
        player.sendMessage(Component.text("Selection wand given! Use it to select areas.").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
    }
    
    private void giveInfoTool() {
        ItemStack tool = new ItemBuilder(Material.COMPASS)
            .name(Component.text("UDX Info Tool").color(NamedTextColor.BLUE).decoration(TextDecoration.BOLD, true))
            .lore(List.of(
                Component.text("Right-click: Get block/entity info").color(NamedTextColor.YELLOW),
                Component.text("Shows spawner configs, loot tables, etc.").color(NamedTextColor.GRAY)
            ))
            .build();
        
        player.getInventory().addItem(tool);
        player.sendMessage(Component.text("Info tool given! Right-click blocks to get information.").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
    }
    
    private void clearAllTools() {
        int removed = 0;
        for (int i = 0; i < player.getInventory().getSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                Component displayName = item.getItemMeta().displayName();
                if (displayName != null) {
                    String name = ((net.kyori.adventure.text.TextComponent) displayName).content();
                    if (name.startsWith("UDX ")) {
                        player.getInventory().setItem(i, null);
                        removed++;
                    }
                }
            }
        }
        
        if (removed > 0) {
            player.sendMessage(Component.text("Removed " + removed + " UDX tools from your inventory.").color(NamedTextColor.GREEN));
            player.playSound(player.getLocation(), Sound.ENTITY_ITEM_BREAK, 1.0f, 1.0f);
        } else {
            player.sendMessage(Component.text("No UDX tools found in your inventory.").color(NamedTextColor.YELLOW));
        }
    }
}
