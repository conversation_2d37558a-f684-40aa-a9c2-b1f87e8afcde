package com.ultimatedungeon.udx.gui.menus;

import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Material;
import org.jetbrains.annotations.NotNull;

/**
 * Represents information about a dungeon.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class DungeonInfo {
    private final String name;
    private final String description;
    private final Material icon;
    private final Difficulty difficulty;
    private final int minPlayers;
    private final int maxPlayers;
    
    public DungeonInfo(@NotNull String name, @NotNull String description, @NotNull Material icon, 
                      @NotNull Difficulty difficulty, int minPlayers, int maxPlayers) {
        this.name = name;
        this.description = description;
        this.icon = icon;
        this.difficulty = difficulty;
        this.minPlayers = minPlayers;
        this.maxPlayers = maxPlayers;
    }
    
    public String getName() { return name; }
    public String getDescription() { return description; }
    public Material getIcon() { return icon; }
    public Difficulty getDifficulty() { return difficulty; }
    public int getMinPlayers() { return minPlayers; }
    public int getMaxPlayers() { return maxPlayers; }
    
    public enum Difficulty {
        EASY("Easy", NamedTextColor.GREEN),
        NORMAL("Normal", NamedTextColor.YELLOW),
        HARD("Hard", NamedTextColor.RED),
        EXPERT("Expert", NamedTextColor.DARK_PURPLE);
        
        private final String displayName;
        private final NamedTextColor color;
        
        Difficulty(String displayName, NamedTextColor color) {
            this.displayName = displayName;
            this.color = color;
        }
        
        public String getDisplayName() { return displayName; }
        public NamedTextColor getColor() { return color; }
    }
}
