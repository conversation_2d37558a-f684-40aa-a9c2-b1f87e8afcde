package com.ultimatedungeon.udx.util;

import org.bukkit.Bukkit;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitScheduler;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Supplier;

/**
 * Utility class for managing scheduled tasks with Paper's modern scheduler.
 * 
 * <p>This class provides a clean API for scheduling tasks on both the main thread
 * and async threads, with proper cleanup and error handling.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class SchedulerUtil {
    
    private final Plugin plugin;
    private final BukkitScheduler scheduler;
    private final ConcurrentMap<Integer, BukkitTask> activeTasks;
    
    public SchedulerUtil(@NotNull Plugin plugin) {
        this.plugin = plugin;
        this.scheduler = Bukkit.getScheduler();
        this.activeTasks = new ConcurrentHashMap<>();
    }
    
    /**
     * Runs a task on the main thread.
     * 
     * @param task the task to run
     * @return the scheduled task
     */
    @NotNull
    public BukkitTask runTask(@NotNull Runnable task) {
        BukkitTask bukkitTask = scheduler.runTask(plugin, () -> {
            try {
                task.run();
            } catch (Exception e) {
                plugin.getLogger().severe("Error in scheduled task: " + e.getMessage());
                e.printStackTrace();
            }
        });
        activeTasks.put(bukkitTask.getTaskId(), bukkitTask);
        return bukkitTask;
    }
    
    /**
     * Runs a task on the main thread after a delay.
     * 
     * @param task the task to run
     * @param delay the delay in ticks
     * @return the scheduled task
     */
    @NotNull
    public BukkitTask runTaskLater(@NotNull Runnable task, long delay) {
        BukkitTask bukkitTask = scheduler.runTaskLater(plugin, () -> {
            try {
                task.run();
            } catch (Exception e) {
                plugin.getLogger().severe("Error in delayed task: " + e.getMessage());
                e.printStackTrace();
            }
        }, delay);
        activeTasks.put(bukkitTask.getTaskId(), bukkitTask);
        return bukkitTask;
    }
    
    /**
     * Runs a repeating task on the main thread.
     * 
     * @param task the task to run
     * @param delay the initial delay in ticks
     * @param period the period between executions in ticks
     * @return the scheduled task
     */
    @NotNull
    public BukkitTask runTaskTimer(@NotNull Runnable task, long delay, long period) {
        BukkitTask bukkitTask = scheduler.runTaskTimer(plugin, () -> {
            try {
                task.run();
            } catch (Exception e) {
                plugin.getLogger().severe("Error in timer task: " + e.getMessage());
                e.printStackTrace();
            }
        }, delay, period);
        activeTasks.put(bukkitTask.getTaskId(), bukkitTask);
        return bukkitTask;
    }
    
    /**
     * Runs a task asynchronously.
     * 
     * @param task the task to run
     * @return the scheduled task
     */
    @NotNull
    public BukkitTask runTaskAsynchronously(@NotNull Runnable task) {
        BukkitTask bukkitTask = scheduler.runTaskAsynchronously(plugin, () -> {
            try {
                task.run();
            } catch (Exception e) {
                plugin.getLogger().severe("Error in async task: " + e.getMessage());
                e.printStackTrace();
            }
        });
        activeTasks.put(bukkitTask.getTaskId(), bukkitTask);
        return bukkitTask;
    }
    
    /**
     * Runs a task asynchronously after a delay.
     * 
     * @param task the task to run
     * @param delay the delay in ticks
     * @return the scheduled task
     */
    @NotNull
    public BukkitTask runTaskLaterAsynchronously(@NotNull Runnable task, long delay) {
        BukkitTask bukkitTask = scheduler.runTaskLaterAsynchronously(plugin, () -> {
            try {
                task.run();
            } catch (Exception e) {
                plugin.getLogger().severe("Error in delayed async task: " + e.getMessage());
                e.printStackTrace();
            }
        }, delay);
        activeTasks.put(bukkitTask.getTaskId(), bukkitTask);
        return bukkitTask;
    }
    
    /**
     * Runs a repeating task asynchronously.
     * 
     * @param task the task to run
     * @param delay the initial delay in ticks
     * @param period the period between executions in ticks
     * @return the scheduled task
     */
    @NotNull
    public BukkitTask runTaskTimerAsynchronously(@NotNull Runnable task, long delay, long period) {
        BukkitTask bukkitTask = scheduler.runTaskTimerAsynchronously(plugin, () -> {
            try {
                task.run();
            } catch (Exception e) {
                plugin.getLogger().severe("Error in async timer task: " + e.getMessage());
                e.printStackTrace();
            }
        }, delay, period);
        activeTasks.put(bukkitTask.getTaskId(), bukkitTask);
        return bukkitTask;
    }
    
    /**
     * Runs a supplier asynchronously and returns a CompletableFuture.
     * 
     * @param supplier the supplier to run
     * @param <T> the return type
     * @return a CompletableFuture with the result
     */
    @NotNull
    public <T> CompletableFuture<T> supplyAsync(@NotNull Supplier<T> supplier) {
        CompletableFuture<T> future = new CompletableFuture<>();
        
        runTaskAsynchronously(() -> {
            try {
                T result = supplier.get();
                runTask(() -> future.complete(result));
            } catch (Exception e) {
                runTask(() -> future.completeExceptionally(e));
            }
        });
        
        return future;
    }
    
    /**
     * Runs a task asynchronously and then runs a callback on the main thread.
     * 
     * @param asyncTask the task to run asynchronously
     * @param callback the callback to run on the main thread
     */
    public void runAsyncThenSync(@NotNull Runnable asyncTask, @NotNull Runnable callback) {
        runTaskAsynchronously(() -> {
            try {
                asyncTask.run();
                runTask(callback);
            } catch (Exception e) {
                plugin.getLogger().severe("Error in async-then-sync task: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    /**
     * Cancels a task by its ID.
     * 
     * @param taskId the task ID to cancel
     */
    public void cancelTask(int taskId) {
        BukkitTask task = activeTasks.remove(taskId);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }
    }
    
    /**
     * Cancels a task.
     * 
     * @param task the task to cancel
     */
    public void cancelTask(@NotNull BukkitTask task) {
        activeTasks.remove(task.getTaskId());
        if (!task.isCancelled()) {
            task.cancel();
        }
    }
    
    /**
     * Gets the number of active tasks.
     * 
     * @return the number of active tasks
     */
    public int getActiveTaskCount() {
        return activeTasks.size();
    }
    
    /**
     * Checks if the current thread is the main server thread.
     * 
     * @return true if on main thread, false otherwise
     */
    public boolean isMainThread() {
        return Bukkit.isPrimaryThread();
    }
    
    /**
     * Ensures a task runs on the main thread.
     * If already on main thread, runs immediately.
     * Otherwise, schedules for next tick.
     * 
     * @param task the task to run
     */
    public void ensureMainThread(@NotNull Runnable task) {
        if (isMainThread()) {
            task.run();
        } else {
            runTask(task);
        }
    }
    
    /**
     * Shuts down the scheduler and cancels all active tasks.
     */
    public void shutdown() {
        plugin.getLogger().info("Shutting down scheduler, cancelling " + activeTasks.size() + " active tasks");
        
        for (BukkitTask task : activeTasks.values()) {
            if (!task.isCancelled()) {
                task.cancel();
            }
        }
        activeTasks.clear();
    }
}
