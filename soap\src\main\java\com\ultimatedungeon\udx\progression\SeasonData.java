package com.ultimatedungeon.udx.progression;

import org.jetbrains.annotations.NotNull;

import java.time.Instant;
import java.util.Objects;

/**
 * Represents a player's data for a completed season.
 * 
 * <p>This class stores the final results of a season including
 * score, rank, and completion timestamp.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class SeasonData {
    
    private final int seasonId;
    private final int finalScore;
    private final int finalRank;
    private final Instant completedAt;
    
    public SeasonData(int seasonId, int finalScore, int finalRank, @NotNull Instant completedAt) {
        this.seasonId = seasonId;
        this.finalScore = finalScore;
        this.finalRank = finalRank;
        this.completedAt = completedAt;
    }
    
    // Getters
    
    public int getSeasonId() {
        return seasonId;
    }
    
    public int getFinalScore() {
        return finalScore;
    }
    
    public int getFinalRank() {
        return finalRank;
    }
    
    @NotNull
    public Instant getCompletedAt() {
        return completedAt;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SeasonData that = (SeasonData) obj;
        return seasonId == that.seasonId;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(seasonId);
    }
    
    @Override
    public String toString() {
        return "SeasonData{" +
            "seasonId=" + seasonId +
            ", finalScore=" + finalScore +
            ", finalRank=" + finalRank +
            ", completedAt=" + completedAt +
            '}';
    }
}
